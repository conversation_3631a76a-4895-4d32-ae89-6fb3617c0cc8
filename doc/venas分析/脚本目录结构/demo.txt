covid19
python3 scripts/venas2.py -input_name covid19_example
python3 scripts/venas2.py -input_name covid19_example -batch_mode True -initial_size 20000 -batch_size 10000
python3 scripts/venas2_addnew.py -venas2_output_path venas2_output_covid19_example -input_name covid19_example_new

mtb
python3 scripts/venas2_mtb.py -input_name mtb_example
python3 scripts/venas2_mtb.py -input_name mtb_example -batch_mode True -initial_size 200 -batch_size 100
python3 scripts/venas2_addnew_mtb.py -venas2_output_path venas2_output_mtb_example -input_name mtb_example_newvenas2_addnew_mtb

