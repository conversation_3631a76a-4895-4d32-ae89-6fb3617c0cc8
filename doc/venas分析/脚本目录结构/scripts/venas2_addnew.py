#!/usr/bin/env python
# coding=utf-8
# __author__ = 'zp'

import pandas as pd
import numpy as np
from datetime import datetime
from datetime import timedelta
import time
import argparse
import os
import faiss
import random

beg_time = time.time()
parser = argparse.ArgumentParser()
parser.add_argument('-venas2_output_path', '--venas2_output_path', dest='venas2_output_path', help="the output path of the venas2 results, which contain the existing network information", required=True)
parser.add_argument('-input_name', '--input_name', dest='input_name', help="the name of input samples mutation file", required=True)
# # parser.add_argument('-output_path', '--output_path', dest='output_path', help="the output path of the results", required=True)
# # parser.add_argument('-batch_mode', '--batch_mode', dest='batch_mode', help="the number of input samples", required=True)
# # parser.add_argument('-base_num', '--base_num', dest='base_num', help="the parameter is used when the batch_mode is True to define the number of samples for original network", required=True)
# # parser.add_argument('-batch_num', '--batch_num', dest='batch_num', help="the parameter is used when the batch_mode is True to define the number of samples for each batch", required=True)
args = parser.parse_args()

#We strongly suggest only when the samples count higher than 100000 use the batchwise mode
#parameters
input_name = args.input_name
data_path = args.venas2_output_path + '/'
# input_name = 'covid19_example1_new'
# data_path = 'venas2_output_covid19_example1/'

##load in the data
dict_nc2num = {'a':1, 't':2, 'c':3, 'g':4, '-':5, 'o':6}
dict_num2nc = {1:'a', 2:'t', 3:'c', 4:'g', 5:'-', 6:'o'}
can_lens = 200
rawdata_path = 'rawdata/' 
output_path = 'venas2_output_'+input_name+'/'
if os.path.exists(output_path):
    print('the folder exists! please check the output dir')
else:
    os.makedirs(output_path)

command = 'cp ' + data_path + 'hap_array_df.txt ' + output_path
os.system(command)

newdata = pd.read_csv(rawdata_path + input_name +  '.txt', sep='\t', header=0)
newdata = newdata.fillna('')
if newdata.shape[1] == 2:
    meta_flag = False
elif newdata.shape[1] == 4:
    meta_flag = True
else:
    print('File format warning!!! Please check the files.')

def convert_smaples_mutation_into_hap_df_array(data_fn):
    # data_fn = data_base
    haplotype_df_fn = []
    hap_pos_fn = []
    for each_haplotype, group in data_fn.groupby('haplotype'):
        # print(each_haplotype, group)
        sample_names = group['samplename'].tolist()
        sample_count = len(sample_names)
        sample_locations = ';'.join(group['location'].drop_duplicates().tolist())
        sample_times = group['time'].drop_duplicates().tolist()
        # sample_times = [ datetime.strptime(x, '%Y-%m-%d') for x in sample_times]
        sample_times_min = min(sample_times)
        sample_times_max = max(sample_times)
        if each_haplotype == '':
            muts_count = 0
            haplotype_df_fn.append([each_haplotype, muts_count, ';'.join(sample_names), sample_count, sample_locations, sample_times_min, sample_times_max, '','' ])
        else:
            each_haps = each_haplotype.split(';')
            muts_count = len(each_haps)
            hap_pos = []
            hap_value = []
            for each_cur_hap in each_haps:
                each_cur_hapidx = int(each_cur_hap.split('(')[0])
                each_cur_hapletter = each_cur_hap[-2:-1].lower()
                each_value = dict_nc2num[each_cur_hapletter]
                hap_pos.append(each_cur_hapidx)
                hap_value.append(each_value)
            haplotype_df_fn.append([each_haplotype, muts_count, ';'.join(sample_names), sample_count, sample_locations, sample_times_min, sample_times_max, hap_pos, hap_value])
            hap_pos_fn += hap_pos
    haplotype_df_fn = pd.DataFrame(haplotype_df_fn)
    haplotype_df_fn.columns = ['haplotype', 'muts_count', 'sample_names', 'sample_count',  'sample_locations', 'sample_time_min', 'sample_time_max', 'hap_pos', 'hap_value']
    haplotype_df_fn = haplotype_df_fn.sort_values([ 'sample_time_min', 'muts_count','sample_count'], ascending=[True, True, False])
    # haplotype_df = pd.concat( [haplotype_df_first, haplotype_df_rest], axis=0 )
    haplotype_df_fn.index= list(range(haplotype_df_fn.shape[0]))
    hap_count_fn = haplotype_df_fn.shape[0]
    hap_pos_fn = list(sorted(set(hap_pos_fn)))
    ##convert to array
    hap_array_df = pd.DataFrame(0, index = range(hap_count_fn), columns=hap_pos_fn, dtype='int8')
    for each_hapidx in range(hap_count_fn):
        # each_hapidx = 4
        cur_hap_pos = haplotype_df_fn.loc[each_hapidx]['hap_pos']
        cur_hap_value = haplotype_df_fn.loc[each_hapidx]['hap_value']
        if cur_hap_pos == '':
            continue
        else: 
            hap_array_df.loc[each_hapidx, cur_hap_pos] = cur_hap_value
    hap_array = np.array(hap_array_df, dtype='int8')
    return haplotype_df_fn, hap_array, hap_array_df, hap_pos_fn

def find_parent(tmpidx):
    tmpidx2 = edges_df[edges_df[1]==tmpidx].iloc[0][0]
    if tmpidx2 == 0:
        return tmpidx2
    else:
        tmp_hap_pos = haplotype_df_updated.loc[tmpidx2]['hap_pos']
        if set(tmp_hap_pos) < set(cur_hap_pos):
            return tmpidx2            
        else:
            return find_parent(tmpidx2)

def convert_smaples_mutation_into_hap_df_array_nometa(data_fn):
    # data_fn = data_base
    haplotype_df_fn = []
    hap_pos_fn = []
    for each_haplotype, group in data_fn.groupby('haplotype'):
        # print(each_haplotype, group)
        sample_names = group['samplename'].tolist()
        sample_count = len(sample_names)
        # sample_locations = ';'.join(group['location'].drop_duplicates().tolist())
        # sample_times = group['time'].drop_duplicates().tolist()
        # sample_times = [ datetime.strptime(x, '%Y-%m-%d') for x in sample_times]
        # sample_times_min = min(sample_times)
        # sample_times_max = max(sample_times)
        if each_haplotype == '':
            muts_count = 0
            haplotype_df_fn.append([each_haplotype, muts_count, ';'.join(sample_names), sample_count, '','' ])
        else:
            each_haps = each_haplotype.split(';')
            muts_count = len(each_haps)
            hap_pos = []
            hap_value = []
            for each_cur_hap in each_haps:
                each_cur_hapidx = int(each_cur_hap.split('(')[0])
                each_cur_hapletter = each_cur_hap[-2:-1].lower()
                each_value = dict_nc2num[each_cur_hapletter]
                hap_pos.append(each_cur_hapidx)
                hap_value.append(each_value)
            haplotype_df_fn.append([each_haplotype, muts_count, ';'.join(sample_names), sample_count, hap_pos, hap_value])
            hap_pos_fn += hap_pos
    haplotype_df_fn = pd.DataFrame(haplotype_df_fn)
    haplotype_df_fn.columns = ['haplotype', 'muts_count', 'sample_names', 'sample_count',  'hap_pos', 'hap_value']
    haplotype_df_fn = haplotype_df_fn.sort_values([ 'muts_count','sample_count'], ascending=[ True, False])
    # haplotype_df = pd.concat( [haplotype_df_first, haplotype_df_rest], axis=0 )
    haplotype_df_fn.index= list(range(haplotype_df_fn.shape[0]))
    hap_count_fn = haplotype_df_fn.shape[0]
    hap_pos_fn = list(sorted(set(hap_pos_fn)))
    ##convert to array
    hap_array_df = pd.DataFrame(0, index = range(hap_count_fn), columns=hap_pos_fn, dtype='int8')
    for each_hapidx in range(hap_count_fn):
        # each_hapidx = 4
        cur_hap_pos = haplotype_df_fn.loc[each_hapidx]['hap_pos']
        cur_hap_value = haplotype_df_fn.loc[each_hapidx]['hap_value']
        if cur_hap_pos == '':
            continue
        else: 
            hap_array_df.loc[each_hapidx, cur_hap_pos] = cur_hap_value
    hap_array = np.array(hap_array_df, dtype='int8')
    return haplotype_df_fn, hap_array, hap_array_df, hap_pos_fn


nodes_df_existing = pd.read_csv(data_path + 'nodes.csv', sep=',', header=0)
edges_df_existing = pd.read_csv(data_path + 'edges.csv', sep=',', header=0)

edges_df = pd.DataFrame(columns=[0, 1,2,3])
data_batch = newdata
if data_batch.shape[0] == 0:
    print('No data! please check the new data!')

if meta_flag == True:
    newdata.columns = ['samplename', 'haplotype' , 'collection_date', 'location']
    newdata['time'] = newdata['collection_date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d'))

    haplotype_df_existing = pd.read_csv(data_path + 'haplotype_final.txt', sep='\t', header=0)
    haplotype_df_existing = haplotype_df_existing.fillna('')
    haplotype_df_existing['sample_time_min'] = haplotype_df_existing['sample_time_min'].apply(lambda x:  datetime.strptime(x, '%Y-%m-%d'))
    haplotype_df_existing['sample_time_max'] = haplotype_df_existing['sample_time_max'].apply(lambda x:  datetime.strptime(x, '%Y-%m-%d'))
    hap_count_existing = haplotype_df_existing.shape[0]

    hap_array_df_existing = pd.read_csv(data_path + 'hap_array_df.txt', sep='\t', header=0)
    hap_pos_existing = [ int(x) for x in hap_array_df_existing.columns.tolist()]
    hap_array_df_existing.columns = hap_pos_existing
    # edges_list_existing = edges_df_existing.values.tolist()
    haplotype_df_updated = haplotype_df_existing.copy()
    hap_array_df_updated = hap_array_df_existing.copy()
    hap_pos_updated = hap_pos_existing

    """step1: get the haplotype vector form and update the latest hap"""
    haplotype_df_batch, hap_array_batch, hap_array_df_batch, hap_pos_batch = convert_smaples_mutation_into_hap_df_array(data_batch)
    #merge the existing haps into haplotype_df_update and change it self
    df1 = pd.DataFrame()
    df1['hapid'] = haplotype_df_updated.index
    df1['haplotype'] = haplotype_df_updated['haplotype']
    df2 = pd.DataFrame()
    df2['hapid'] = haplotype_df_batch.index
    df2['haplotype'] = haplotype_df_batch['haplotype']
    haplotype_df_merge = pd.merge(df1, df2, how='inner', on='haplotype')
    if haplotype_df_merge.shape[0] == 0: #there is no samples in existing haps
        pass
    else:
        for each_hap_info in haplotype_df_merge.values:
            hapid_x, haplotype, hapid_y = each_hap_info
            haplotype_df_updated.loc[hapid_x, 'sample_names'] += ';' + haplotype_df_batch.loc[hapid_y, 'sample_names']
            haplotype_df_updated.loc[hapid_x, 'sample_count'] += haplotype_df_batch.loc[hapid_y, 'sample_count']
            haplotype_df_updated.loc[hapid_x, 'sample_locations'] = ';'.join(set(haplotype_df_updated.loc[hapid_x, 'sample_locations'].split(';')+ haplotype_df_batch.loc[hapid_y, 'sample_locations'].split(';')))
            haplotype_df_updated.loc[hapid_x, 'sample_time_min'] = min( haplotype_df_updated.loc[hapid_x, 'sample_time_min'], haplotype_df_batch.loc[hapid_y, 'sample_time_min'])
            haplotype_df_updated.loc[hapid_x, 'sample_time_max'] = max( haplotype_df_updated.loc[hapid_x, 'sample_time_max'], haplotype_df_batch.loc[hapid_y, 'sample_time_max'])
        haplotype_removed_indexs = [x for x in haplotype_df_batch.index.tolist() if x not in haplotype_df_merge['hapid_y'].tolist()]
        haplotype_df_batch = haplotype_df_batch.loc[haplotype_removed_indexs]
        hap_array_df_batch = hap_array_df_batch.loc[haplotype_removed_indexs]
        hap_array_batch = np.array(hap_array_df_batch)

    hap_count_updated = haplotype_df_updated.shape[0]
    hap_count_batch = haplotype_df_batch.shape[0]
    haplotype_df_batch.index = range(hap_count_updated, hap_count_updated + hap_count_batch )
    hap_array_df_batch.index = range(hap_count_updated, hap_count_updated + hap_count_batch)
    haplotype_df_updated = pd.concat([ haplotype_df_updated, haplotype_df_batch ], axis=0)

    batch_flag = True
    # if hap_count_batch == 0:
    #     print('The new haplotypes have all appeared in existing haplotypes.')
    #     # break
    if len(hap_pos_updated) == 0:
        hap_array_df_updated = pd.concat([hap_array_df_updated, hap_array_df_batch],axis=0)
        hap_array_df_index = hap_array_df_updated
        hap_array_index = np.array(hap_array_df_updated)
        hap_pos_updated = hap_pos_batch
        hap_array_df_updated.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
    else:
        hap_pos_df = pd.merge(pd.DataFrame(hap_pos_updated).reset_index(), pd.DataFrame(hap_pos_batch).reset_index(), how='outer', on=0 )
        hap_pos_df_new = hap_pos_df[hap_pos_df['index_x'].isna() ]
        hap_pos_df_existing = hap_pos_df[hap_pos_df['index_y'].isna() ]
        hap_pos_df_overlap = hap_pos_df[(hap_pos_df['index_x'].notna()) & (hap_pos_df['index_y'].notna()) ]
        if hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] == 0:
            batch_flag = False
        elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] != 0:
            batch_flag = False
        elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] == 0:
            batch_flag = False
        elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] == 0:
            hap_pos_overlap = hap_pos_df_overlap[0].tolist()
            hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
            hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
            hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
            hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
            hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap ]
            hap_array_df_index = pd.concat([ hap_array_df_overlap, hap_array_df_batch ], axis=0)
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            hap_pos_updated = hap_pos_overlap
        elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] != 0:
            hap_pos_existing = hap_pos_df_existing[0].tolist()
            hap_pos_new = hap_pos_df_new[0].tolist()
            hap_pos_updated = hap_pos_existing + hap_pos_new
            hap_array_df_up = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
            hap_array_df_up = hap_array_df_up.fillna(0).astype('int8') 
            hap_array_df_up.columns = [ int(x) for x in hap_array_df_up.columns ]
            hap_array_df_up = pd.concat([hap_array_df_up[hap_pos_existing], pd.DataFrame(0, index=hap_array_df_up.index, columns=hap_pos_new,dtype='int8' ) ],axis=1 )
            hap_array_df_down = pd.concat([ pd.DataFrame(0, index=hap_array_df_batch.index, columns=hap_pos_existing, dtype='int8' ), hap_array_df_batch[hap_pos_new] ],axis=1 )
            hap_array_df_index = pd.concat( [hap_array_df_up,hap_array_df_down], axis=0)
            hap_array_df_batch = hap_array_df_down
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
        elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] != 0:
            hap_pos_overlap = hap_pos_df_overlap[0].tolist()
            hap_pos_new = hap_pos_df_new[0].tolist()
            hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
            hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
            hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
            hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
            hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
            hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap + hap_pos_new ]
            hap_array_df_index = pd.concat([ hap_array_df_up, hap_array_df_batch ], axis=0)
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            hap_pos_updated = hap_pos_overlap + hap_pos_new
        elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] == 0:
            hap_pos_existing = hap_pos_df_existing[0].tolist()
            hap_pos_overlap = hap_pos_df_overlap[0].tolist()
            hap_pos_df_existing_idx = (hap_pos_df_existing['index_x'] + 1).tolist()
            hap_pos_df_overlap_idx = (hap_pos_df_overlap['index_x'] + 1).tolist()
            command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_existing_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_existing.txt'
            os.system(command)
            command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_overlap_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_overlap.txt'
            os.system(command)
            pd.DataFrame(0, index=range(hap_count_batch),columns=range(hap_pos_df_existing.shape[0])).to_csv(output_path+'tmp_zeros_existing.txt',sep='\t', index=False, header=False)
            command = 'cat '+output_path+'hap_array_df_existing.txt '+output_path+'tmp_zeros_existing.txt > '+output_path+'hap_array_df_existing_merge.txt'
            os.system(command)
            hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df_overlap.txt',sep='\t',header=0)
            hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
            hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
            # hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
            hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
            hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap ]
            hap_array_df_index = pd.concat([ hap_array_df_overlap, hap_array_df_batch ], axis=0)
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df_index.txt',sep='\t', header=True, index=None)
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            #keep the hap_array_df_updated
            command = 'paste ' + output_path+'hap_array_df_existing_merge.txt '+ output_path+'hap_array_df_index.txt > ' + output_path+'hap_array_df.txt'
            os.system(command)
            hap_pos_updated = hap_pos_existing + hap_pos_overlap
            os.remove(output_path+'hap_array_df_existing.txt')
            os.remove(output_path+'hap_array_df_overlap.txt')
            os.remove(output_path+'tmp_zeros_existing.txt')
            os.remove(output_path+'hap_array_df_existing_merge.txt')
            os.remove(output_path+'hap_array_df_index.txt')
        elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] != 0:
            hap_pos_existing = hap_pos_df_existing[0].tolist()
            hap_pos_overlap = hap_pos_df_overlap[0].tolist()
            hap_pos_new = hap_pos_df_new[0].tolist()
            hap_pos_df_existing_idx = (hap_pos_df_existing['index_x'] + 1).tolist()
            hap_pos_df_overlap_idx = (hap_pos_df_overlap['index_x'] + 1).tolist()
            # print(hap_pos_df, hap_pos_df_existing, hap_pos_df_overlap, hap_pos_df_new)
            command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_existing_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_existing.txt'
            os.system(command)
            command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_overlap_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_overlap.txt'
            os.system(command)
            pd.DataFrame(0, index=range(hap_count_batch),columns=range(hap_pos_df_existing.shape[0])).to_csv(output_path+'tmp_zeros_existing.txt',sep='\t', index=False, header=False)
            # cat hap_array_df_existing.txt tmp_zeros_existing.txt > hap_array_df_existing_merge.txt
            command = 'cat '+output_path+'hap_array_df_existing.txt '+output_path+'tmp_zeros_existing.txt > '+output_path+'hap_array_df_existing_merge.txt'
            os.system(command)
            # hap_array_df_overlap = pd.read_csv(output_path+'haplotype_array_df_tmp.txt',sep='\t',header=0, usecols= [ str(x) for x in hap_pos_overlap], dtype='int8')
            hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df_overlap.txt',sep='\t',header=0)
            hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
            # print(cur_hapidx, hap_array_df_overlap, hap_pos_overlap,hap_pos_df)
            hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
            hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
            hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap + hap_pos_new ]
            hap_array_df_index = pd.concat([ hap_array_df_up, hap_array_df_batch ], axis=0)
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df_index.txt',sep='\t', header=True, index=None)
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            #keep the hap_array_df_updated
            # paste hap_array_df_existing_merge.txt hap_array_df_index.txt >hap_array_df.txt
            command = 'paste ' + output_path+'hap_array_df_existing_merge.txt '+ output_path+'hap_array_df_index.txt > ' + output_path+'hap_array_df.txt'
            os.system(command)
            # t = pd.read_csv(output_path+'hap_array_df.txt',sep='\t')
            hap_pos_updated = hap_pos_existing + hap_pos_overlap + hap_pos_new
            os.remove(output_path+'hap_array_df_existing.txt')
            os.remove(output_path+'hap_array_df_overlap.txt')
            os.remove(output_path+'tmp_zeros_existing.txt')
            os.remove(output_path+'hap_array_df_existing_merge.txt')
            os.remove(output_path+'hap_array_df_index.txt')

    if batch_flag == True:
        """step2 :construct the faiss index and query to search"""
        print('constructing the faiss index and query...')
        xb = hap_array_index.astype(np.int8)
        xq = hap_array_batch.astype(np.int8)
        measure = faiss.METRIC_L2
        param = 'Flat'
        # param = 'HNSW64'
        # dim = len(hap_pos_batch)
        dim= hap_array_batch.shape[1]
        index = faiss.index_factory(dim, param, measure)
        # print(index.is_trained)
        index.add(xb)
        can_dis, can_ids = index.search(xq, can_lens)
        """step 3 identify the parent hap for each hap"""
        # edges_df = pd.DataFrame(columns=[0, 1,2,3])
        edges_df = edges_df_existing.copy()
        edges_df.columns = [0,1,2,3]
        # hap_count_batch = haplotype_df_batch.shape[0]
        hap_ids = haplotype_df_updated.index.tolist()[-hap_count_batch:]
        for cur_hap_index in range(hap_count_batch):
        # for cur_hap_index in range(30):
            # cur_hap_index = 0
            cur_hapidx = hap_ids[cur_hap_index]
            # cur_hapidx = 118
            # if cur_hapidx % 1 == 0:
            #     print('processing the ', str(cur_hap_index), '/', str(hap_count_batch),' th hap in the ', str(batch_idx), 'th batch')
            cur_hap, cur_mut_count, cur_sample_names, cur_sample_count, cur_locations, cur_time_min, cur_time_max, cur_hap_pos, cur_hap_value = haplotype_df_updated.loc[cur_hapidx]
            cur_can_ids = can_ids[cur_hap_index].tolist()
            cur_locations = cur_locations.split(';')
            if cur_mut_count == 1: #no choice but the reference hap
                parent_hapidx = 0
                # edges_list.append([parent_hapidx, cur_hapidx, cur_hap, 1])
                edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, cur_hap, 1]).T ], axis=0)
                # type1_list.append([batch_idx, cur_hap_index , cur_hapidx])
                continue
            flag_count = 0
            parent_can_list = []
            dict_parent_can = {}
            # print('processing hap: ', cur_hapidx,'\n' ,'mutation positions: ', cur_hap_pos,'\n', 'values: ', cur_hap_value, '\n', [ x for x in cur_can_ids if x!=-1 ][:20])
            a = hap_array_df_index.loc[cur_hapidx]
            for each_hap_index in range(1, can_lens):
                # each_hap_index = 1
                each_hapidx = cur_can_ids[each_hap_index]
                if each_hapidx == -1:
                    break
                if flag_count >= 5:
                    break
                elif each_hapidx >= cur_hapidx:
                    continue
                each_hap_pos = haplotype_df_updated.loc[each_hapidx]['hap_pos']
                flag_count += 1
                b = hap_array_df_index.loc[each_hapidx]
                diff = a.compare(b)
                diff_count = diff.shape[0]
                dict_parent_can[each_hapidx] = diff
                parent_can_list.append( [each_hapidx,  diff_count])
                # print( 'candidate parents: ', each_hapidx, each_hap_pos, diff )
            # print('\n')
            if len(parent_can_list) > 0: 
                parent_can_df = pd.DataFrame(parent_can_list)
                parent_can_df = parent_can_df.sort_values([1], ascending=True)
                parent_can_df = parent_can_df[parent_can_df[1]==parent_can_df.iloc[0][1]] # select the haps with minimum distance
                if parent_can_df.shape[0] == 1: # this means there is only 1 candidate          
                    parent_hapidx = parent_can_df.iloc[0][0]
                    parent_diff = dict_parent_can[parent_hapidx]
                    # print(parent_hapidx, parent_diff)
                    parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                    # print(haplotype_df_updated.loc[parent_hapidx], cur_hap)
                    tmpdf= parent_diff[parent_diff['other']!=0]
                    if tmpdf.shape[0] == 0: # have no back mutation (the simplest)
                        # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                        # type1_count += 1         
                        pass       
                    else: #have back mutation
                        if tmpdf[tmpdf['self']!=0].shape[0] == tmpdf.shape[0]: # the same pos have multiple mutations
                            # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                            pass
                        else:
                            # type2_list.append([batch_idx,cur_hap_index, cur_hapidx])
                            # print(cur_hapidx, cur_hap, parent_can_df, parent_diff, parent_hap, parent_hapidx) 
                            # print(edges_df)
                            parent_hapidx = find_parent(parent_hapidx)
                            if parent_hapidx not in dict_parent_can.keys():
                                if parent_hapidx == 0:
                                    edge_muts = cur_hap
                                    edge_diff_count = cur_mut_count
                                    edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                                    continue
                                else:
                                    b = hap_array_df_index.loc[parent_hapidx]
                                    parent_diff = a.compare(b)
                            else:
                                parent_diff = dict_parent_can[parent_hapidx]
                    #process the parent hap 
                    if parent_hapidx == 0:
                        edge_muts = cur_hap
                        edge_diff_count = cur_mut_count
                    else:
                        # parent_diff = dict_parent_can[parent_hapidx]
                        parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                        parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                        parent_hap_pos = [ int(x.strip()) for x in parent_hap_pos[1:-1].split(',') ]
                        edge_muts = []
                        # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], cur_hapidx, parent_hapidx, parent_diff)
                        for each_diffidx in parent_diff.index.tolist():
                            # each_diffidx = 3036
                            v1, v2 = parent_diff.loc[each_diffidx]
                            if v1 == 0 and v2 != 0:
                                mut_idx = parent_hap_pos.index(each_diffidx)
                                edge_mut_tmp = parent_hap.split(';')[mut_idx]
                                edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                            elif v1!= 0 and v2 == 0:
                                mut_idx = cur_hap_pos.index(each_diffidx)
                                edge_mut = cur_hap.split(';')[mut_idx]
                            elif v1!= 0 and v2 != 0:
                                mut1_idx = cur_hap_pos.index(each_diffidx)
                                mut1 = cur_hap.split(';')[mut1_idx]
                                # print(parent_hap, parent_hap_pos, [type(x) for x in parent_hap_pos], parent_diff, each_diffidx, type(each_diffidx))
                                mut2_idx = parent_hap_pos.index(each_diffidx)
                                # print(parent_hap, parent_hap_pos, parent_diff, each_diffidx, mut2_idx)
                                mut2 = parent_hap.split(';')[mut2_idx]
                                edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                            edge_muts.append( edge_mut)
                        edge_diff_count = parent_diff.shape[0]
                        edge_muts = ';'.join(edge_muts)
                        # edges_list.append([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count] )
                    edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                else: #this means there are multiple just select the hap which contain most samples in spatical-tempory 
                    # print(cur_hapidx, cur_hap, parent_can_df)
                    # type3_list.append([batch_idx,cur_hap_index, cur_hapidx])
                    parent_can_df.index = parent_can_df[0]
                    parent_can_idxs_list = parent_can_df[0].tolist()
                    #remove the candidates with back mutation
                    can_back_df = []
                    for each_idx in parent_can_idxs_list:
                        # each_idx = parent_can_idxs_list[0]
                        each_diff = dict_parent_can[each_idx]
                        tmpdf= each_diff[each_diff['other']!=0]
                        tmpdf= tmpdf[tmpdf['self']==0]
                        can_back_df.append(tmpdf.shape[0])
                    can_back_df = pd.DataFrame(can_back_df)
                    can_back_df.index=  parent_can_idxs_list
                    can_back_df = can_back_df.sort_values([0], ascending=True)
                    can_back_df = can_back_df[can_back_df[0]==can_back_df.iloc[0][0]]
                    if can_back_df.shape[0] == 1:  ##only 1 candidate do not have back mutation
                        parent_hapidx = can_back_df.index[0]
                    else: #multiple do not have back mutation
                        #if have parent-child relationship
                        parent_child_flag = False
                        can_list = can_back_df.index.tolist()
                        can_list = sorted(can_list, reverse=True)
                        for i in range(len(can_list)-1):
                            can_parent_hapidx = edges_df[edges_df[1]== can_list[i]].index[0]
                            if can_parent_hapidx in can_list[i+1:]: 
                                parent_hapidx = can_parent_hapidx
                                parent_child_flag = True
                                # continue
                        #if do not have parent-child relationship
                        if parent_child_flag == False:
                            parent_can_df = parent_can_df.loc[can_back_df.index.tolist()]
                            parent_can_df = pd.concat([parent_can_df,  haplotype_df_updated.loc[parent_can_df.index.tolist()][['sample_time_min', 'sample_time_max', 'sample_locations']] ], axis=1)
                            parent_can_idx_selected = []
                            for each_parent_can in parent_can_df.values:
                                # each_parent_can = parent_can_df.values[0]
                                each_parent_hapidx, diff_count, each_time_min, each_time_max, each_locations = each_parent_can
                                each_locations = each_locations.split(';')
                                delta_time = each_time_max - cur_time_min
                                if delta_time.days >= -30 and len(set(cur_locations).intersection(set(each_locations))) >= 1:
                                    parent_can_idx_selected.append(each_parent_hapidx)
                            if len(parent_can_idx_selected) == 0: #this means no one hap in spatical-tempory 
                                #select the hap which contain the most samples in all candidates
                                 # parent_hapidx = haplotype_df_updated.loc[parent_can_df.index.tolist()].sort_values(['sample_count'], ascending=False).index[0]
                                 ##select the hap which is the nearest of time in all candidates
                                 parent_hapidx = parent_can_df.sort_values(['sample_time_min'], ascending=False).index[0]
                            elif len(parent_can_idx_selected) == 1: #this means only one just select it
                                parent_hapidx = parent_can_idx_selected[0]
                            elif len(parent_can_idx_selected) > 1: # this means multiple in spatical-tempory just select the the hap which contain the most samples in spatical-tempory 
                                parent_hapidx = haplotype_df_updated.loc[parent_can_idx_selected].sort_values(['sample_count'], ascending=False).index[0]
                    parent_diff = dict_parent_can[parent_hapidx]
                    parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                    if parent_hap == '':
                        edge_muts = cur_hap
                        edge_diff_count = parent_diff.shape[0]
                    else:
                        parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                        parent_hap_pos = [ int(x.strip()) for x in parent_hap_pos[1:-1].split(',') ]
                        # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], parent_hapidx, parent_diff)
                        edge_muts = []
                        for each_diffidx in parent_diff.index:
                            v1, v2 = parent_diff.loc[each_diffidx]
                            if v1 == 0 and v2 != 0:                            
                                mut_idx = parent_hap_pos.index(each_diffidx)
                                edge_mut_tmp = parent_hap.split(';')[mut_idx]
                                edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                            elif v1!= 0 and v2 == 0:
                                mut_idx = cur_hap_pos.index(each_diffidx)
                                edge_mut = cur_hap.split(';')[mut_idx]
                            elif v1!= 0 and v2 != 0:
                                mut1_idx = cur_hap_pos.index(each_diffidx)
                                mut1 = cur_hap.split(';')[mut1_idx]
                                mut2_idx = parent_hap_pos.index(each_diffidx)
                                mut2 = parent_hap.split(';')[mut2_idx]
                                edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                            edge_muts.append( edge_mut)
                        edge_diff_count = parent_diff.shape[0]
                        edge_muts = ';'.join(edge_muts)
                    edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
            else: #this maybe caused by the can_lens is set too small just re-search for cur hap
                # type4_list.append([batch_idx, cur_hap_index, cur_hapidx])
                print(cur_hapidx ,' Warning')
                parent_hapidx = 0
                edge_muts = cur_hap
                edge_diff_count = cur_mut_count
                edges_df = pd.concat([ edges_df, pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
else:
    newdata.columns = ['samplename', 'haplotype']

    haplotype_df_existing = pd.read_csv(data_path + 'haplotype_final.txt', sep='\t', header=0)
    haplotype_df_existing = haplotype_df_existing.fillna('')
    hap_count_existing = haplotype_df_existing.shape[0]

    hap_array_df_existing = pd.read_csv(data_path + 'hap_array_df.txt', sep='\t', header=0)
    hap_pos_existing = [ int(x) for x in hap_array_df_existing.columns.tolist()]
    hap_array_df_existing.columns = hap_pos_existing

    haplotype_df_updated = haplotype_df_existing.copy()
    hap_array_df_updated = hap_array_df_existing.copy()
    hap_pos_updated = hap_pos_existing

    """step1: get the haplotype vector form and update the latest hap"""
    haplotype_df_batch, hap_array_batch, hap_array_df_batch, hap_pos_batch = convert_smaples_mutation_into_hap_df_array_nometa(data_batch)
    #merge the existing haps into haplotype_df_update and change it self
    df1 = pd.DataFrame()
    df1['hapid'] = haplotype_df_updated.index
    df1['haplotype'] = haplotype_df_updated['haplotype']
    df2 = pd.DataFrame()
    df2['hapid'] = haplotype_df_batch.index
    df2['haplotype'] = haplotype_df_batch['haplotype']
    haplotype_df_merge = pd.merge(df1, df2, how='inner', on='haplotype')
    if haplotype_df_merge.shape[0] == 0: #there is no samples in existing haps
        pass
    else:
        for each_hap_info in haplotype_df_merge.values:
            hapid_x, haplotype, hapid_y = each_hap_info
            haplotype_df_updated.loc[hapid_x, 'sample_names'] += ';' + haplotype_df_batch.loc[hapid_y, 'sample_names']
            haplotype_df_updated.loc[hapid_x, 'sample_count'] += haplotype_df_batch.loc[hapid_y, 'sample_count']
            # haplotype_df_updated.loc[hapid_x, 'sample_locations'] = ';'.join(set(haplotype_df_updated.loc[hapid_x, 'sample_locations'].split(';')+ haplotype_df_batch.loc[hapid_y, 'sample_locations'].split(';')))
            # haplotype_df_updated.loc[hapid_x, 'sample_time_min'] = min( haplotype_df_updated.loc[hapid_x, 'sample_time_min'], haplotype_df_batch.loc[hapid_y, 'sample_time_min'])
            # haplotype_df_updated.loc[hapid_x, 'sample_time_max'] = max( haplotype_df_updated.loc[hapid_x, 'sample_time_max'], haplotype_df_batch.loc[hapid_y, 'sample_time_max'])
        haplotype_removed_indexs = [x for x in haplotype_df_batch.index.tolist() if x not in haplotype_df_merge['hapid_y'].tolist()]
        haplotype_df_batch = haplotype_df_batch.loc[haplotype_removed_indexs]
        hap_array_df_batch = hap_array_df_batch.loc[haplotype_removed_indexs]
        hap_array_batch = np.array(hap_array_df_batch)

    hap_count_updated = haplotype_df_updated.shape[0]
    hap_count_batch = haplotype_df_batch.shape[0]
    haplotype_df_batch.index = range(hap_count_updated, hap_count_updated + hap_count_batch )
    hap_array_df_batch.index = range(hap_count_updated, hap_count_updated + hap_count_batch)
    haplotype_df_updated = pd.concat([ haplotype_df_updated, haplotype_df_batch ], axis=0)

    batch_flag = True
    # if hap_count_batch == 0:
    #     print('The new haplotypes have all appeared in existing haplotypes.')
    #     # break
    if len(hap_pos_updated) == 0:
        hap_array_df_updated = pd.concat([hap_array_df_updated, hap_array_df_batch],axis=0)
        hap_array_df_index = hap_array_df_updated
        hap_array_index = np.array(hap_array_df_updated)
        hap_pos_updated = hap_pos_batch
        hap_array_df_updated.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
    else:
        hap_pos_df = pd.merge(pd.DataFrame(hap_pos_updated).reset_index(), pd.DataFrame(hap_pos_batch).reset_index(), how='outer', on=0 )
        hap_pos_df_new = hap_pos_df[hap_pos_df['index_x'].isna() ]
        hap_pos_df_existing = hap_pos_df[hap_pos_df['index_y'].isna() ]
        hap_pos_df_overlap = hap_pos_df[(hap_pos_df['index_x'].notna()) & (hap_pos_df['index_y'].notna()) ]
        if hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] == 0:
            batch_flag = False
        elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] != 0:
            batch_flag = False
        elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] == 0:
            batch_flag = False
        elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] == 0:
            hap_pos_overlap = hap_pos_df_overlap[0].tolist()
            hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
            hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
            hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
            hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
            hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap ]
            hap_array_df_index = pd.concat([ hap_array_df_overlap, hap_array_df_batch ], axis=0)
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            hap_pos_updated = hap_pos_overlap
        elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] != 0:
            hap_pos_existing = hap_pos_df_existing[0].tolist()
            hap_pos_new = hap_pos_df_new[0].tolist()
            hap_pos_updated = hap_pos_existing + hap_pos_new
            hap_array_df_up = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
            hap_array_df_up = hap_array_df_up.fillna(0).astype('int8') 
            hap_array_df_up.columns = [ int(x) for x in hap_array_df_up.columns ]
            hap_array_df_up = pd.concat([hap_array_df_up[hap_pos_existing], pd.DataFrame(0, index=hap_array_df_up.index, columns=hap_pos_new,dtype='int8' ) ],axis=1 )
            hap_array_df_down = pd.concat([ pd.DataFrame(0, index=hap_array_df_batch.index, columns=hap_pos_existing, dtype='int8' ), hap_array_df_batch[hap_pos_new] ],axis=1 )
            hap_array_df_index = pd.concat( [hap_array_df_up,hap_array_df_down], axis=0)
            hap_array_df_batch = hap_array_df_down
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
        elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] != 0:
            hap_pos_overlap = hap_pos_df_overlap[0].tolist()
            hap_pos_new = hap_pos_df_new[0].tolist()
            hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
            hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
            hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
            hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
            hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
            hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap + hap_pos_new ]
            hap_array_df_index = pd.concat([ hap_array_df_up, hap_array_df_batch ], axis=0)
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            hap_pos_updated = hap_pos_overlap + hap_pos_new
        elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] == 0:
            hap_pos_existing = hap_pos_df_existing[0].tolist()
            hap_pos_overlap = hap_pos_df_overlap[0].tolist()
            hap_pos_df_existing_idx = (hap_pos_df_existing['index_x'] + 1).tolist()
            hap_pos_df_overlap_idx = (hap_pos_df_overlap['index_x'] + 1).tolist()
            command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_existing_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_existing.txt'
            os.system(command)
            command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_overlap_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_overlap.txt'
            os.system(command)
            pd.DataFrame(0, index=range(hap_count_batch),columns=range(hap_pos_df_existing.shape[0])).to_csv(output_path+'tmp_zeros_existing.txt',sep='\t', index=False, header=False)
            command = 'cat '+output_path+'hap_array_df_existing.txt '+output_path+'tmp_zeros_existing.txt > '+output_path+'hap_array_df_existing_merge.txt'
            os.system(command)
            hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df_overlap.txt',sep='\t',header=0)
            hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
            hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
            # hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
            hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
            hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap ]
            hap_array_df_index = pd.concat([ hap_array_df_overlap, hap_array_df_batch ], axis=0)
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df_index.txt',sep='\t', header=True, index=None)
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            #keep the hap_array_df_updated
            command = 'paste ' + output_path+'hap_array_df_existing_merge.txt '+ output_path+'hap_array_df_index.txt > ' + output_path+'hap_array_df.txt'
            os.system(command)
            hap_pos_updated = hap_pos_existing + hap_pos_overlap
            os.remove(output_path+'hap_array_df_existing.txt')
            os.remove(output_path+'hap_array_df_overlap.txt')
            os.remove(output_path+'tmp_zeros_existing.txt')
            os.remove(output_path+'hap_array_df_existing_merge.txt')
            os.remove(output_path+'hap_array_df_index.txt')
        elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] != 0:
            hap_pos_existing = hap_pos_df_existing[0].tolist()
            hap_pos_overlap = hap_pos_df_overlap[0].tolist()
            hap_pos_new = hap_pos_df_new[0].tolist()
            hap_pos_df_existing_idx = (hap_pos_df_existing['index_x'] + 1).tolist()
            hap_pos_df_overlap_idx = (hap_pos_df_overlap['index_x'] + 1).tolist()
            # print(hap_pos_df, hap_pos_df_existing, hap_pos_df_overlap, hap_pos_df_new)
            command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_existing_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_existing.txt'
            os.system(command)
            command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_overlap_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_overlap.txt'
            os.system(command)
            pd.DataFrame(0, index=range(hap_count_batch),columns=range(hap_pos_df_existing.shape[0])).to_csv(output_path+'tmp_zeros_existing.txt',sep='\t', index=False, header=False)
            # cat hap_array_df_existing.txt tmp_zeros_existing.txt > hap_array_df_existing_merge.txt
            command = 'cat '+output_path+'hap_array_df_existing.txt '+output_path+'tmp_zeros_existing.txt > '+output_path+'hap_array_df_existing_merge.txt'
            os.system(command)
            # hap_array_df_overlap = pd.read_csv(output_path+'haplotype_array_df_tmp.txt',sep='\t',header=0, usecols= [ str(x) for x in hap_pos_overlap], dtype='int8')
            hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df_overlap.txt',sep='\t',header=0)
            hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
            # print(cur_hapidx, hap_array_df_overlap, hap_pos_overlap,hap_pos_df)
            hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
            hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
            hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap + hap_pos_new ]
            hap_array_df_index = pd.concat([ hap_array_df_up, hap_array_df_batch ], axis=0)
            hap_array_index = np.array(hap_array_df_index, dtype='int8')
            hap_array_df_index.to_csv(output_path+'hap_array_df_index.txt',sep='\t', header=True, index=None)
            hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
            #keep the hap_array_df_updated
            # paste hap_array_df_existing_merge.txt hap_array_df_index.txt >hap_array_df.txt
            command = 'paste ' + output_path+'hap_array_df_existing_merge.txt '+ output_path+'hap_array_df_index.txt > ' + output_path+'hap_array_df.txt'
            os.system(command)
            # t = pd.read_csv(output_path+'hap_array_df.txt',sep='\t')
            hap_pos_updated = hap_pos_existing + hap_pos_overlap + hap_pos_new
            os.remove(output_path+'hap_array_df_existing.txt')
            os.remove(output_path+'hap_array_df_overlap.txt')
            os.remove(output_path+'tmp_zeros_existing.txt')
            os.remove(output_path+'hap_array_df_existing_merge.txt')
            os.remove(output_path+'hap_array_df_index.txt')

    if batch_flag == True:
        """step2 :construct the faiss index and query to search"""
        print('constructing the faiss index and query...')
        xb = hap_array_index.astype(np.int8)
        xq = hap_array_batch.astype(np.int8)
        measure = faiss.METRIC_L2
        param = 'Flat'
        # param = 'HNSW64'
        # dim = len(hap_pos_batch)
        dim= hap_array_batch.shape[1]
        index = faiss.index_factory(dim, param, measure)
        # print(index.is_trained)
        index.add(xb)
        can_dis, can_ids = index.search(xq, can_lens)

        """step 3 identify the parent hap for each hap"""
        # edges_df = pd.DataFrame(columns=[0, 1,2,3])
        edges_df = edges_df_existing.copy()
        edges_df.columns = [0,1,2,3]
        # hap_count_batch = haplotype_df_batch.shape[0]
        hap_ids = haplotype_df_updated.index.tolist()[-hap_count_batch:]
        for cur_hap_index in range(hap_count_batch):
        # for cur_hap_index in range(1, hap_count_base):
        # for cur_hap_index in range(30):
            # cur_hap_index = 153
            cur_hapidx = hap_ids[cur_hap_index]
            # cur_hapidx = 118
            # if cur_hapidx % 1 == 0:
            #     print('processing the ', str(cur_hap_index), '/', str(hap_count_batch),' th hap in the ', str(batch_idx), 'th batch')
            cur_hap, cur_mut_count, cur_sample_names, cur_sample_count, cur_hap_pos, cur_hap_value = haplotype_df_updated.loc[cur_hapidx]
            cur_can_ids = can_ids[cur_hap_index].tolist()
            # cur_locations = cur_locations.split(';')
            if cur_mut_count == 1: #no choice but the reference hap
                parent_hapidx = 0
                # edges_list.append([parent_hapidx, cur_hapidx, cur_hap, 1])
                edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, cur_hap, 1]).T ], axis=0)
                # type1_list.append([batch_idx, cur_hap_index , cur_hapidx])
                continue
            flag_count = 0
            parent_can_list = []
            dict_parent_can = {}
            # print('processing hap: ', cur_hapidx,'\n' ,'mutation positions: ','\n', 'values: ', '\n', [ x for x in cur_can_ids if x!=-1 ][:20])
            a = hap_array_df_index.loc[cur_hapidx]
            for each_hap_index in range(1, can_lens):
                # each_hap_index = 1
                each_hapidx = cur_can_ids[each_hap_index]
                if each_hapidx == -1:
                    break
                if flag_count >= 5:
                    break
                elif each_hapidx >= cur_hapidx:
                    continue
                each_hap_pos = haplotype_df_updated.loc[each_hapidx]['hap_pos']
                flag_count += 1
                b = hap_array_df_index.loc[each_hapidx]
                diff = a.compare(b)
                diff_count = diff.shape[0]
                dict_parent_can[each_hapidx] = diff
                parent_can_list.append( [each_hapidx,  diff_count])
                # print( 'candidate parents: ', each_hapidx, diff,diff.shape )
            # print('\n')
            if len(parent_can_list) > 0: 
                parent_can_df = pd.DataFrame(parent_can_list)
                parent_can_df = parent_can_df.sort_values([1], ascending=True)
                parent_can_df = parent_can_df[parent_can_df[1]==parent_can_df.iloc[0][1]] # select the haps with minimum distance
                if parent_can_df.shape[0] == 1: # this means there is only 1 candidate          
                    parent_hapidx = parent_can_df.iloc[0][0]
                    parent_diff = dict_parent_can[parent_hapidx]
                    # print(parent_hapidx, parent_diff)
                    parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                    # print(haplotype_df_updated.loc[parent_hapidx], cur_hap)
                    tmpdf= parent_diff[parent_diff['other']!=0]
                    if tmpdf.shape[0] == 0: # have no back mutation (the simplest)
                        # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                        # type1_count += 1
                        pass       
                    else: #have back mutation
                        if tmpdf[tmpdf['self']!=0].shape[0] == tmpdf.shape[0]: # the same pos have multiple mutations
                            # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                            pass
                        else:
                            # pass
                            # type2_list.append([batch_idx,cur_hap_index, cur_hapidx])
                            parent_hapidx = find_parent(parent_hapidx)
                            if parent_hapidx not in dict_parent_can.keys():
                                if parent_hapidx == 0:
                                    edge_muts = cur_hap
                                    edge_diff_count = cur_mut_count
                                    edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                                    continue
                                else:
                                    b = hap_array_df_updated.loc[parent_hapidx]
                                    parent_diff = a.compare(b)
                            else:
                                parent_diff = dict_parent_can[parent_hapidx]
                    #process the parent hap 
                    if parent_hapidx == 0:
                        edge_muts = cur_hap
                        edge_diff_count = cur_mut_count
                    else:
                        # parent_diff = dict_parent_can[parent_hapidx]
                        parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                        parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                        parent_hap_pos = [ int(x.strip()) for x in parent_hap_pos[1:-1].split(',') ]
                        edge_muts = []
                        # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], cur_hapidx, parent_hapidx, parent_diff)
                        for each_diffidx in parent_diff.index:
                            # each_diffidx = 3036
                            v1, v2 = parent_diff.loc[each_diffidx]
                            if v1 == 0 and v2 != 0:
                                mut_idx = parent_hap_pos.index(each_diffidx)
                                edge_mut_tmp = parent_hap.split(';')[mut_idx]
                                edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                            elif v1!= 0 and v2 == 0:
                                mut_idx = cur_hap_pos.index(each_diffidx)
                                edge_mut = cur_hap.split(';')[mut_idx]
                            elif v1!= 0 and v2 != 0:
                                mut1_idx = cur_hap_pos.index(each_diffidx)
                                mut1 = cur_hap.split(';')[mut1_idx]
                                mut2_idx = parent_hap_pos.index(each_diffidx)
                                mut2 = parent_hap.split(';')[mut2_idx]
                                edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                            edge_muts.append( edge_mut)
                        edge_diff_count = parent_diff.shape[0]
                        edge_muts = ';'.join(edge_muts)
                        # edges_list.append([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count] )
                    edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                else: #this means there are multiple just select the hap which contain most samples in spatical-tempory 
                    # print(cur_hapidx, cur_hap, parent_can_df)
                    # type3_list.append([batch_idx,cur_hap_index, cur_hapidx])
                    parent_can_df.index = parent_can_df[0]
                    parent_can_idxs_list = parent_can_df[0].tolist()
                    #remove the candidates with back mutation
                    can_back_df = []
                    for each_idx in parent_can_idxs_list:
                        # each_idx = parent_can_idxs_list[0]
                        each_diff = dict_parent_can[each_idx]
                        tmpdf= each_diff[each_diff['other']!=0]
                        tmpdf= tmpdf[tmpdf['self']==0]
                        can_back_df.append(tmpdf.shape[0])
                    can_back_df = pd.DataFrame(can_back_df)
                    can_back_df.index=  parent_can_idxs_list
                    can_back_df = can_back_df.sort_values([0], ascending=True)
                    can_back_df = can_back_df[can_back_df[0]==can_back_df.iloc[0][0]]
                    # print(can_back_df)
                    # can_back_df = parent_can_df
                    if can_back_df.shape[0] == 1:  ##only 1 candidate do not have back mutation
                        parent_hapidx = can_back_df.index[0]
                    else: #multiple do not have back mutation
                        #if have parent-child relationship
                        parent_child_flag = False
                        can_list = can_back_df.index.tolist()
                        can_list = sorted(can_list, reverse=True)
                        # print(cur_hapidx, can_list, edges_df)
                        for i in range(len(can_list)-1):
                            can_parent_hapidx = edges_df[edges_df[1]== can_list[i]].index[0]
                            if can_parent_hapidx in can_list[i+1:]: 
                                parent_hapidx = can_parent_hapidx
                                parent_child_flag = True
                                # continue
                        #if do not have parent-child relationship
                        if parent_child_flag == False:
                            parent_can_df = parent_can_df.loc[can_back_df.index.tolist()]
                            parent_can_idx_selected = parent_can_df[0].values.tolist()
                            # parent_can_df = pd.concat([parent_can_df,  haplotype_df_updated.loc[parent_can_df.index.tolist()][['sample_time_min', 'sample_time_max', 'sample_locations']] ], axis=1)
                            # parent_can_idx_selected = []
                            # for each_parent_can in parent_can_df.values:
                            #     # each_parent_can = parent_can_df.values[0]
                            #     each_parent_hapidx, diff_count, each_time_min, each_time_max, each_locations = each_parent_can
                            #     each_locations = each_locations.split(';')
                            #     delta_time = each_time_max - cur_time_min
                            #     if delta_time.days >= -30 and len(set(cur_locations).intersection(set(each_locations))) >= 1:
                            #         parent_can_idx_selected.append(each_parent_hapidx)
                            if len(parent_can_idx_selected) == 0: #this means no one hap in spatical-tempory 
                                #select the hap which contain the most samples in all candidates
                                 # parent_hapidx = haplotype_df_updated.loc[parent_can_df.index.tolist()].sort_values(['sample_count'], ascending=False).index[0]
                                 ##select the hap which is the nearest of time in all candidates
                                 # parent_hapidx = parent_can_df.sort_values(['sample_time_min'], ascending=False).index[0]
                                 parent_hapidx = parent_can_df.sort_values(['sample_count'], ascending=False).index[0]
                            elif len(parent_can_idx_selected) == 1: #this means only one just select it
                                parent_hapidx = parent_can_idx_selected[0]
                            elif len(parent_can_idx_selected) > 1: # this means multiple in spatical-tempory just select the the hap which contain the most samples in spatical-tempory 
                                parent_hapidx = haplotype_df_updated.loc[parent_can_idx_selected].sort_values(['sample_count'], ascending=False).index[0]
                    parent_diff = dict_parent_can[parent_hapidx]
                    parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                    if parent_hap == '':
                        edge_muts = cur_hap
                        edge_diff_count = parent_diff.shape[0]
                    else:
                        parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                        parent_hap_pos = [ int(x.strip()) for x in parent_hap_pos[1:-1].split(',') ]
                        # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], parent_hapidx, parent_diff)
                        edge_muts = []
                        for each_diffidx in parent_diff.index:
                            v1, v2 = parent_diff.loc[each_diffidx]
                            if v1 == 0 and v2 != 0:                            
                                mut_idx = parent_hap_pos.index(each_diffidx)
                                edge_mut_tmp = parent_hap.split(';')[mut_idx]
                                edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                            elif v1!= 0 and v2 == 0:
                                mut_idx = cur_hap_pos.index(each_diffidx)
                                edge_mut = cur_hap.split(';')[mut_idx]
                            elif v1!= 0 and v2 != 0:
                                mut1_idx = cur_hap_pos.index(each_diffidx)
                                mut1 = cur_hap.split(';')[mut1_idx]
                                mut2_idx = parent_hap_pos.index(each_diffidx)
                                mut2 = parent_hap.split(';')[mut2_idx]
                                edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                            edge_muts.append( edge_mut)
                        edge_diff_count = parent_diff.shape[0]
                        edge_muts = ';'.join(edge_muts)
                    edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
            else: #this maybe caused by the can_lens is set too small just re-search for cur hap
                # type4_list.append([batch_idx, cur_hap_index, cur_hapidx])
                print(cur_hapidx ,' Warning') 
                parent_hapidx = 0
                edge_muts = cur_hap
                edge_diff_count = cur_mut_count
                edges_df = pd.concat([ edges_df, pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)

"""save files"""
haplotype_df_updated.to_csv(output_path+'haplotype_final.txt',sep='\t', header=True, index=None)
# hap_array_df_updated.to_csv(output_path+'hap_array_df_final.txt',sep='\t', header=True, index=None)

edges_df_new = edges_df.iloc[-hap_count_batch:]
edges_df_new.columns = ['source', 'target', 'mutations', 'distance']
edges_df_new.to_csv(output_path+'edges_new.csv',sep=',', index=None)

max_clusterid = int(max(nodes_df_existing['ClusterId']))
cluster_len = 1
f=open(output_path + "nodes_new.csv","w")
f.write("Hapid,Label,Time_min,Time_max,ClusterId,Cluster_len,Value,Mutations,Locations,Samplenames\n")
# f.write("Hapid,Label,ClusterId,Cluster_len,Value,Mutations\n")
for node in haplotype_df_updated.index[-hap_count_batch:]:
    node_value = 100
    node_label = node
    node_parent = edges_df_new[edges_df_new['target']==node].iloc[0]['source']
    # node_cluster = dict_nodestoclusters[ node_parent ]
    # node_cluster_len = dict_clusterslen[ node_cluster ]
    if node_parent > nodes_df_existing.shape[0]:
        node_cluster = max_clusterid + 1
        node_cluster_len = cluster_len
        cluster_len += 1
    else:
        node_cluster = nodes_df_existing.loc[node_parent]['ClusterId']
        node_cluster_len = nodes_df_existing.loc[node_parent]['Cluster_len']
    # node_lineage = haplotype_df.loc[node]['Lineage']
    node_hap = haplotype_df_updated.loc[node]['haplotype']
    node_samples = haplotype_df_updated.loc[node]['sample_names']
    if meta_flag == True:
        node_time_min = datetime.strftime(haplotype_df_updated.loc[node]['sample_time_min'], '%Y-%m-%d')
        node_time_max = datetime.strftime(haplotype_df_updated.loc[node]['sample_time_max'], '%Y-%m-%d')
        node_locations = haplotype_df_updated.loc[node]['sample_locations']
    else:
        node_time_min = node_time_max = node_locations = ''
    t = [node, node_label, node_time_min, node_time_max, node_cluster, node_cluster_len, node_value, node_hap,node_locations, node_samples]
    t= [str(x) for x in t ]
    f.write(','.join(t)+"\n")

f.close()

nodes_df_new = pd.read_csv(output_path + "nodes_new.csv", sep=',', header=0)
nodes_df_updated = pd.concat([nodes_df_existing, nodes_df_new], axis=0)
nodes_df_updated.to_csv(output_path + "nodes.csv", sep=',', index=False, header=True)

# nodes_df_updated = pd.read_csv(output_path + "nodes.csv", sep=',', header=0)
# edges_df = pd.read_csv(output_path+'edges.csv',sep=',', header=0)
# edges_df_new = pd.read_csv(output_path+'edges_new.csv',sep=',', header=0)

vis_nodes_df = nodes_df_updated[nodes_df_updated['Value']>0]
vis_nodes_list = vis_nodes_df['ClusterId'].tolist()
edges_df_visualize = []
for eachidx in range(edges_df_new.shape[0]):
    # eachidx = 1
    source, target, mut, dis = edges_df_new.iloc[eachidx]
    if source > nodes_df_existing.shape[0]:
        pass
    elif source in vis_nodes_list:
        pass
    else:
        cur_cluster = nodes_df_updated.iloc[source]['ClusterId']
        cur_keynode = nodes_df_updated[ (nodes_df_updated['ClusterId']==cur_cluster) & (nodes_df_updated['Value']==1000)].iloc[0]['Hapid']
        edges_df_visualize.append([cur_keynode, target, mut, dis])

edges_df_visualize = pd.DataFrame(edges_df_visualize)
# edges_df_visualize.columns = ['source', 'target', 'mutations', 'distance']
edges_df = pd.concat([edges_df, edges_df_visualize], axis=0)
edges_df.columns = ['source', 'target', 'mutations', 'distance']
edges_df.to_csv(output_path+'edges.csv',sep=',', index=None)

##write the parent data of new samples
f=open(output_path + "parent_data.txt","w")
t = ['New_hapid', 'New_haplotype', 'New_samples', 'New_time_min', 'New_time_max', 'New_locations', 'parent_hapid', 'parent_haplotype', 'parent_time_min', 'parent_time_max', 'parent_locations', 'parent_samples']
f.write('\t'.join(t)+'\n')
#the rest
i = 0
#the haplotypes merged into the previous network
if haplotype_df_merge.shape[0] > 0:
    merged_haps_list = haplotype_df_merge['hapid_y'].tolist()
    for node in merged_haps_list:
        node_id = haplotype_df_merge['hapid_x'].tolist()
        node_newid = 'newhap_' + str(i)
        node_hap = haplotype_df_new_orig.loc[node]['haplotype']
        node_samples = haplotype_df_new_orig.loc[node]['sample_names']
        #parent
        parent = edges_df[edges_df['target']==node_id].iloc[0]['source']
        parent_id = parent
        parent_hap = haplotype_df_updated.loc[parent]['haplotype']
        parent_samples = haplotype_df_updated.loc[parent]['sample_names']
        if meta_flag == True:
            node_time_min = datetime.strftime(haplotype_df_new_orig.loc[node]['sample_time_min'], '%Y-%m-%d')
            node_time_max = datetime.strftime(haplotype_df_new_orig.loc[node]['sample_time_max'], '%Y-%m-%d')
            node_locations = haplotype_df_new_orig.loc[node]['sample_locations']
            parent_time_min = datetime.strftime(haplotype_df_updated.loc[parent]['sample_time_min'], '%Y-%m-%d')
            parent_time_max = datetime.strftime(haplotype_df_updated.loc[parent]['sample_time_max'], '%Y-%m-%d')
            parent_locations = haplotype_df_updated.loc[parent]['sample_locations']
        else:
            node_time_min = node_time_max = node_locations = ''
            parent_time_min = parent_time_max = parent_locations = ''      
        t = [node_newid, node_hap, node_samples, node_time_min, node_time_max, node_locations, parent_id, parent_hap, parent_time_min, parent_time_max, parent_locations, parent_samples]
        t = [str(x) for x in t ]
        f.write('\t'.join(t)+"\n")
        i += 1

haplotype_df_new = haplotype_df_updated.iloc[-hap_count_batch: ]
for node in haplotype_df_new.index.tolist():
    # node_lineage = haplotype_df.loc[node]['Lineage']
    node_newid = 'newhap_' + str(i)
    node_hap = haplotype_df_updated.loc[node]['haplotype']
    node_samples = haplotype_df_updated.loc[node]['sample_names']

    #parent
    parent = edges_df_new[edges_df_new['target']==node].iloc[0]['source']
    if parent in haplotype_df_new.index:
        parent_id = 'newhap_' + str(haplotype_df_new.index.tolist().index(parent))
    else:
        parent_id = parent
    parent_hap = haplotype_df_updated.loc[parent]['haplotype']
    parent_samples = haplotype_df_updated.loc[parent]['sample_names']
    if meta_flag == True:
        node_time_min = datetime.strftime(haplotype_df_updated.loc[node]['sample_time_min'], '%Y-%m-%d')
        node_time_max = datetime.strftime(haplotype_df_updated.loc[node]['sample_time_max'], '%Y-%m-%d')
        node_locations = haplotype_df_updated.loc[node]['sample_locations']
        parent_time_min = datetime.strftime(haplotype_df_updated.loc[parent]['sample_time_min'], '%Y-%m-%d')
        parent_time_max = datetime.strftime(haplotype_df_updated.loc[parent]['sample_time_max'], '%Y-%m-%d')
        parent_locations = haplotype_df_updated.loc[parent]['sample_locations']
    else:
        node_time_min = node_time_max = node_locations = ''
        parent_time_min = parent_time_max = parent_locations = ''  
    t = [node_newid, node_hap, node_samples, node_time_min, node_time_max, node_locations, parent_id, parent_hap, parent_time_min, parent_time_max, parent_locations, parent_samples]
    t = [str(x) for x in t ]
    f.write('\t'.join(t)+"\n")
    i += 1

f.close()

end_time = time.time()
diff = end_time - beg_time
print(time.ctime())
print(diff, ' s')
print(diff/60, ' min')
print(diff/60/60, ' h')

# if __name__ == '__main__':
#     main()

