#!/usr/bin/env python
# coding=utf-8
# __author__ = 'zp'

import pandas as pd
import numpy as np
from datetime import datetime
from datetime import timedelta
import time
import argparse
import os
import faiss
import random

import networkx as nx
import cdlib
from networkx.algorithms import community
from cdlib import algorithms, viz, NodeClustering

beg_time = time.time()
parser = argparse.ArgumentParser()
parser.add_argument('-input_name', '--input_name', dest='input_name', help="the name of input samples mutation file", required=True)
# parser.add_argument('-output_path', '--output_path', dest='output_path', help="the output path of the results", default= 'venas2_output')
parser.add_argument('-batch_mode', '--batch_mode', dest='batch_mode', help="the number of input samples", default=False)
parser.add_argument('-initial_size', '--initial_size', dest='initial_size', help="the samples count used to construct the initial network when using batch mode", default=10000)
parser.add_argument('-batch_size', '--batch_size', dest='batch_size', help="the samples count which added to the network each time when using batch mode", default=10000)
args = parser.parse_args()

#parameters
input_name = args.input_name
# input_name = 'covid19_example'
# input_name = 'test'
batch_mode = args.batch_mode
initial_size = int(args.initial_size)
batch_size = int(args.batch_size)
# can_lens = int(args.can_lens)

##load in the data
dict_nc2num = {'a':1, 't':2, 'c':3, 'g':4, '-':5, 'o':6}
dict_num2nc = {1:'a', 2:'t', 3:'c', 4:'g', 5:'-', 6:'o'}
data_path = 'rawdata/' 
output_path = 'venas2_output_' + input_name + '/'
if os.path.exists(output_path):
    print('the folder exists! please check the output dir')
else:
    os.makedirs(output_path)

data = pd.read_csv(data_path + str(input_name) +'.txt', sep='\t', header=0)
data = data.fillna('')
if data.shape[1] == 2:
    meta_flag = False
elif data.shape[1] == 4:
    meta_flag = True
else:
    print('File format wwaring!!! Please check the files.')

#functions
def convert_smaples_mutation_into_hap_df_array(data_fn):
    # data_fn = data_base
    haplotype_df_fn = []
    hap_pos_fn = []
    for each_haplotype, group in data_fn.groupby('haplotype'):
        # print(each_haplotype, group)
        sample_names = group['samplename'].tolist()
        sample_count = len(sample_names)
        sample_locations = ';'.join(group['location'].drop_duplicates().tolist())
        sample_times = group['time'].drop_duplicates().tolist()
        # sample_times = [ datetime.strptime(x, '%Y-%m-%d') for x in sample_times]
        sample_times_min = min(sample_times)
        sample_times_max = max(sample_times)
        if each_haplotype == '':
            muts_count = 0
            haplotype_df_fn.append([each_haplotype, muts_count, ';'.join(sample_names), sample_count, sample_locations, sample_times_min, sample_times_max, '','' ])
        else:
            each_haps = each_haplotype.split(';')
            muts_count = len(each_haps)
            hap_pos = []
            hap_value = []
            for each_cur_hap in each_haps:
                each_cur_hapidx = int(each_cur_hap.split('(')[0])
                each_cur_hapletter = each_cur_hap[-2:-1].lower()
                each_value = dict_nc2num[each_cur_hapletter]
                hap_pos.append(each_cur_hapidx)
                hap_value.append(each_value)
            haplotype_df_fn.append([each_haplotype, muts_count, ';'.join(sample_names), sample_count, sample_locations, sample_times_min, sample_times_max, hap_pos, hap_value])
            hap_pos_fn += hap_pos
    haplotype_df_fn = pd.DataFrame(haplotype_df_fn)
    haplotype_df_fn.columns = ['haplotype', 'muts_count', 'sample_names', 'sample_count',  'sample_locations', 'sample_time_min', 'sample_time_max', 'hap_pos', 'hap_value']
    haplotype_df_fn = haplotype_df_fn.sort_values([ 'sample_time_min', 'muts_count','sample_count'], ascending=[True, True, False])
    # haplotype_df = pd.concat( [haplotype_df_first, haplotype_df_rest], axis=0 )
    haplotype_df_fn.index= list(range(haplotype_df_fn.shape[0]))
    hap_count_fn = haplotype_df_fn.shape[0]
    hap_pos_fn = list(sorted(set(hap_pos_fn)))
    ##convert to array
    hap_array_df = pd.DataFrame(0, index = range(hap_count_fn), columns=hap_pos_fn, dtype='int8')
    for each_hapidx in range(hap_count_fn):
        # each_hapidx = 4
        cur_hap_pos = haplotype_df_fn.loc[each_hapidx]['hap_pos']
        cur_hap_value = haplotype_df_fn.loc[each_hapidx]['hap_value']
        if cur_hap_pos == '':
            continue
        else: 
            hap_array_df.loc[each_hapidx, cur_hap_pos] = cur_hap_value
    hap_array = np.array(hap_array_df, dtype='int8')
    return haplotype_df_fn, hap_array, hap_array_df, hap_pos_fn

def find_parent(tmpidx):
    tmpidx2 = edges_df[edges_df[1]==tmpidx].iloc[0][0]
    if tmpidx2 == 0:
        return tmpidx2
    else:
        tmp_hap_pos = haplotype_df_updated.loc[tmpidx2]['hap_pos']
        if set(tmp_hap_pos) < set(cur_hap_pos):
            return tmpidx2            
        else:
            return find_parent(tmpidx2)

def convert_smaples_mutation_into_hap_df_array_nometa(data_fn):
    # data_fn = data_base
    haplotype_df_fn = []
    hap_pos_fn = []
    for each_haplotype, group in data_fn.groupby('haplotype'):
        # print(each_haplotype, group)
        sample_names = group['samplename'].tolist()
        sample_count = len(sample_names)
        # sample_locations = ';'.join(group['location'].drop_duplicates().tolist())
        # sample_times = group['time'].drop_duplicates().tolist()
        # sample_times = [ datetime.strptime(x, '%Y-%m-%d') for x in sample_times]
        # sample_times_min = min(sample_times)
        # sample_times_max = max(sample_times)
        if each_haplotype == '':
            muts_count = 0
            haplotype_df_fn.append([each_haplotype, muts_count, ';'.join(sample_names), sample_count, '','' ])
        else:
            each_haps = each_haplotype.split(';')
            muts_count = len(each_haps)
            hap_pos = []
            hap_value = []
            for each_cur_hap in each_haps:
                each_cur_hapidx = int(each_cur_hap.split('(')[0])
                each_cur_hapletter = each_cur_hap[-2:-1].lower()
                each_value = dict_nc2num[each_cur_hapletter]
                hap_pos.append(each_cur_hapidx)
                hap_value.append(each_value)
            haplotype_df_fn.append([each_haplotype, muts_count, ';'.join(sample_names), sample_count, hap_pos, hap_value])
            hap_pos_fn += hap_pos
    haplotype_df_fn = pd.DataFrame(haplotype_df_fn)
    haplotype_df_fn.columns = ['haplotype', 'muts_count', 'sample_names', 'sample_count',  'hap_pos', 'hap_value']
    haplotype_df_fn = haplotype_df_fn.sort_values([ 'muts_count','sample_count'], ascending=[ True, False])
    # haplotype_df = pd.concat( [haplotype_df_first, haplotype_df_rest], axis=0 )
    haplotype_df_fn.index= list(range(haplotype_df_fn.shape[0]))
    hap_count_fn = haplotype_df_fn.shape[0]
    hap_pos_fn = list(sorted(set(hap_pos_fn)))
    ##convert to array
    hap_array_df = pd.DataFrame(0, index = range(hap_count_fn), columns=hap_pos_fn, dtype='int8')
    for each_hapidx in range(hap_count_fn):
        # each_hapidx = 4
        cur_hap_pos = haplotype_df_fn.loc[each_hapidx]['hap_pos']
        cur_hap_value = haplotype_df_fn.loc[each_hapidx]['hap_value']
        if cur_hap_pos == '':
            continue
        else: 
            hap_array_df.loc[each_hapidx, cur_hap_pos] = cur_hap_value
    hap_array = np.array(hap_array_df, dtype='int8')
    return haplotype_df_fn, hap_array, hap_array_df, hap_pos_fn

if meta_flag == True:
    data.columns = ['samplename', 'haplotype' , 'collection_date', 'location']
    data['time'] = data['collection_date'].apply(lambda x: datetime.strptime(x, '%Y-%m-%d'))
    # data = data.sort_values('time',ascending=True)
    data_count = data.shape[0]

    ##can_lens control the number of top haps considered for each new hap in faiss
    if data_count <= 1000000: 
        can_lens = 200
    elif data_count > 1000000:
        can_lens = 500

    #divide the dataset 
    if batch_mode == False:
        data_base = data
        data_rest = pd.DataFrame()
    else:
        # initial_size = int(data.shape[0]/5)
        data_base = data.iloc[:initial_size ]
        data_rest = data.iloc[initial_size: ]

    """step1: get the haplotype vector form and update the latest hap"""
    haplotype_df_base, hap_array_base, hap_array_df_base, hap_pos_base = convert_smaples_mutation_into_hap_df_array(data_base)

    if haplotype_df_base.iloc[0]['haplotype'] != '':  #this means no ref information
        ref_df = pd.DataFrame(['', 0, 'ref', 1, data.iloc[0]['location'], data.iloc[0]['time'], data.iloc[0]['time'], [], [] ]).T
        ref_df.columns = ['haplotype', 'muts_count', 'sample_names', 'sample_count',  'sample_locations', 'sample_time_min', 'sample_time_max', 'hap_pos', 'hap_value']
        haplotype_df_updated = pd.concat([ref_df, haplotype_df_base ],axis=0)
        haplotype_df_updated.index = range(haplotype_df_updated.shape[0])
        hap_array_df_updated = pd.concat([ pd.DataFrame(0, index=[0], columns=hap_array_df_base.columns.tolist()), hap_array_df_base ], axis=0)
        hap_array_df_updated.index= range(hap_array_df_updated.shape[0])
        hap_pos_updated = hap_pos_base
    else:
        haplotype_df_updated = haplotype_df_base.copy()
        hap_array_df_updated = hap_array_df_base.copy()
        # haplotype_df_updated.index = range(haplotype_df_updated.shape[0])
        # hap_array_df_updated = pd.concat([ pd.DataFrame(0, index=[0], columns=hap_array_df_base.columns.tolist()), hap_array_df_base ], axis=0)
        # hap_array_df_updated.index= range(hap_array_df_updated.shape[0])
        hap_pos_updated = hap_pos_base

    hap_array_df_updated.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)

    """step2 :construct the faiss index and query to search"""
    print('constructing the faiss index and query...')
    xb = hap_array_base.astype(np.int8)
    xq = hap_array_base.astype(np.int8)
    measure = faiss.METRIC_L2
    param = 'Flat'
    # param = 'HNSW64'
    # dim = len(hap_pos_base)
    dim= hap_array_base.shape[1]
    index = faiss.index_factory(dim, param, measure)
    # print(index.is_trained)
    index.add(xb)
    can_dis, can_ids = index.search(xq, can_lens)

    """step 3 identify the parent hap for each hap"""
    edges_df = pd.DataFrame(columns=[0, 1,2,3])
    hap_count_base = haplotype_df_base.shape[0]
    # hap_ids = haplotype_df_updated.index.tolist()[-hap_count_base:]
    hap_ids = haplotype_df_updated.index.tolist()
    for cur_hap_index in range(1, hap_count_base):
    # for cur_hap_index in range(30):
        # cur_hap_index = 153
        cur_hapidx = hap_ids[cur_hap_index]
        # cur_hapidx = 118
        # if cur_hapidx % 1 == 0:
        #     print('processing the ', str(cur_hap_index), '/', str(hap_count_batch),' th hap in the ', str(batch_idx), 'th batch')
        cur_hap, cur_mut_count, cur_sample_names, cur_sample_count, cur_locations, cur_time_min, cur_time_max, cur_hap_pos, cur_hap_value = haplotype_df_updated.loc[cur_hapidx]
        cur_can_ids = can_ids[cur_hap_index].tolist()
        cur_locations = cur_locations.split(';')
        if cur_mut_count == 1: #no choice but the reference hap
            parent_hapidx = 0
            # edges_list.append([parent_hapidx, cur_hapidx, cur_hap, 1])
            edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, cur_hap, 1]).T ], axis=0)
            # type1_list.append([batch_idx, cur_hap_index , cur_hapidx])
            continue
        flag_count = 0
        parent_can_list = []
        dict_parent_can = {}
        # print('processing hap: ', cur_hapidx,'\n' ,'mutation positions: ', cur_hap_pos,'\n', 'values: ', cur_hap_value, '\n', [ x for x in cur_can_ids if x!=-1 ][:20])
        a = hap_array_df_updated.loc[cur_hapidx]
        for each_hap_index in range(1, can_lens):
            # each_hap_index = 1
            each_hapidx = cur_can_ids[each_hap_index]
            if each_hapidx == -1:
                break
            if flag_count >= 5:
                break
            elif each_hapidx >= cur_hapidx:
                continue
            each_hap_pos = haplotype_df_updated.loc[each_hapidx]['hap_pos']
            flag_count += 1
            b = hap_array_df_updated.loc[each_hapidx]
            diff = a.compare(b)
            diff_count = diff.shape[0]
            dict_parent_can[each_hapidx] = diff
            parent_can_list.append( [each_hapidx,  diff_count])
        #     print( 'candidate parents: ', each_hapidx, each_hap_pos, diff )
        # print('\n')
        if len(parent_can_list) > 0: 
            parent_can_df = pd.DataFrame(parent_can_list)
            parent_can_df = parent_can_df.sort_values([1], ascending=True)
            parent_can_df = parent_can_df[parent_can_df[1]==parent_can_df.iloc[0][1]] # select the haps with minimum distance
            if parent_can_df.shape[0] == 1: # this means there is only 1 candidate          
                parent_hapidx = parent_can_df.iloc[0][0]
                parent_diff = dict_parent_can[parent_hapidx]
                # print(parent_hapidx, parent_diff)
                parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                # print(haplotype_df_updated.loc[parent_hapidx], cur_hap)
                tmpdf= parent_diff[parent_diff['other']!=0]
                if tmpdf.shape[0] == 0: # have no back mutation (the simplest)
                    # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                    # type1_count += 1
                    pass       
                else: #have back mutation
                    if tmpdf[tmpdf['self']!=0].shape[0] == tmpdf.shape[0]: # the same pos have multiple mutations
                        # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                        pass
                    else:
                        # type2_list.append([batch_idx,cur_hap_index, cur_hapidx])
                        # print(cur_hapidx, cur_hap, parent_can_df, parent_diff, parent_hap, parent_hapidx) 
                        # print(edges_df)
                        parent_hapidx = find_parent(parent_hapidx)
                        if parent_hapidx not in dict_parent_can.keys():
                            if parent_hapidx == 0:
                                edge_muts = cur_hap
                                edge_diff_count = cur_mut_count
                                edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                                continue
                            else:
                                b = hap_array_df_updated.loc[parent_hapidx]
                                parent_diff = a.compare(b)
                        else:
                            parent_diff = dict_parent_can[parent_hapidx]
                #process the parent hap 
                if parent_hapidx == 0:
                    edge_muts = cur_hap
                    edge_diff_count = cur_mut_count
                else:
                    # parent_diff = dict_parent_can[parent_hapidx]
                    parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                    parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                    edge_muts = []
                    # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], cur_hapidx, parent_hapidx, parent_diff)
                    for each_diffidx in parent_diff.index:
                        # each_diffidx = 3036
                        v1, v2 = parent_diff.loc[each_diffidx]
                        if v1 == 0 and v2 != 0:
                            mut_idx = parent_hap_pos.index(each_diffidx)
                            edge_mut_tmp = parent_hap.split(';')[mut_idx]
                            edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                        elif v1!= 0 and v2 == 0:
                            mut_idx = cur_hap_pos.index(each_diffidx)
                            edge_mut = cur_hap.split(';')[mut_idx]
                        elif v1!= 0 and v2 != 0:
                            mut1_idx = cur_hap_pos.index(each_diffidx)
                            mut1 = cur_hap.split(';')[mut1_idx]
                            mut2_idx = parent_hap_pos.index(each_diffidx)
                            mut2 = parent_hap.split(';')[mut2_idx]
                            edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                        edge_muts.append( edge_mut)
                    edge_diff_count = parent_diff.shape[0]
                    edge_muts = ';'.join(edge_muts)
                    # edges_list.append([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count] )
                edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
            else: #this means there are multiple just select the hap which contain most samples in spatical-tempory 
                # print(cur_hapidx, cur_hap, parent_can_df)
                # type3_list.append([batch_idx,cur_hap_index, cur_hapidx])
                parent_can_df.index = parent_can_df[0]
                parent_can_idxs_list = parent_can_df[0].tolist()
                #remove the candidates with back mutation
                can_back_df = []
                for each_idx in parent_can_idxs_list:
                    # each_idx = parent_can_idxs_list[0]
                    each_diff = dict_parent_can[each_idx]
                    tmpdf= each_diff[each_diff['other']!=0]
                    tmpdf= tmpdf[tmpdf['self']==0]
                    can_back_df.append(tmpdf.shape[0])
                can_back_df = pd.DataFrame(can_back_df)
                can_back_df.index=  parent_can_idxs_list
                can_back_df = can_back_df.sort_values([0], ascending=True)
                can_back_df = can_back_df[can_back_df[0]==can_back_df.iloc[0][0]]
                # print(can_back_df)
                if can_back_df.shape[0] == 1:  ##only 1 candidate do not have back mutation
                    parent_hapidx = can_back_df.index[0]
                else: #multiple do not have back mutation
                    #if have parent-child relationship
                    parent_child_flag = False
                    can_list = can_back_df.index.tolist()
                    can_list = sorted(can_list, reverse=True)
                    # print(cur_hapidx, can_list, edges_df)
                    for i in range(len(can_list)-1):
                        can_parent_hapidx = edges_df[edges_df[1]== can_list[i]].index[0]
                        if can_parent_hapidx in can_list[i+1:]: 
                            parent_hapidx = can_parent_hapidx
                            parent_child_flag = True
                            # continue
                    #if do not have parent-child relationship
                    if parent_child_flag == False:
                        parent_can_df = parent_can_df.loc[can_back_df.index.tolist()]
                        parent_can_df = pd.concat([parent_can_df,  haplotype_df_updated.loc[parent_can_df.index.tolist()][['sample_time_min', 'sample_time_max', 'sample_locations']] ], axis=1)
                        parent_can_idx_selected = []
                        for each_parent_can in parent_can_df.values:
                            # each_parent_can = parent_can_df.values[0]
                            each_parent_hapidx, diff_count, each_time_min, each_time_max, each_locations = each_parent_can
                            each_locations = each_locations.split(';')
                            delta_time = each_time_max - cur_time_min
                            if delta_time.days >= -30 and len(set(cur_locations).intersection(set(each_locations))) >= 1:
                                parent_can_idx_selected.append(each_parent_hapidx)
                        if len(parent_can_idx_selected) == 0: #this means no one hap in spatical-tempory 
                            #select the hap which contain the most samples in all candidates
                             # parent_hapidx = haplotype_df_updated.loc[parent_can_df.index.tolist()].sort_values(['sample_count'], ascending=False).index[0]
                             ##select the hap which is the nearest of time in all candidates
                             parent_hapidx = parent_can_df.sort_values(['sample_time_min'], ascending=False).index[0]
                        elif len(parent_can_idx_selected) == 1: #this means only one just select it
                            parent_hapidx = parent_can_idx_selected[0]
                        elif len(parent_can_idx_selected) > 1: # this means multiple in spatical-tempory just select the the hap which contain the most samples in spatical-tempory 
                            parent_hapidx = haplotype_df_updated.loc[parent_can_idx_selected].sort_values(['sample_count'], ascending=False).index[0]
                parent_diff = dict_parent_can[parent_hapidx]
                parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                if parent_hap == '':
                    edge_muts = cur_hap
                    edge_diff_count = parent_diff.shape[0]
                else:
                    parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                    # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], parent_hapidx, parent_diff)
                    edge_muts = []
                    for each_diffidx in parent_diff.index:
                        v1, v2 = parent_diff.loc[each_diffidx]
                        if v1 == 0 and v2 != 0:                            
                            mut_idx = parent_hap_pos.index(each_diffidx)
                            edge_mut_tmp = parent_hap.split(';')[mut_idx]
                            edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                        elif v1!= 0 and v2 == 0:
                            mut_idx = cur_hap_pos.index(each_diffidx)
                            edge_mut = cur_hap.split(';')[mut_idx]
                        elif v1!= 0 and v2 != 0:
                            mut1_idx = cur_hap_pos.index(each_diffidx)
                            mut1 = cur_hap.split(';')[mut1_idx]
                            mut2_idx = parent_hap_pos.index(each_diffidx)
                            mut2 = parent_hap.split(';')[mut2_idx]
                            edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                        edge_muts.append( edge_mut)
                    edge_diff_count = parent_diff.shape[0]
                    edge_muts = ';'.join(edge_muts)
                edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
        else: #this maybe caused by the can_lens is set too small just re-search for cur hap
            # type4_list.append([batch_idx, cur_hap_index, cur_hapidx])
            print(cur_hapidx ,' Warning') 
            parent_hapidx = 0
            edge_muts = cur_hap
            edge_diff_count = cur_mut_count
            edges_df = pd.concat([ edges_df, pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)

    """
    Adding new samples iterately 
    you can add the same number samples or add the samples in each day/week/month
    """
    if data_rest.shape[0] != 0:
        batch_times = int(data_rest.shape[0] // batch_size)
        for batch_idx in range(0, batch_times+1):
        # for batch_idx in range(0,50):
            # batch_idx = 79
            print('processing the batch ', str(batch_idx),'/' ,str(batch_times))
            if batch_idx == batch_times:
                data_batch = data_rest.iloc[ batch_idx * batch_size : ]
            else:
                data_batch = data_rest[ batch_idx*batch_size: (batch_idx+1)*batch_size ]
            # print(batch_idx, data_batch.shape)
            if data_batch.shape[0] == 0:
                continue
            """step1: get the haplotype vector form and update the latest hap"""
            haplotype_df_batch, hap_array_batch, hap_array_df_batch, hap_pos_batch = convert_smaples_mutation_into_hap_df_array(data_batch)
            # print('batch count ', haplotype_df_batch.shape)
            #merge the existing haps into haplotype_df_update and change it self
            df1 = pd.DataFrame()
            df1['hapid'] = haplotype_df_updated.index
            df1['haplotype'] = haplotype_df_updated['haplotype']
            df2 = pd.DataFrame()
            df2['hapid'] = haplotype_df_batch.index
            df2['haplotype'] = haplotype_df_batch['haplotype']
            haplotype_df_merge = pd.merge(df1, df2, how='inner', on='haplotype')
            if haplotype_df_merge.shape[0] == 0: #there is no samples in existing haps
                pass
            else:
                for each_hap_info in haplotype_df_merge.values:
                    hapid_x, haplotype, hapid_y = each_hap_info
                    haplotype_df_updated.loc[hapid_x, 'sample_names'] += ';' + haplotype_df_batch.loc[hapid_y, 'sample_names']
                    haplotype_df_updated.loc[hapid_x, 'sample_count'] += haplotype_df_batch.loc[hapid_y, 'sample_count']
                    haplotype_df_updated.loc[hapid_x, 'sample_locations'] = ';'.join(set(haplotype_df_updated.loc[hapid_x, 'sample_locations'].split(';')+ haplotype_df_batch.loc[hapid_y, 'sample_locations'].split(';')))
                    haplotype_df_updated.loc[hapid_x, 'sample_time_min'] = min( haplotype_df_updated.loc[hapid_x, 'sample_time_min'], haplotype_df_batch.loc[hapid_y, 'sample_time_min'])
                    haplotype_df_updated.loc[hapid_x, 'sample_time_max'] = max( haplotype_df_updated.loc[hapid_x, 'sample_time_max'], haplotype_df_batch.loc[hapid_y, 'sample_time_max'])
                haplotype_removed_indexs = [x for x in haplotype_df_batch.index.tolist() if x not in haplotype_df_merge['hapid_y'].tolist()]
                haplotype_df_batch = haplotype_df_batch.loc[haplotype_removed_indexs]
                hap_array_df_batch = hap_array_df_batch.loc[haplotype_removed_indexs]
                hap_array_batch = np.array(hap_array_df_batch)
            hap_count_updated = haplotype_df_updated.shape[0]
            hap_count_batch = haplotype_df_batch.shape[0]
            # print(hap_count_batch)
            haplotype_df_batch.index = range(hap_count_updated, hap_count_updated + hap_count_batch )
            hap_array_df_batch.index = range(hap_count_updated, hap_count_updated + hap_count_batch)
            haplotype_df_updated = pd.concat([ haplotype_df_updated, haplotype_df_batch ], axis=0)
            # if hap_count_batch == 0:
            #     continu
            """this is for extremely large dataset / the array df is too big to load
             the order of pos is very important"""
            # extract the haps and pos from hap_array_df to build hap_array_index
            if len(hap_pos_updated) == 0:
                hap_array_df_updated = pd.concat([hap_array_df_updated, hap_array_df_batch],axis=0)
                hap_array_df_index = hap_array_df_updated
                hap_array_index = np.array(hap_array_df_updated)
                hap_pos_updated = hap_pos_batch
                hap_array_df_updated.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
            else:
                hap_pos_df = pd.merge(pd.DataFrame(hap_pos_updated).reset_index(), pd.DataFrame(hap_pos_batch).reset_index(), how='outer', on=0 )
                hap_pos_df_new = hap_pos_df[hap_pos_df['index_x'].isna() ]
                hap_pos_df_existing = hap_pos_df[hap_pos_df['index_y'].isna() ]
                hap_pos_df_overlap = hap_pos_df[(hap_pos_df['index_x'].notna()) & (hap_pos_df['index_y'].notna()) ]
                # print(hap_pos_df_existing.shape, hap_pos_df_overlap.shape, hap_pos_df_new.shape)
                if hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] == 0:
                    continue
                elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] != 0:
                    continue
                elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] == 0:
                    continue
                elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] == 0:
                    hap_pos_overlap = hap_pos_df_overlap[0].tolist()
                    hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
                    hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
                    hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
                    hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
                    hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap ]
                    hap_array_df_index = pd.concat([ hap_array_df_overlap, hap_array_df_batch ], axis=0)
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    hap_pos_updated = hap_pos_overlap
                elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] != 0:
                    hap_pos_existing = hap_pos_df_existing[0].tolist()
                    hap_pos_new = hap_pos_df_new[0].tolist()
                    hap_pos_updated = hap_pos_existing + hap_pos_new
                    hap_array_df_up = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
                    hap_array_df_up = hap_array_df_up.fillna(0).astype('int8') 
                    hap_array_df_up.columns = [ int(x) for x in hap_array_df_up.columns ]
                    hap_array_df_up = pd.concat([hap_array_df_up[hap_pos_existing], pd.DataFrame(0, index=hap_array_df_up.index, columns=hap_pos_new,dtype='int8' ) ],axis=1 )
                    hap_array_df_down = pd.concat([ pd.DataFrame(0, index=hap_array_df_batch.index, columns=hap_pos_existing, dtype='int8' ), hap_array_df_batch[hap_pos_new] ],axis=1 )
                    hap_array_df_index = pd.concat( [hap_array_df_up,hap_array_df_down], axis=0)
                    hap_array_df_batch = hap_array_df_down
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
                elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] != 0:
                    hap_pos_overlap = hap_pos_df_overlap[0].tolist()
                    hap_pos_new = hap_pos_df_new[0].tolist()
                    hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
                    hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
                    hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
                    hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
                    hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
                    hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap + hap_pos_new ]
                    hap_array_df_index = pd.concat([ hap_array_df_up, hap_array_df_batch ], axis=0)
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    hap_pos_updated = hap_pos_overlap + hap_pos_new
                elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] == 0:
                    hap_pos_existing = hap_pos_df_existing[0].tolist()
                    hap_pos_overlap = hap_pos_df_overlap[0].tolist()
                    hap_pos_df_existing_idx = (hap_pos_df_existing['index_x'] + 1).tolist()
                    hap_pos_df_overlap_idx = (hap_pos_df_overlap['index_x'] + 1).tolist()
                    command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_existing_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_existing.txt'
                    os.system(command)
                    command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_overlap_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_overlap.txt'
                    os.system(command)
                    pd.DataFrame(0, index=range(hap_count_batch),columns=range(hap_pos_df_existing.shape[0])).to_csv(output_path+'tmp_zeros_existing.txt',sep='\t', index=False, header=False)
                    command = 'cat '+output_path+'hap_array_df_existing.txt '+output_path+'tmp_zeros_existing.txt > '+output_path+'hap_array_df_existing_merge.txt'
                    os.system(command)
                    hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df_overlap.txt',sep='\t',header=0)
                    hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
                    hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
                    # hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
                    hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
                    hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap ]
                    hap_array_df_index = pd.concat([ hap_array_df_overlap, hap_array_df_batch ], axis=0)
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df_index.txt',sep='\t', header=True, index=None)
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    #keep the hap_array_df_updated
                    command = 'paste ' + output_path+'hap_array_df_existing_merge.txt '+ output_path+'hap_array_df_index.txt > ' + output_path+'hap_array_df.txt'
                    os.system(command)
                    hap_pos_updated = hap_pos_existing + hap_pos_overlap
                    os.remove(output_path+'hap_array_df_existing.txt')
                    os.remove(output_path+'hap_array_df_overlap.txt')
                    os.remove(output_path+'tmp_zeros_existing.txt')
                    os.remove(output_path+'hap_array_df_existing_merge.txt')
                    os.remove(output_path+'hap_array_df_index.txt')
                elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] != 0:
                    hap_pos_existing = hap_pos_df_existing[0].tolist()
                    hap_pos_overlap = hap_pos_df_overlap[0].tolist()
                    hap_pos_new = hap_pos_df_new[0].tolist()
                    hap_pos_df_existing_idx = (hap_pos_df_existing['index_x'] + 1).tolist()
                    hap_pos_df_overlap_idx = (hap_pos_df_overlap['index_x'] + 1).tolist()
                    # print(hap_pos_df, hap_pos_df_existing, hap_pos_df_overlap, hap_pos_df_new)
                    command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_existing_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_existing.txt'
                    os.system(command)
                    command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_overlap_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_overlap.txt'
                    os.system(command)
                    pd.DataFrame(0, index=range(hap_count_batch),columns=range(hap_pos_df_existing.shape[0])).to_csv(output_path+'tmp_zeros_existing.txt',sep='\t', index=False, header=False)
                    # cat hap_array_df_existing.txt tmp_zeros_existing.txt > hap_array_df_existing_merge.txt
                    command = 'cat '+output_path+'hap_array_df_existing.txt '+output_path+'tmp_zeros_existing.txt > '+output_path+'hap_array_df_existing_merge.txt'
                    os.system(command)
                    # hap_array_df_overlap = pd.read_csv(output_path+'haplotype_array_df_tmp.txt',sep='\t',header=0, usecols= [ str(x) for x in hap_pos_overlap], dtype='int8')
                    hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df_overlap.txt',sep='\t',header=0)
                    hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
                    # print(cur_hapidx, hap_array_df_overlap, hap_pos_overlap,hap_pos_df)
                    hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
                    hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
                    hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap + hap_pos_new ]
                    hap_array_df_index = pd.concat([ hap_array_df_up, hap_array_df_batch ], axis=0)
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df_index.txt',sep='\t', header=True, index=None)
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    #keep the hap_array_df_updated
                    # paste hap_array_df_existing_merge.txt hap_array_df_index.txt >hap_array_df.txt
                    command = 'paste ' + output_path+'hap_array_df_existing_merge.txt '+ output_path+'hap_array_df_index.txt > ' + output_path+'hap_array_df.txt'
                    os.system(command)
                    # t = pd.read_csv(output_path+'hap_array_df.txt',sep='\t')
                    hap_pos_updated = hap_pos_existing + hap_pos_overlap + hap_pos_new
                    os.remove(output_path+'hap_array_df_existing.txt')
                    os.remove(output_path+'hap_array_df_overlap.txt')
                    os.remove(output_path+'tmp_zeros_existing.txt')
                    os.remove(output_path+'hap_array_df_existing_merge.txt')
                    os.remove(output_path+'hap_array_df_index.txt')

            """step2 :construct the faiss index and query to search"""
            print('constructing the faiss index and query...')
            xb = hap_array_index.astype(np.int8)
            xq = hap_array_batch.astype(np.int8)
            measure = faiss.METRIC_L2
            param = 'Flat'
            # param = 'HNSW64'
            # dim = len(hap_pos_batch)
            dim= hap_array_batch.shape[1]
            index = faiss.index_factory(dim, param, measure)
            # print(xb, xq, xb.shape, xq.shape, dim,index)
            # print(index.is_trained)
            index.add(xb)
            can_dis, can_ids = index.search(xq, can_lens)

            """step 3 identify the parent hap for each hap"""
            # hap_count_batch = haplotype_df_batch.shape[0]
            hap_ids = haplotype_df_updated.index.tolist()[-hap_count_batch:]
            for cur_hap_index in range(hap_count_batch):
            # for cur_hap_index in range(30):
                # cur_hap_index = 0
                cur_hapidx = hap_ids[cur_hap_index]
                # cur_hapidx = 118
                # if cur_hapidx % 1 == 0:
                #     print('processing the ', str(cur_hap_index), '/', str(hap_count_batch),' th hap in the ', str(batch_idx), 'th batch')
                cur_hap, cur_mut_count, cur_sample_names, cur_sample_count, cur_locations, cur_time_min, cur_time_max, cur_hap_pos, cur_hap_value = haplotype_df_updated.loc[cur_hapidx]
                cur_can_ids = can_ids[cur_hap_index].tolist()
                cur_locations = cur_locations.split(';')
                if cur_mut_count == 1: #no choice but the reference hap
                    parent_hapidx = 0
                    # edges_list.append([parent_hapidx, cur_hapidx, cur_hap, 1])
                    edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, cur_hap, 1]).T ], axis=0)
                    # type1_list.append([batch_idx, cur_hap_index , cur_hapidx])
                    continue
                flag_count = 0
                parent_can_list = []
                dict_parent_can = {}
                a = hap_array_df_index.loc[cur_hapidx]
                # print('processing hap: ', cur_hapidx,'\n' ,'mutation positions: ', cur_hap_pos,'\n', 'values: ', cur_hap_value, '\n', [ x for x in cur_can_ids if x!=-1 ][:20],haplotype_df_updated.shape, hap_array_df_index.shape)
                for each_hap_index in range(1, can_lens):
                    # each_hap_index = 1
                    each_hapidx = cur_can_ids[each_hap_index]
                    if each_hapidx == -1:
                        break
                    if flag_count >= 5:
                        break
                    elif each_hapidx >= cur_hapidx:
                        continue
                    each_hap_pos = haplotype_df_updated.loc[each_hapidx]['hap_pos']
                    flag_count += 1
                    b = hap_array_df_index.loc[each_hapidx]
                    diff = a.compare(b)
                    diff_count = diff.shape[0]
                    dict_parent_can[each_hapidx] = diff
                    parent_can_list.append( [each_hapidx,  diff_count])
                #     print( 'candidate parents: ', each_hapidx, each_hap_pos, diff,b )
                # print('\n')
                if len(parent_can_list) > 0: 
                    parent_can_df = pd.DataFrame(parent_can_list)
                    parent_can_df = parent_can_df.sort_values([1], ascending=True)
                    parent_can_df = parent_can_df[parent_can_df[1]==parent_can_df.iloc[0][1]] # select the haps with minimum distance
                    if parent_can_df.shape[0] == 1: # this means there is only 1 candidate          
                        parent_hapidx = parent_can_df.iloc[0][0]
                        parent_diff = dict_parent_can[parent_hapidx]
                        # print(parent_hapidx, parent_diff)
                        parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                        # print(haplotype_df_updated.loc[parent_hapidx], cur_hap)
                        tmpdf= parent_diff[parent_diff['other']!=0]
                        if tmpdf.shape[0] == 0: # have no back mutation (the simplest)
                            # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                            # type1_count += 1         
                            pass       
                        else: #have back mutation
                            if tmpdf[tmpdf['self']!=0].shape[0] == tmpdf.shape[0]: # the same pos have multiple mutations
                                # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                                pass
                            else:
                                # type2_list.append([batch_idx,cur_hap_index, cur_hapidx])
                                # print(cur_hapidx, cur_hap, parent_can_df, parent_diff, parent_hap, parent_hapidx) 
                                # print(edges_df)
                                parent_hapidx = find_parent(parent_hapidx)
                                if parent_hapidx not in dict_parent_can.keys():
                                    if parent_hapidx == 0:
                                        edge_muts = cur_hap
                                        edge_diff_count = cur_mut_count
                                        edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                                        continue
                                    else:
                                        b = hap_array_df_index.loc[parent_hapidx]
                                        parent_diff = a.compare(b)
                                else:
                                    parent_diff = dict_parent_can[parent_hapidx]
                        #process the parent hap 
                        if parent_hapidx == 0:
                            edge_muts = cur_hap
                            edge_diff_count = cur_mut_count
                        else:
                            # parent_diff = dict_parent_can[parent_hapidx]
                            parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                            parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                            edge_muts = []
                            # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], cur_hapidx, parent_hapidx, parent_diff)
                            for each_diffidx in parent_diff.index:
                                # each_diffidx = 3036
                                v1, v2 = parent_diff.loc[each_diffidx]
                                if v1 == 0 and v2 != 0:
                                    mut_idx = parent_hap_pos.index(each_diffidx)
                                    edge_mut_tmp = parent_hap.split(';')[mut_idx]
                                    edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                                elif v1!= 0 and v2 == 0:
                                    mut_idx = cur_hap_pos.index(each_diffidx)
                                    edge_mut = cur_hap.split(';')[mut_idx]
                                elif v1!= 0 and v2 != 0:
                                    mut1_idx = cur_hap_pos.index(each_diffidx)
                                    mut1 = cur_hap.split(';')[mut1_idx]
                                    mut2_idx = parent_hap_pos.index(each_diffidx)
                                    mut2 = parent_hap.split(';')[mut2_idx]
                                    edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                                edge_muts.append( edge_mut)
                            edge_diff_count = parent_diff.shape[0]
                            edge_muts = ';'.join(edge_muts)
                            # edges_list.append([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count] )
                        edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                    else: #this means there are multiple just select the hap which contain most samples in spatical-tempory 
                        # print(cur_hapidx, cur_hap, parent_can_df)
                        # type3_list.append([batch_idx,cur_hap_index, cur_hapidx])
                        parent_can_df.index = parent_can_df[0]
                        parent_can_idxs_list = parent_can_df[0].tolist()
                        #remove the candidates with back mutation
                        can_back_df = []
                        for each_idx in parent_can_idxs_list:
                            # each_idx = parent_can_idxs_list[0]
                            each_diff = dict_parent_can[each_idx]
                            tmpdf= each_diff[each_diff['other']!=0]
                            tmpdf= tmpdf[tmpdf['self']==0]
                            can_back_df.append(tmpdf.shape[0])
                        can_back_df = pd.DataFrame(can_back_df)
                        can_back_df.index=  parent_can_idxs_list
                        can_back_df = can_back_df.sort_values([0], ascending=True)
                        can_back_df = can_back_df[can_back_df[0]==can_back_df.iloc[0][0]]
                        if can_back_df.shape[0] == 1:  ##only 1 candidate do not have back mutation
                            parent_hapidx = can_back_df.index[0]
                        else: #multiple do not have back mutation
                            #if have parent-child relationship
                            parent_child_flag = False
                            can_list = can_back_df.index.tolist()
                            can_list = sorted(can_list, reverse=True)
                            for i in range(len(can_list)-1):
                                can_parent_hapidx = edges_df[edges_df[1]== can_list[i]].index[0]
                                if can_parent_hapidx in can_list[i+1:]: 
                                    parent_hapidx = can_parent_hapidx
                                    parent_child_flag = True
                                    # continue
                            #if do not have parent-child relationship
                            if parent_child_flag == False:
                                parent_can_df = parent_can_df.loc[can_back_df.index.tolist()]
                                parent_can_df = pd.concat([parent_can_df,  haplotype_df_updated.loc[parent_can_df.index.tolist()][['sample_time_min', 'sample_time_max', 'sample_locations']] ], axis=1)
                                parent_can_idx_selected = []
                                for each_parent_can in parent_can_df.values:
                                    # each_parent_can = parent_can_df.values[0]
                                    each_parent_hapidx, diff_count, each_time_min, each_time_max, each_locations = each_parent_can
                                    each_locations = each_locations.split(';')
                                    delta_time = each_time_max - cur_time_min
                                    if delta_time.days >= -30 and len(set(cur_locations).intersection(set(each_locations))) >= 1:
                                        parent_can_idx_selected.append(each_parent_hapidx)
                                if len(parent_can_idx_selected) == 0: #this means no one hap in spatical-tempory 
                                    #select the hap which contain the most samples in all candidates
                                     # parent_hapidx = haplotype_df_updated.loc[parent_can_df.index.tolist()].sort_values(['sample_count'], ascending=False).index[0]
                                     ##select the hap which is the nearest of time in all candidates
                                     parent_hapidx = parent_can_df.sort_values(['sample_time_min'], ascending=False).index[0]
                                elif len(parent_can_idx_selected) == 1: #this means only one just select it
                                    parent_hapidx = parent_can_idx_selected[0]
                                elif len(parent_can_idx_selected) > 1: # this means multiple in spatical-tempory just select the the hap which contain the most samples in spatical-tempory 
                                    parent_hapidx = haplotype_df_updated.loc[parent_can_idx_selected].sort_values(['sample_count'], ascending=False).index[0]
                        parent_diff = dict_parent_can[parent_hapidx]
                        parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                        if parent_hap == '':
                            edge_muts = cur_hap
                            edge_diff_count = parent_diff.shape[0]
                        else:
                            parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                            # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], parent_hapidx, parent_diff)
                            edge_muts = []
                            # print(hap_array_index.shape)
                            # print(parent_hap_pos, parent_hapidx, parent_diff, haplotype_df_updated.shape, dict_parent_can, haplotype_df_batch.shape)
                            for each_diffidx in parent_diff.index:
                                v1, v2 = parent_diff.loc[each_diffidx]
                                if v1 == 0 and v2 != 0:                            
                                    mut_idx = parent_hap_pos.index(each_diffidx)
                                    edge_mut_tmp = parent_hap.split(';')[mut_idx]
                                    edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                                elif v1!= 0 and v2 == 0:
                                    mut_idx = cur_hap_pos.index(each_diffidx)
                                    edge_mut = cur_hap.split(';')[mut_idx]
                                elif v1!= 0 and v2 != 0:
                                    mut1_idx = cur_hap_pos.index(each_diffidx)
                                    mut1 = cur_hap.split(';')[mut1_idx]
                                    mut2_idx = parent_hap_pos.index(each_diffidx)
                                    mut2 = parent_hap.split(';')[mut2_idx]
                                    edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                                edge_muts.append( edge_mut)
                            edge_diff_count = parent_diff.shape[0]
                            edge_muts = ';'.join(edge_muts)
                        edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                else: #this maybe caused by the can_lens is set too small just re-search for cur hap
                    # type4_list.append([batch_idx, cur_hap_index, cur_hapidx])
                    print(cur_hapidx ,' Warning') 
                    parent_hapidx = 0
                    edge_muts = cur_hap
                    edge_diff_count = cur_mut_count
                    edges_df = pd.concat([ edges_df, pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
else:
    data.columns = ['samplename', 'haplotype']
    data_count = data.shape[0]
    ##can_lens control the number of top haps considered for each new hap in faiss
    if data_count <= 1000000: 
        can_lens = 200
    elif data_count > 1000000:
        can_lens = 500

    if batch_mode == False:
        data_base = data
        data_rest = pd.DataFrame()
    else:
        # initial_size = int(data.shape[0]/5)
        data_base = data.iloc[:initial_size ]
        data_rest = data.iloc[initial_size: ]

    """step1: get the haplotype vector form and update the latest hap"""
    haplotype_df_base, hap_array_base, hap_array_df_base, hap_pos_base = convert_smaples_mutation_into_hap_df_array_nometa(data_base)

    if haplotype_df_base.iloc[0]['haplotype'] != '':  #this means no ref information
        ref_df = pd.DataFrame(['', 0, 'ref', 1, [], [] ]).T
        ref_df.columns = ['haplotype', 'muts_count', 'sample_names', 'sample_count', 'hap_pos', 'hap_value']
        haplotype_df_updated = pd.concat([ref_df, haplotype_df_base ],axis=0)
        haplotype_df_updated.index = range(haplotype_df_updated.shape[0])
        hap_array_df_updated = pd.concat([ pd.DataFrame(0, index=[0], columns=hap_array_df_base.columns.tolist()), hap_array_df_base ], axis=0)
        hap_array_df_updated.index= range(hap_array_df_updated.shape[0])
        hap_pos_updated = hap_pos_base
    else:
        haplotype_df_updated = haplotype_df_base.copy()
        hap_array_df_updated = hap_array_df_base.copy()
        # haplotype_df_updated.index = range(haplotype_df_updated.shape[0])
        # hap_array_df_updated = pd.concat([ pd.DataFrame(0, index=[0], columns=hap_array_df_base.columns.tolist()), hap_array_df_base ], axis=0)
        # hap_array_df_updated.index= range(hap_array_df_updated.shape[0])
        hap_pos_updated = hap_pos_base

    hap_array_df_updated.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)

    """step2 :construct the faiss index and query to search"""
    print('constructing the faiss index and query...')
    xb = hap_array_base.astype(np.int8)
    xq = hap_array_base.astype(np.int8)
    measure = faiss.METRIC_L2
    param = 'Flat'
    # param = 'HNSW64'
    # dim = len(hap_pos_base)
    dim= hap_array_base.shape[1]
    index = faiss.index_factory(dim, param, measure)
    # print(index.is_trained)
    index.add(xb)
    can_dis, can_ids = index.search(xq, can_lens)

    """step 3 identify the parent hap for each hap"""
    edges_df = pd.DataFrame(columns=[0, 1,2,3])
    hap_count_base = haplotype_df_base.shape[0]
    # hap_ids = haplotype_df_updated.index.tolist()[-hap_count_base:]
    hap_ids = haplotype_df_updated.index.tolist()
    for cur_hap_index in range(1, hap_count_base):
    # for cur_hap_index in range(30):
        # cur_hap_index = 153
        cur_hapidx = hap_ids[cur_hap_index]
        # cur_hapidx = 118
        # if cur_hapidx % 1 == 0:
        #     print('processing the ', str(cur_hap_index), '/', str(hap_count_batch),' th hap in the ', str(batch_idx), 'th batch')
        cur_hap, cur_mut_count, cur_sample_names, cur_sample_count, cur_hap_pos, cur_hap_value = haplotype_df_updated.loc[cur_hapidx]
        cur_can_ids = can_ids[cur_hap_index].tolist()
        # cur_locations = cur_locations.split(';')
        if cur_mut_count == 1: #no choice but the reference hap
            parent_hapidx = 0
            # edges_list.append([parent_hapidx, cur_hapidx, cur_hap, 1])
            edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, cur_hap, 1]).T ], axis=0)
            # type1_list.append([batch_idx, cur_hap_index , cur_hapidx])
            continue
        flag_count = 0
        parent_can_list = []
        dict_parent_can = {}
        # print('processing hap: ', cur_hapidx,'\n' ,'mutation positions: ','\n', 'values: ', '\n', [ x for x in cur_can_ids if x!=-1 ][:20])
        a = hap_array_df_updated.loc[cur_hapidx]
        for each_hap_index in range(1, can_lens):
            # each_hap_index = 1
            each_hapidx = cur_can_ids[each_hap_index]
            if each_hapidx == -1:
                break
            if flag_count >= 5:
                break
            elif each_hapidx >= cur_hapidx:
                continue
            each_hap_pos = haplotype_df_updated.loc[each_hapidx]['hap_pos']
            flag_count += 1
            b = hap_array_df_updated.loc[each_hapidx]
            diff = a.compare(b)
            diff_count = diff.shape[0]
            dict_parent_can[each_hapidx] = diff
            parent_can_list.append( [each_hapidx,  diff_count])
            # print( 'candidate parents: ', each_hapidx, diff,diff.shape )
        # print('\n')
        if len(parent_can_list) > 0: 
            parent_can_df = pd.DataFrame(parent_can_list)
            parent_can_df = parent_can_df.sort_values([1], ascending=True)
            parent_can_df = parent_can_df[parent_can_df[1]==parent_can_df.iloc[0][1]] # select the haps with minimum distance
            if parent_can_df.shape[0] == 1: # this means there is only 1 candidate          
                parent_hapidx = parent_can_df.iloc[0][0]
                parent_diff = dict_parent_can[parent_hapidx]
                # print(parent_hapidx, parent_diff)
                parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                # print(haplotype_df_updated.loc[parent_hapidx], cur_hap)
                tmpdf= parent_diff[parent_diff['other']!=0]
                if tmpdf.shape[0] == 0: # have no back mutation (the simplest)
                    # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                    # type1_count += 1
                    pass       
                else: #have back mutation
                    if tmpdf[tmpdf['self']!=0].shape[0] == tmpdf.shape[0]: # the same pos have multiple mutations
                        # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                        pass
                    else:
                        # pass
                        # type2_list.append([batch_idx,cur_hap_index, cur_hapidx])
                        parent_hapidx = find_parent(parent_hapidx)
                        if parent_hapidx not in dict_parent_can.keys():
                            if parent_hapidx == 0:
                                edge_muts = cur_hap
                                edge_diff_count = cur_mut_count
                                edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                                continue
                            else:
                                b = hap_array_df_updated.loc[parent_hapidx]
                                parent_diff = a.compare(b)
                        else:
                            parent_diff = dict_parent_can[parent_hapidx]
                #process the parent hap 
                if parent_hapidx == 0:
                    edge_muts = cur_hap
                    edge_diff_count = cur_mut_count
                else:
                    # parent_diff = dict_parent_can[parent_hapidx]
                    parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                    parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                    edge_muts = []
                    # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], cur_hapidx, parent_hapidx, parent_diff)
                    for each_diffidx in parent_diff.index:
                        # each_diffidx = 3036
                        v1, v2 = parent_diff.loc[each_diffidx]
                        if v1 == 0 and v2 != 0:
                            mut_idx = parent_hap_pos.index(each_diffidx)
                            edge_mut_tmp = parent_hap.split(';')[mut_idx]
                            edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                        elif v1!= 0 and v2 == 0:
                            mut_idx = cur_hap_pos.index(each_diffidx)
                            edge_mut = cur_hap.split(';')[mut_idx]
                        elif v1!= 0 and v2 != 0:
                            mut1_idx = cur_hap_pos.index(each_diffidx)
                            mut1 = cur_hap.split(';')[mut1_idx]
                            mut2_idx = parent_hap_pos.index(each_diffidx)
                            mut2 = parent_hap.split(';')[mut2_idx]
                            edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                        edge_muts.append( edge_mut)
                    edge_diff_count = parent_diff.shape[0]
                    edge_muts = ';'.join(edge_muts)
                    # edges_list.append([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count] )
                edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
            else: #this means there are multiple just select the hap which contain most samples in spatical-tempory 
                # print(cur_hapidx, cur_hap, parent_can_df)
                # type3_list.append([batch_idx,cur_hap_index, cur_hapidx])
                parent_can_df.index = parent_can_df[0]
                parent_can_idxs_list = parent_can_df[0].tolist()
                #remove the candidates with back mutation
                can_back_df = []
                for each_idx in parent_can_idxs_list:
                    # each_idx = parent_can_idxs_list[0]
                    each_diff = dict_parent_can[each_idx]
                    tmpdf= each_diff[each_diff['other']!=0]
                    tmpdf= tmpdf[tmpdf['self']==0]
                    can_back_df.append(tmpdf.shape[0])
                can_back_df = pd.DataFrame(can_back_df)
                can_back_df.index=  parent_can_idxs_list
                can_back_df = can_back_df.sort_values([0], ascending=True)
                can_back_df = can_back_df[can_back_df[0]==can_back_df.iloc[0][0]]
                # print(can_back_df)
                # can_back_df = parent_can_df
                if can_back_df.shape[0] == 1:  ##only 1 candidate do not have back mutation
                    parent_hapidx = can_back_df.index[0]
                else: #multiple do not have back mutation
                    #if have parent-child relationship
                    parent_child_flag = False
                    can_list = can_back_df.index.tolist()
                    can_list = sorted(can_list, reverse=True)
                    # print(cur_hapidx, can_list, edges_df)
                    for i in range(len(can_list)-1):
                        can_parent_hapidx = edges_df[edges_df[1]== can_list[i]].index[0]
                        if can_parent_hapidx in can_list[i+1:]: 
                            parent_hapidx = can_parent_hapidx
                            parent_child_flag = True
                            # continue
                    #if do not have parent-child relationship
                    if parent_child_flag == False:
                        parent_can_df = parent_can_df.loc[can_back_df.index.tolist()]
                        parent_can_idx_selected = parent_can_df[0].values.tolist()
                        # parent_can_df = pd.concat([parent_can_df,  haplotype_df_updated.loc[parent_can_df.index.tolist()][['sample_time_min', 'sample_time_max', 'sample_locations']] ], axis=1)
                        # parent_can_idx_selected = []
                        # for each_parent_can in parent_can_df.values:
                        #     # each_parent_can = parent_can_df.values[0]
                        #     each_parent_hapidx, diff_count, each_time_min, each_time_max, each_locations = each_parent_can
                        #     each_locations = each_locations.split(';')
                        #     delta_time = each_time_max - cur_time_min
                        #     if delta_time.days >= -30 and len(set(cur_locations).intersection(set(each_locations))) >= 1:
                        #         parent_can_idx_selected.append(each_parent_hapidx)
                        if len(parent_can_idx_selected) == 0: #this means no one hap in spatical-tempory 
                            #select the hap which contain the most samples in all candidates
                             # parent_hapidx = haplotype_df_updated.loc[parent_can_df.index.tolist()].sort_values(['sample_count'], ascending=False).index[0]
                             ##select the hap which is the nearest of time in all candidates
                             # parent_hapidx = parent_can_df.sort_values(['sample_time_min'], ascending=False).index[0]
                             parent_hapidx = parent_can_df.sort_values(['sample_count'], ascending=False).index[0]
                        elif len(parent_can_idx_selected) == 1: #this means only one just select it
                            parent_hapidx = parent_can_idx_selected[0]
                        elif len(parent_can_idx_selected) > 1: # this means multiple in spatical-tempory just select the the hap which contain the most samples in spatical-tempory 
                            parent_hapidx = haplotype_df_updated.loc[parent_can_idx_selected].sort_values(['sample_count'], ascending=False).index[0]
                parent_diff = dict_parent_can[parent_hapidx]
                parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                if parent_hap == '':
                    edge_muts = cur_hap
                    edge_diff_count = parent_diff.shape[0]
                else:
                    parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                    # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], parent_hapidx, parent_diff)
                    edge_muts = []
                    for each_diffidx in parent_diff.index:
                        v1, v2 = parent_diff.loc[each_diffidx]
                        if v1 == 0 and v2 != 0:                            
                            mut_idx = parent_hap_pos.index(each_diffidx)
                            edge_mut_tmp = parent_hap.split(';')[mut_idx]
                            edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                        elif v1!= 0 and v2 == 0:
                            mut_idx = cur_hap_pos.index(each_diffidx)
                            edge_mut = cur_hap.split(';')[mut_idx]
                        elif v1!= 0 and v2 != 0:
                            mut1_idx = cur_hap_pos.index(each_diffidx)
                            mut1 = cur_hap.split(';')[mut1_idx]
                            mut2_idx = parent_hap_pos.index(each_diffidx)
                            mut2 = parent_hap.split(';')[mut2_idx]
                            edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                        edge_muts.append( edge_mut)
                    edge_diff_count = parent_diff.shape[0]
                    edge_muts = ';'.join(edge_muts)
                edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
        else: #this maybe caused by the can_lens is set too small just re-search for cur hap
            # type4_list.append([batch_idx, cur_hap_index, cur_hapidx])
            print(cur_hapidx ,' Warning') 
            parent_hapidx = 0
            edge_muts = cur_hap
            edge_diff_count = cur_mut_count
            edges_df = pd.concat([ edges_df, pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)

    """
    Adding new samples iterately 
    you can add the same number samples or add the samples in each day/week/month
    """
    if data_rest.shape[0] != 0:
        batch_times = int(data_rest.shape[0] // batch_size)
        for batch_idx in range(0, batch_times+1):
        # for batch_idx in range(0,50):
            # batch_idx = 2
            print('processing the batch ', str(batch_idx),'/' ,str(batch_times))
            if batch_idx == batch_times:
                data_batch = data_rest.iloc[ batch_idx * batch_size : ]
            else:
                data_batch = data_rest[ batch_idx*batch_size: (batch_idx+1)*batch_size ]
            # print(batch_idx, data_batch.shape)
            if data_batch.shape[0] == 0:
                continue
            """step1: get the haplotype vector form and update the latest hap"""
            haplotype_df_batch, hap_array_batch, hap_array_df_batch, hap_pos_batch = convert_smaples_mutation_into_hap_df_array_nometa(data_batch)
            # print('batch count ', haplotype_df_batch.shape)
            #merge the existing haps into haplotype_df_update and change it self
            df1 = pd.DataFrame()
            df1['hapid'] = haplotype_df_updated.index
            df1['haplotype'] = haplotype_df_updated['haplotype']
            df2 = pd.DataFrame()
            df2['hapid'] = haplotype_df_batch.index
            df2['haplotype'] = haplotype_df_batch['haplotype']
            haplotype_df_merge = pd.merge(df1, df2, how='inner', on='haplotype')
            if haplotype_df_merge.shape[0] == 0: #there is no samples in existing haps
                pass
            else:
                for each_hap_info in haplotype_df_merge.values:
                    hapid_x, haplotype, hapid_y = each_hap_info
                    haplotype_df_updated.loc[hapid_x, 'sample_names'] += ';' + haplotype_df_batch.loc[hapid_y, 'sample_names']
                    haplotype_df_updated.loc[hapid_x, 'sample_count'] += haplotype_df_batch.loc[hapid_y, 'sample_count']
                    # haplotype_df_updated.loc[hapid_x, 'sample_locations'] = ';'.join(set(haplotype_df_updated.loc[hapid_x, 'sample_locations'].split(';')+ haplotype_df_batch.loc[hapid_y, 'sample_locations'].split(';')))
                    # haplotype_df_updated.loc[hapid_x, 'sample_time_min'] = min( haplotype_df_updated.loc[hapid_x, 'sample_time_min'], haplotype_df_batch.loc[hapid_y, 'sample_time_min'])
                    # haplotype_df_updated.loc[hapid_x, 'sample_time_max'] = max( haplotype_df_updated.loc[hapid_x, 'sample_time_max'], haplotype_df_batch.loc[hapid_y, 'sample_time_max'])
                haplotype_removed_indexs = [x for x in haplotype_df_batch.index.tolist() if x not in haplotype_df_merge['hapid_y'].tolist()]
                haplotype_df_batch = haplotype_df_batch.loc[haplotype_removed_indexs]
                hap_array_df_batch = hap_array_df_batch.loc[haplotype_removed_indexs]
                hap_array_batch = np.array(hap_array_df_batch)

            hap_count_updated = haplotype_df_updated.shape[0]
            hap_count_batch = haplotype_df_batch.shape[0]
            # print(hap_count_batch)
            haplotype_df_batch.index = range(hap_count_updated, hap_count_updated + hap_count_batch )
            hap_array_df_batch.index = range(hap_count_updated, hap_count_updated + hap_count_batch)
            haplotype_df_updated = pd.concat([ haplotype_df_updated, haplotype_df_batch ], axis=0)
            # if hap_count_batch == 0:
            #     continu
            """this is for extremely large dataset / the array df is too big to load
             the order of pos is very important"""
            # extract the haps and pos from hap_array_df to build hap_array_index
            if len(hap_pos_updated) == 0:
                hap_array_df_updated = pd.concat([hap_array_df_updated, hap_array_df_batch],axis=0)
                hap_array_df_index = hap_array_df_updated
                hap_array_index = np.array(hap_array_df_updated)
                hap_pos_updated = hap_pos_batch
                hap_array_df_updated.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
            else:
                hap_pos_df = pd.merge(pd.DataFrame(hap_pos_updated).reset_index(), pd.DataFrame(hap_pos_batch).reset_index(), how='outer', on=0 )
                hap_pos_df_new = hap_pos_df[hap_pos_df['index_x'].isna() ]
                hap_pos_df_existing = hap_pos_df[hap_pos_df['index_y'].isna() ]
                hap_pos_df_overlap = hap_pos_df[(hap_pos_df['index_x'].notna()) & (hap_pos_df['index_y'].notna()) ]
                # print(hap_pos_df_existing.shape, hap_pos_df_overlap.shape, hap_pos_df_new.shape)
                if hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] == 0:
                    continue
                elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] != 0:
                    continue
                elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] == 0:
                    continue
                elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] == 0:
                    hap_pos_overlap = hap_pos_df_overlap[0].tolist()
                    hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
                    hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
                    hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
                    hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
                    hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap ]
                    hap_array_df_index = pd.concat([ hap_array_df_overlap, hap_array_df_batch ], axis=0)
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    hap_pos_updated = hap_pos_overlap
                elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] == 0 and hap_pos_df_new.shape[0] != 0:
                    hap_pos_existing = hap_pos_df_existing[0].tolist()
                    hap_pos_new = hap_pos_df_new[0].tolist()
                    hap_pos_updated = hap_pos_existing + hap_pos_new
                    hap_array_df_up = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
                    hap_array_df_up = hap_array_df_up.fillna(0).astype('int8') 
                    hap_array_df_up.columns = [ int(x) for x in hap_array_df_up.columns ]
                    hap_array_df_up = pd.concat([hap_array_df_up[hap_pos_existing], pd.DataFrame(0, index=hap_array_df_up.index, columns=hap_pos_new,dtype='int8' ) ],axis=1 )
                    hap_array_df_down = pd.concat([ pd.DataFrame(0, index=hap_array_df_batch.index, columns=hap_pos_existing, dtype='int8' ), hap_array_df_batch[hap_pos_new] ],axis=1 )
                    hap_array_df_index = pd.concat( [hap_array_df_up,hap_array_df_down], axis=0)
                    hap_array_df_batch = hap_array_df_down
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
                elif hap_pos_df_existing.shape[0] == 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] != 0:
                    hap_pos_overlap = hap_pos_df_overlap[0].tolist()
                    hap_pos_new = hap_pos_df_new[0].tolist()
                    hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df.txt',sep='\t',header=0)
                    hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
                    hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
                    hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
                    hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
                    hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap + hap_pos_new ]
                    hap_array_df_index = pd.concat([ hap_array_df_up, hap_array_df_batch ], axis=0)
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df.txt',sep='\t', header=True, index=None)
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    hap_pos_updated = hap_pos_overlap + hap_pos_new
                elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] == 0:
                    hap_pos_existing = hap_pos_df_existing[0].tolist()
                    hap_pos_overlap = hap_pos_df_overlap[0].tolist()
                    hap_pos_df_existing_idx = (hap_pos_df_existing['index_x'] + 1).tolist()
                    hap_pos_df_overlap_idx = (hap_pos_df_overlap['index_x'] + 1).tolist()
                    command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_existing_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_existing.txt'
                    os.system(command)
                    command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_overlap_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_overlap.txt'
                    os.system(command)
                    pd.DataFrame(0, index=range(hap_count_batch),columns=range(hap_pos_df_existing.shape[0])).to_csv(output_path+'tmp_zeros_existing.txt',sep='\t', index=False, header=False)
                    command = 'cat '+output_path+'hap_array_df_existing.txt '+output_path+'tmp_zeros_existing.txt > '+output_path+'hap_array_df_existing_merge.txt'
                    os.system(command)
                    hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df_overlap.txt',sep='\t',header=0)
                    hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
                    hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
                    # hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
                    hap_array_df_overlap = hap_array_df_overlap[hap_pos_overlap]
                    hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap ]
                    hap_array_df_index = pd.concat([ hap_array_df_overlap, hap_array_df_batch ], axis=0)
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df_index.txt',sep='\t', header=True, index=None)
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    #keep the hap_array_df_updated
                    command = 'paste ' + output_path+'hap_array_df_existing_merge.txt '+ output_path+'hap_array_df_index.txt > ' + output_path+'hap_array_df.txt'
                    os.system(command)
                    hap_pos_updated = hap_pos_existing + hap_pos_overlap
                    os.remove(output_path+'hap_array_df_existing.txt')
                    os.remove(output_path+'hap_array_df_overlap.txt')
                    os.remove(output_path+'tmp_zeros_existing.txt')
                    os.remove(output_path+'hap_array_df_existing_merge.txt')
                    os.remove(output_path+'hap_array_df_index.txt')
                elif hap_pos_df_existing.shape[0] != 0 and hap_pos_df_overlap.shape[0] != 0 and hap_pos_df_new.shape[0] != 0:
                    hap_pos_existing = hap_pos_df_existing[0].tolist()
                    hap_pos_overlap = hap_pos_df_overlap[0].tolist()
                    hap_pos_new = hap_pos_df_new[0].tolist()
                    hap_pos_df_existing_idx = (hap_pos_df_existing['index_x'] + 1).tolist()
                    hap_pos_df_overlap_idx = (hap_pos_df_overlap['index_x'] + 1).tolist()
                    # print(hap_pos_df, hap_pos_df_existing, hap_pos_df_overlap, hap_pos_df_new)
                    command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_existing_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_existing.txt'
                    os.system(command)
                    command = 'cut -f '+','.join([ str(int(x)) for x in hap_pos_df_overlap_idx ])+ ' '+output_path+'hap_array_df.txt >'+output_path+'hap_array_df_overlap.txt'
                    os.system(command)
                    pd.DataFrame(0, index=range(hap_count_batch),columns=range(hap_pos_df_existing.shape[0])).to_csv(output_path+'tmp_zeros_existing.txt',sep='\t', index=False, header=False)
                    # cat hap_array_df_existing.txt tmp_zeros_existing.txt > hap_array_df_existing_merge.txt
                    command = 'cat '+output_path+'hap_array_df_existing.txt '+output_path+'tmp_zeros_existing.txt > '+output_path+'hap_array_df_existing_merge.txt'
                    os.system(command)
                    # hap_array_df_overlap = pd.read_csv(output_path+'haplotype_array_df_tmp.txt',sep='\t',header=0, usecols= [ str(x) for x in hap_pos_overlap], dtype='int8')
                    hap_array_df_overlap = pd.read_csv(output_path+'hap_array_df_overlap.txt',sep='\t',header=0)
                    hap_array_df_overlap = hap_array_df_overlap.fillna(0).astype('int8') 
                    # print(cur_hapidx, hap_array_df_overlap, hap_pos_overlap,hap_pos_df)
                    hap_array_df_overlap.columns = [ int(x) for x in hap_array_df_overlap.columns ]
                    hap_array_df_up = pd.concat( [hap_array_df_overlap, pd.DataFrame(0, index=hap_array_df_overlap.index, columns=hap_pos_new,dtype='int8' )] , axis=1)
                    hap_array_df_batch = hap_array_df_batch[ hap_pos_overlap + hap_pos_new ]
                    hap_array_df_index = pd.concat([ hap_array_df_up, hap_array_df_batch ], axis=0)
                    hap_array_index = np.array(hap_array_df_index, dtype='int8')
                    hap_array_df_index.to_csv(output_path+'hap_array_df_index.txt',sep='\t', header=True, index=None)
                    hap_array_batch = np.array(hap_array_df_batch, dtype='int8')
                    #keep the hap_array_df_updated
                    # paste hap_array_df_existing_merge.txt hap_array_df_index.txt >hap_array_df.txt
                    command = 'paste ' + output_path+'hap_array_df_existing_merge.txt '+ output_path+'hap_array_df_index.txt > ' + output_path+'hap_array_df.txt'
                    os.system(command)
                    # t = pd.read_csv(output_path+'hap_array_df.txt',sep='\t')
                    hap_pos_updated = hap_pos_existing + hap_pos_overlap + hap_pos_new
                    os.remove(output_path+'hap_array_df_existing.txt')
                    os.remove(output_path+'hap_array_df_overlap.txt')
                    os.remove(output_path+'tmp_zeros_existing.txt')
                    os.remove(output_path+'hap_array_df_existing_merge.txt')
                    os.remove(output_path+'hap_array_df_index.txt')

            """step2 :construct the faiss index and query to search"""
            print('constructing the faiss index and query...')
            xb = hap_array_index.astype(np.int8)
            xq = hap_array_batch.astype(np.int8)
            measure = faiss.METRIC_L2
            param = 'Flat'
            # param = 'HNSW64'
            # dim = len(hap_pos_batch)
            dim= hap_array_batch.shape[1]
            index = faiss.index_factory(dim, param, measure)
            # print(xb, xq, xb.shape, xq.shape, dim,index)
            # print(index.is_trained)
            index.add(xb)
            can_dis, can_ids = index.search(xq, can_lens)

            """step 3 identify the parent hap for each hap"""
            # hap_count_batch = haplotype_df_batch.shape[0]
            hap_ids = haplotype_df_updated.index.tolist()[-hap_count_batch:]
            for cur_hap_index in range(hap_count_batch):
            # for cur_hap_index in range(30):
                # cur_hap_index = 0
                cur_hapidx = hap_ids[cur_hap_index]
                # cur_hapidx = 118
                # if cur_hapidx % 1 == 0:
                #     print('processing the ', str(cur_hap_index), '/', str(hap_count_batch),' th hap in the ', str(batch_idx), 'th batch')
                cur_hap, cur_mut_count, cur_sample_names, cur_sample_count, cur_hap_pos, cur_hap_value = haplotype_df_updated.loc[cur_hapidx]
                cur_can_ids = can_ids[cur_hap_index].tolist()
                # cur_locations = cur_locations.split(';')
                if cur_mut_count == 1: #no choice but the reference hap
                    parent_hapidx = 0
                    # edges_list.append([parent_hapidx, cur_hapidx, cur_hap, 1])
                    edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, cur_hap, 1]).T ], axis=0)
                    # type1_list.append([batch_idx, cur_hap_index , cur_hapidx])
                    continue
                flag_count = 0
                parent_can_list = []
                dict_parent_can = {}
                a = hap_array_df_index.loc[cur_hapidx]
                # print('processing hap: ', cur_hapidx,'\n' ,'mutation positions: ', cur_hap_pos,'\n', 'values: ', cur_hap_value, '\n', [ x for x in cur_can_ids if x!=-1 ][:20],haplotype_df_updated.shape, hap_array_df_index.shape)
                for each_hap_index in range(1, can_lens):
                    # each_hap_index = 1
                    each_hapidx = cur_can_ids[each_hap_index]
                    if each_hapidx == -1:
                        break
                    if flag_count >= 5:
                        break
                    elif each_hapidx >= cur_hapidx:
                        continue
                    each_hap_pos = haplotype_df_updated.loc[each_hapidx]['hap_pos']
                    flag_count += 1
                    b = hap_array_df_index.loc[each_hapidx]
                    diff = a.compare(b)
                    diff_count = diff.shape[0]
                    dict_parent_can[each_hapidx] = diff
                    parent_can_list.append( [each_hapidx,  diff_count])
                #     print( 'candidate parents: ', each_hapidx, each_hap_pos, diff,b )
                # print('\n')
                if len(parent_can_list) > 0: 
                    parent_can_df = pd.DataFrame(parent_can_list)
                    parent_can_df = parent_can_df.sort_values([1], ascending=True)
                    parent_can_df = parent_can_df[parent_can_df[1]==parent_can_df.iloc[0][1]] # select the haps with minimum distance
                    if parent_can_df.shape[0] == 1: # this means there is only 1 candidate          
                        parent_hapidx = parent_can_df.iloc[0][0]
                        parent_diff = dict_parent_can[parent_hapidx]
                        # print(parent_hapidx, parent_diff)
                        parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                        # print(haplotype_df_updated.loc[parent_hapidx], cur_hap)
                        tmpdf= parent_diff[parent_diff['other']!=0]
                        if tmpdf.shape[0] == 0: # have no back mutation (the simplest)
                            # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                            # type1_count += 1         
                            pass       
                        else: #have back mutation
                            if tmpdf[tmpdf['self']!=0].shape[0] == tmpdf.shape[0]: # the same pos have multiple mutations
                                # type1_list.append([batch_idx,cur_hap_index, cur_hapidx])
                                pass
                            else:
                                # pass
                                # type2_list.append([batch_idx,cur_hap_index, cur_hapidx])
                                # print(cur_hapidx, cur_hap, parent_can_df, parent_diff, parent_hap, parent_hapidx) 
                                # print(edges_df)
                                parent_hapidx = find_parent(parent_hapidx)
                                if parent_hapidx not in dict_parent_can.keys():
                                    if parent_hapidx == 0:
                                        edge_muts = cur_hap
                                        edge_diff_count = cur_mut_count
                                        edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                                        continue
                                    else:
                                        b = hap_array_df_index.loc[parent_hapidx]
                                        parent_diff = a.compare(b)
                                else:
                                    parent_diff = dict_parent_can[parent_hapidx]
                        #process the parent hap 
                        if parent_hapidx == 0:
                            edge_muts = cur_hap
                            edge_diff_count = cur_mut_count
                        else:
                            # parent_diff = dict_parent_can[parent_hapidx]
                            parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                            parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                            edge_muts = []
                            # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], cur_hapidx, parent_hapidx, parent_diff)
                            for each_diffidx in parent_diff.index:
                                # each_diffidx = 3036
                                v1, v2 = parent_diff.loc[each_diffidx]
                                if v1 == 0 and v2 != 0:
                                    mut_idx = parent_hap_pos.index(each_diffidx)
                                    edge_mut_tmp = parent_hap.split(';')[mut_idx]
                                    edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                                elif v1!= 0 and v2 == 0:
                                    mut_idx = cur_hap_pos.index(each_diffidx)
                                    edge_mut = cur_hap.split(';')[mut_idx]
                                elif v1!= 0 and v2 != 0:
                                    mut1_idx = cur_hap_pos.index(each_diffidx)
                                    mut1 = cur_hap.split(';')[mut1_idx]
                                    mut2_idx = parent_hap_pos.index(each_diffidx)
                                    mut2 = parent_hap.split(';')[mut2_idx]
                                    edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                                edge_muts.append( edge_mut)
                            edge_diff_count = parent_diff.shape[0]
                            edge_muts = ';'.join(edge_muts)
                            # edges_list.append([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count] )
                        edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                    else: #this means there are multiple just select the hap which contain most samples in spatical-tempory 
                        # print(cur_hapidx, cur_hap, parent_can_df)
                        # type3_list.append([batch_idx,cur_hap_index, cur_hapidx])
                        parent_can_df.index = parent_can_df[0]
                        parent_can_idxs_list = parent_can_df[0].tolist()
                        #remove the candidates with back mutation
                        # can_back_df = parent_can_df
                        can_back_df = []
                        for each_idx in parent_can_idxs_list:
                            # each_idx = parent_can_idxs_list[0]
                            each_diff = dict_parent_can[each_idx]
                            tmpdf= each_diff[each_diff['other']!=0]
                            tmpdf= tmpdf[tmpdf['self']==0]
                            can_back_df.append(tmpdf.shape[0])
                        can_back_df = pd.DataFrame(can_back_df)
                        can_back_df.index=  parent_can_idxs_list
                        can_back_df = can_back_df.sort_values([0], ascending=True)
                        can_back_df = can_back_df[can_back_df[0]==can_back_df.iloc[0][0]]
                        if can_back_df.shape[0] == 1:  ##only 1 candidate do not have back mutation
                            parent_hapidx = can_back_df.index[0]
                        else: #multiple do not have back mutation
                            #if have parent-child relationship
                            parent_child_flag = False
                            can_list = can_back_df.index.tolist()
                            can_list = sorted(can_list, reverse=True)
                            for i in range(len(can_list)-1):
                                can_parent_hapidx = edges_df[edges_df[1]== can_list[i]].index[0]
                                if can_parent_hapidx in can_list[i+1:]: 
                                    parent_hapidx = can_parent_hapidx
                                    parent_child_flag = True
                                    # continue
                            #if do not have parent-child relationship
                            if parent_child_flag == False:
                                parent_can_df = parent_can_df.loc[can_back_df.index.tolist()]
                                parent_can_idx_selected = parent_can_df[0].values.tolist()
                                # parent_can_df = pd.concat([parent_can_df,  haplotype_df_updated.loc[parent_can_df.index.tolist()][['sample_time_min', 'sample_time_max', 'sample_locations']] ], axis=1)
                                # parent_can_idx_selected = []
                                # for each_parent_can in parent_can_df.values:
                                #     # each_parent_can = parent_can_df.values[0]
                                #     each_parent_hapidx, diff_count, each_time_min, each_time_max, each_locations = each_parent_can
                                #     each_locations = each_locations.split(';')
                                #     delta_time = each_time_max - cur_time_min
                                #     if delta_time.days >= -30 and len(set(cur_locations).intersection(set(each_locations))) >= 1:
                                #         parent_can_idx_selected.append(each_parent_hapidx)
                                if len(parent_can_idx_selected) == 0: #this means no one hap in spatical-tempory 
                                    #select the hap which contain the most samples in all candidates
                                     # parent_hapidx = haplotype_df_updated.loc[parent_can_df.index.tolist()].sort_values(['sample_count'], ascending=False).index[0]
                                     ##select the hap which is the nearest of time in all candidates
                                     parent_hapidx = parent_can_df.sort_values(['sample_time_min'], ascending=False).index[0]
                                elif len(parent_can_idx_selected) == 1: #this means only one just select it
                                    parent_hapidx = parent_can_idx_selected[0]
                                elif len(parent_can_idx_selected) > 1: # this means multiple in spatical-tempory just select the the hap which contain the most samples in spatical-tempory 
                                    parent_hapidx = haplotype_df_updated.loc[parent_can_idx_selected].sort_values(['sample_count'], ascending=False).index[0]
                        parent_diff = dict_parent_can[parent_hapidx]
                        parent_hap = haplotype_df_updated.loc[parent_hapidx]['haplotype']
                        if parent_hap == '':
                            edge_muts = cur_hap
                            edge_diff_count = parent_diff.shape[0]
                        else:
                            parent_hap_pos = haplotype_df_updated.loc[parent_hapidx]['hap_pos']
                            # print( parent_hap_pos, haplotype_df_updated.loc[parent_hapidx], haplotype_df_updated.loc[cur_hapidx], parent_hapidx, parent_diff)
                            edge_muts = []
                            # print(hap_array_index.shape)
                            # print(parent_hap_pos, parent_hapidx, parent_diff, haplotype_df_updated.shape, dict_parent_can, haplotype_df_batch.shape)
                            for each_diffidx in parent_diff.index:
                                v1, v2 = parent_diff.loc[each_diffidx]
                                if v1 == 0 and v2 != 0:                            
                                    mut_idx = parent_hap_pos.index(each_diffidx)
                                    edge_mut_tmp = parent_hap.split(';')[mut_idx]
                                    edge_mut = str(each_diffidx) + '(SNP:' + str(edge_mut_tmp[-2:-1]) + '->' + str(edge_mut_tmp[-5:-4]) + ')'
                                elif v1!= 0 and v2 == 0:
                                    mut_idx = cur_hap_pos.index(each_diffidx)
                                    edge_mut = cur_hap.split(';')[mut_idx]
                                elif v1!= 0 and v2 != 0:
                                    mut1_idx = cur_hap_pos.index(each_diffidx)
                                    mut1 = cur_hap.split(';')[mut1_idx]
                                    mut2_idx = parent_hap_pos.index(each_diffidx)
                                    mut2 = parent_hap.split(';')[mut2_idx]
                                    edge_mut = str(each_diffidx) + '(SNP:' + str(mut1[-5:-4]) + '->' + str(mut2[-5:-4]) + ')'
                                edge_muts.append( edge_mut)
                            edge_diff_count = parent_diff.shape[0]
                            edge_muts = ';'.join(edge_muts)
                        edges_df = pd.concat([ edges_df,pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)
                else: #this maybe caused by the can_lens is set too small just re-search for cur hap
                    # type4_list.append([batch_idx, cur_hap_index, cur_hapidx])
                    print(cur_hapidx ,' Warning') 
                    parent_hapidx = 0
                    edge_muts = cur_hap
                    edge_diff_count = cur_mut_count
                    edges_df = pd.concat([ edges_df, pd.DataFrame([parent_hapidx, cur_hapidx, edge_muts, edge_diff_count]).T ], axis=0)


hap_count_updated = haplotype_df_updated.shape[0]
# print( hap_count_updated, len(type1_list), len(type2_list), len(type3_list), len(type4_list) )
# print( len(type1_list)/hap_count_updated, len(type2_list)/hap_count_updated, len(type3_list)/hap_count_updated, len(type4_list)/hap_count_updated )
# pd.DataFrame(type1_list).to_csv(output_path+'type1.csv',sep=',', index=None , header=None)
# pd.DataFrame(type2_list).to_csv(output_path+'type2.csv',sep=',', index=None , header=None)
# pd.DataFrame(type3_list).to_csv(output_path+'type3.csv',sep=',', index=None , header=None)
# pd.DataFrame(type4_list).to_csv(output_path+'type4.csv',sep=',', index=None , header=None)

"""save files"""
haplotype_df_updated.to_csv(output_path+'haplotype_final.txt',sep='\t', header=True, index=None)
# hap_array_df_updated.to_csv(output_path+'hap_array_df_final.txt',sep='\t', header=True, index=None)
# edges_df = pd.DataFrame(edges_list)
edges_df.columns = ['source', 'target', 'mutations', 'distance']
edges_df.to_csv(output_path+'edges.csv',sep=',', index=None , header=True)

###for the webserver
#the network
G_dir = nx.DiGraph()
G_dir.add_edges_from(edges_df[['source', 'target']].values.tolist())

G = nx.from_pandas_edgelist(edges_df,source="source",target="target")
coms = cdlib.algorithms.louvain(G, resolution=2.0,randomize=True)
n_com = len(coms.communities)

"""identify the main path"""
#find key node in each cluster
dict_deg = dict(G.degree())
keynodes_list = []
dict_clusterstokeynode = {}
dict_clusterstonodes = {}
dict_nodestoclusters = {}
dict_clusterslen = {}
clusterid = 0
for com in coms.communities:
    score=0
    keynode=0
    for node in com:
        dict_nodestoclusters[node] = clusterid 
        if score < dict_deg[node]:
            score = dict_deg[node]
            keynode = node
    # dict_keynodes[keynode] = score
    keynodes_list.append(keynode)
    dict_clusterstokeynode[clusterid] = keynode
    dict_clusterslen[clusterid]=len(com)
    dict_clusterstonodes[clusterid]= com
    clusterid += 1

#filter keyNode use threshold or cluster count
G_smaller=G.copy()
removeNode=[]
for node in G.nodes():
    if dict_deg[node] ==1:
        removeNode.append(node)

for node in removeNode:
    G_smaller.remove_node(node)

filter_keynodes = []
small_keynodes = []
threshold = len(G_smaller.nodes())/float(n_com)
for keynode in keynodes_list:
    clusterid = dict_nodestoclusters[keynode]
    clusterlen = dict_clusterslen[clusterid]
    if clusterlen > threshold:
        filter_keynodes.append(keynode)
    else:
        small_keynodes.append(keynode)

##assign the nodes in small clusters to the nearest big cluster
print('process the small nodes')
for cur_keynode in small_keynodes:
    # cur_keynode = small_keynodes[0]
    cur_clusterid = dict_nodestoclusters[cur_keynode]
    cur_cluster_nodes = dict_clusterstonodes[cur_clusterid]
    cur_in_clusters = []
    cur_out_clusters = []
    for each_node in cur_cluster_nodes:
        # each_node = cur_cluster_nodes[0]
        in_cluster_list = list(set(G_dir.predecessors(each_node)))
        out_cluster_list = list(set(G_dir.successors(each_node)))
        in_cluster_list = [ dict_nodestoclusters[x] for x in in_cluster_list ]
        out_cluster_list = [ dict_nodestoclusters[x] for x in out_cluster_list ]
        if len(in_cluster_list) == 0:
            pass
        elif len(in_cluster_list) == 1:
            if in_cluster_list[0] == cur_clusterid:
                pass
            else:
                cur_in_clusters.append(in_cluster_list[0])
        elif len(in_cluster_list) > 1:
            cur_in_clusters += [ x for x in in_cluster_list if x != cur_clusterid ]
        ##for out clusters
        if len(out_cluster_list) == 0:
            pass
        elif len(out_cluster_list) == 1:
            if out_cluster_list[0] == cur_clusterid:
                pass
            else:
                out_in_clusters.append(out_cluster_list[0])
        elif len(out_cluster_list) > 1:
            cur_out_clusters += [ x for x in out_cluster_list if x != cur_clusterid ]
    cur_in_clusters = list(set(cur_in_clusters))
    cur_out_clusters = list(set(cur_out_clusters))
    # print(cur_clusterid, cur_keynode, cur_in_clusters, cur_out_clusters)
    ##merge the nodes in cur clusters into predecessor clusters
    if len(cur_in_clusters) == 0:
        continue
    in_clusterid = cur_in_clusters[0]
    dict_clusterstonodes[in_clusterid] += cur_cluster_nodes
    dict_clusterslen[in_clusterid] += len(cur_cluster_nodes)
    for each_node in cur_cluster_nodes:
        dict_nodestoclusters[each_node] = in_clusterid
    ##remove the corresponding clusters
    dict_clusterstokeynode.pop(cur_clusterid)
    dict_clusterslen.pop(cur_clusterid)
    dict_clusterstonodes.pop(cur_clusterid)

##process the trans nodes
print('process the trans nodes')
keynode_edges = []
# selected_nodes = []
for cur_keynode in filter_keynodes:
    # cur_keynode = filter_keynodes[7]
    cur_clusterid = dict_nodestoclusters[cur_keynode]
    cur_cluster_nodes = dict_clusterstonodes[cur_clusterid]
    cur_in_clusters = []
    cur_out_clusters = []
    for each_node in cur_cluster_nodes:
        # each_node = cur_cluster_nodes[0]
        in_cluster_list = list(set(G_dir.predecessors(each_node)))
        out_cluster_list = list(set(G_dir.successors(each_node)))
        in_cluster_list = [ dict_nodestoclusters[x] for x in in_cluster_list ]
        out_cluster_list = [ dict_nodestoclusters[x] for x in out_cluster_list ]
        if len(in_cluster_list) == 0:
            pass
        elif len(in_cluster_list) == 1:
            if in_cluster_list[0] == cur_clusterid:
                pass
            else:
                cur_in_clusters.append(in_cluster_list[0])
        elif len(in_cluster_list) > 1:
            cur_in_clusters += [ x for x in in_cluster_list if x != cur_clusterid ]
        ##for out clusters
        if len(out_cluster_list) == 0:
            pass
        elif len(out_cluster_list) == 1:
            if out_cluster_list[0] == cur_clusterid:
                pass
            else:
                cur_out_clusters.append(out_cluster_list[0])
        elif len(out_cluster_list) > 1:
            cur_out_clusters += [ x for x in out_cluster_list if x != cur_clusterid ]
    cur_in_clusters = list(set(cur_in_clusters))
    cur_out_clusters = list(set(cur_out_clusters))
    # print(cur_clusterid, cur_keynode, cur_in_clusters, cur_out_clusters)
    #for in 
    if len(cur_in_clusters) == 0:
        pass
    else:
        in_clusterid = cur_in_clusters[0]
        in_keynode = dict_clusterstokeynode[in_clusterid]
        keynode_edges.append( [in_keynode, cur_keynode] )
    #for out
    for each_out_cluster in cur_out_clusters:
        out_keynode = dict_clusterstokeynode[each_out_cluster]
        keynode_edges.append( [cur_keynode, out_keynode] )

keynode_edges_df = pd.DataFrame(keynode_edges)
keynode_edges_df = keynode_edges_df.drop_duplicates()

transnodes_list = []
for each_edge in keynode_edges_df.values:
    # each_edge = keynode_edges_df.values.tolist()[0] 
    node1, node2 = each_edge
    each_path = nx.dijkstra_path(G_smaller,node1, node2)
    if len(each_path) == 2:
        pass
    else:
        transnodes_list += each_path[1:-1]


f=open(output_path + "nodes.csv","w")
f.write("Hapid,Label,Time_min,Time_max,ClusterId,Cluster_len,Value,Mutations,Locations,Samplenames\n")
for node in G.nodes():
    if node in filter_keynodes:
        node_value = 1000
    elif node in transnodes_list:
        node_value = 100
    else:
        node_value = 0
    node_label = node
    node_cluster = dict_nodestoclusters[ node ]
    node_cluster_len = dict_clusterslen[ node_cluster ]
    # node_lineage = haplotype_df.loc[node]['Lineage']
    node_hap = haplotype_df_updated.loc[node]['haplotype']
    node_samples = haplotype_df_updated.loc[node]['sample_names']
    if meta_flag == True:
        node_time_min = datetime.strftime(haplotype_df_updated.loc[node]['sample_time_min'], '%Y-%m-%d')
        node_time_max = datetime.strftime(haplotype_df_updated.loc[node]['sample_time_max'], '%Y-%m-%d')
        node_locations = haplotype_df_updated.loc[node]['sample_locations']
    else:
        node_time_min = node_time_max = node_locations = ''
    t = [node, node_label, node_time_min, node_time_max, node_cluster, node_cluster_len, node_value, node_hap,node_locations, node_samples]
    # if meta_flag == True:
    #     t = [node, node_label, node_time_min, node_time_max, node_cluster, node_cluster_len, node_value, node_hap,node_locations, node_samples] + [node_lineage]
    t= [str(x) for x in t ]
    f.write(','.join(t)+"\n")
f.close()

##for the large dataset which have too much haplotypes 
##we just show the main path nodes and edges
if haplotype_df_updated.shape[0] > 20000:
    # output_path = 'venas2_output_covid19_example1/'
    # haplotype_df_updated = pd.read_csv(data_path+'haplotype_final.txt',sep='\t', header=0)
    edges_df = pd.read_csv(output_path + 'edges.csv', sep=',', header=0)
    nodes_df = pd.read_csv(output_path + 'nodes.csv', sep=',', header=0)
    nodes_df_selected = nodes_df[(nodes_df['Value']==1000) | (nodes_df['Value']==100)]
    nodes_df_tmp = pd.DataFrame()
    nodes_df_tmp[0] = (nodes_df_selected['Hapid'] + 1)
    nodes_df_tmp.to_csv(output_path + 'tmp_nodes_selected.txt', sep=',', index=False, header=True)
    # keynodes_selected = filter_keynodes + transnodes_list
    nodes_selected_list = nodes_df_selected['Hapid'].tolist()
    dict_indexs = {}
    for k,v in enumerate(nodes_selected_list):
        dict_indexs[v] = k

    nodes_df_selected.iloc[:]['Hapid'] = list(range(nodes_df_selected.shape[0]))
    nodes_df_selected.iloc[:]['Label'] = list(range(nodes_df_selected.shape[0]))
    nodes_df_selected.to_csv(output_path + 'nodes.csv', sep=',', index=False, header=True)
    edges_selected = []
    for each in  edges_df.values:
        source, target, mutations, dis = each
        if source in nodes_selected_list and target in nodes_selected_list:
            edges_selected.append([dict_indexs[source], dict_indexs[target], mutations,dis])
    edges_selected = pd.DataFrame(edges_selected)
    edges_selected.columns = ['source', 'target', 'mutations', 'distance']
    edges_selected.to_csv(output_path + 'edges.csv', sep=',', index=False, header=True)
    #for hap_array_df
    # haplotype_df_updated = pd.read_csv(output_path + 'haplotype_final.txt', sep='\t', header=0)
    haplotype_df_updated.iloc[nodes_selected_list].to_csv(output_path+'haplotype_final.txt',sep='\t', header=True, index=None)
    pd.DataFrame(range(haplotype_df_updated.shape[0])).to_csv(output_path + 'hap_array_df_idx.txt', sep=',', index=False, header=False)
    command = 'paste ' + output_path+'hap_array_df_idx.txt '+ output_path+'hap_array_df.txt > ' + output_path+'hap_array_df_merge.txt'
    os.system(command)
    # awk -F'\t'  'FILENAME=="venas2_output_covid19_example1/tmp_nodes_selected.txt"{F[$0]=1}FILENAME=="venas2_output_covid19_example1/hap_array_df_merge.txt"{if($1 in F){print}}' venas2_output_covid19_example1/tmp_nodes_selected.txt venas2_output_covid19_example1/hap_array_df_merge.txt > venas2_output_covid19_example1/hap_array_df_tmp.txt
    command = "awk -F'\t'  'FILENAME==\"" +output_path+ "tmp_nodes_selected.txt\"{F[$0]=1}FILENAME==\""+output_path+"hap_array_df_merge.txt\"{if($1 in F){print}}' "+output_path+"tmp_nodes_selected.txt "+output_path+"hap_array_df_merge.txt > "+output_path+"hap_array_df_tmp.txt"
    os.system(command)
    command = 'cut -f 2- ' + output_path+'hap_array_df_tmp.txt > ' + output_path+'hap_array_df.txt'
    os.system(command)
    os.remove(output_path+'hap_array_df_tmp.txt')
    os.remove(output_path+'hap_array_df_merge.txt')
    os.remove(output_path+'hap_array_df_idx.txt')
    os.remove(output_path+'tmp_nodes_selected.txt')

end_time = time.time()
diff = end_time - beg_time
print(time.ctime())
print(diff, ' s')
print(diff/60, ' min')
print(diff/60/60, ' h')

# if __name__ == '__main__':
#     main()
# awk -F'\t'  'FILENAME=="a"{F[$1]=1}FILENAME=="b"{if(NR in F){print}}' a b
# cut -f 2- b
