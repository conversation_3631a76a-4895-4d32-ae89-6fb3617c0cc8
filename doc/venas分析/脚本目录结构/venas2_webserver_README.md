# VENAS2
## Introduction

VENAS2：an updated Viral genome Evolution Network Analysis System.
Comprehensive analyses of viral genomes can provide a global picture of SARS-CoV-2 transmission and help to predict the oncoming trends of the pandemic. However, the rapid accumulation of SARS-CoV-2 genomes presents an unprecedented data size and complexity that has exceeded the capacity of existing methods in constructing evolution network through virus genotyping. The VENAS seeks to apply reliable computational algorithms to build an integrative genomic analysis system that enables researchers to trace viral mutations along the transmission routes using the daily updated SARS-CoV-2 genomes.


## Pre-requisites
VENAS2 requires python3 with following third-party library/program:
argparse
pandas
numpy
faiss
networkx
CDlib
...

## Test the scripts with following commands
python3 scripts/venas2.py -input_name covid19_example
python3 scripts/venas2.py -input_name covid19_example_nometa
python3 scripts/venas2_addnew.py -venas2_output_path venas2_output_covid19_example -input_name covid19_example_newdata
python3 scripts/venas2_addnew.py -venas2_output_path venas2_output_covid19_example_nometa -input_name covid19_example_nometa_newdata

python3 scripts/venas2.py -input_name mtb_example
python3 scripts/venas2_addnew.py -venas2_output_path venas2_output_mtb_example -input_name mtb_example_newdata


## Basic Usage
We assume here that all the scripts are in the system path.

### Part 1: Construct the haplotype network 
The only input file is the mutation file of the samples, and its format is defined as following:
Samplename Mutations Date Location 
e.g. 
EPI_ISL_784970	240(SNP:C->T),1059(SNP:C->T) 2020-01-01	China
EPI_ISL_784972	240(SNP:C->T),1059(SNP:C->T),2448(INDEL:G->-) 2020-01-01 China

```
python3 scripts/venas2.py -input_name input_file
```
**Results Description:**
*	haplotype.txt: The file contains the haplotypes information including Accession ID, mutations, collection date, location and so on.
*	haplotype_array.txt: The file contains the vector form of haplotypes, it is saved for save time. Although we can recalculate it from the haplotype.txt file but it will cost some time. With this file we can add new samples for existing haplotype network more fast. 
*	nodes.csv: the file contains all the nodes(haplotypes) information needed to construct the network.
*	edges.csv: the file contains all the edges information which linked the nodes, and this file is used to construct the network. The source-target pair are parent-child edge, which means the target is evolved from the source haplotype with corresponding mutations.

### Part 2: Placing new samples on the existing haplotype network
This function is used to fasta and accurate place new samples on the existing haplotype network. 
You can get the parent node of new samples in real time. 
You can trace the evolution path of the sample from reference seqence and visualize the main mutations.
```
python3 scripts/venas2_addnew.py -venas2_output_path venas2_ouput_path -input_name input_file
``` 
This function need two inputs:
*  -venas2_output_path: the output results path of part 1 venas2.py
* -input_name: the mutation file path of new samples. 
The results files are the same as part 1 venas2.py. 

**Results Description:**
* parent_data.txt The file contains the information of new haplotypes and their corresponding parent haplotypes, which can help us know better about the new haplotypes. For example, if the parent is belong to lineage B.1 then the new haplotype is very likely belong to B.1 too. 


## Examples
python3 scripts/venas2.py -input_name example1
python3 scripts/venas2_addnew.py -venas2_output_path venas2_output_example1 -input_name example1_newdata


## Contact us
If you have any questions, please feel free to contact us. 
<EMAIL>
<EMAIL>

## About us
Bio-Med Big Data Center, CAS Key Laboratory of Computational Biology, Shanghai Institute of Nutrition and Health, Chinese Academy of Sciences

## Citation

