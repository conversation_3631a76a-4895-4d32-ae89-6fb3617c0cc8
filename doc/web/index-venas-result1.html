<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>病原微生物综合分析云平台</title>
    <link rel="stylesheet" href="assets/libs/bootstrap-4.3.1-dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/libs/treetable/css/jquery.treetable.css">
    <link rel="stylesheet" href="assets/libs/treetable/css/jquery.treetable.theme.default.css">
    <link rel="stylesheet" href="assets/css/icons.css">
    <link rel="stylesheet" href="assets/js/jquery-ui/jquery-ui.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<header>
    <div class="container-fluid">
        <nav class="navbar navbar-expand-lg">
            <a class="navbar-brand" href="index.html"><img src="assets/images/logo-ipac.png" alt=""/></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent">
                <span class="fa fa-bars"></span>
            </button>
            <div class="collapse navbar-collapse active" id="navbarSupportedContent">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">HOME</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index-ipp.html">IPP</a>
                    </li>
                    <li class="nav-item active">
                        <a class="nav-link" href="index-venas.html">VENAS</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index-help.html">HELP</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index-contact.html">CONTACT US</a>
                    </li>
                    <!-- <li class="nav-item">
                        <div class="d-flex align-items-center justify-content-center py-1">
                            <a href="register.html" class="btn btn-primary waves-effect waves-light btn-sm mx-2">SIGN UP</a>  
                            <a href="login.html" class="btn btn-outline-primary waves-effect waves-light btn-sm ml-2">SIGN IN</a>
                        </div>
                    </li> -->
                    <li class="dropdown notification-list list-inline-item ml-4">
                        <div class="dropdown notification-list pt-2">
                            <a class="dropdown-toggle nav-user text-primary" data-toggle="dropdown" href="javascript:void(0);" aria-haspopup="false" aria-expanded="false">Anonymous</a>
                            <div class="dropdown-menu dropdown-menu-right profile-dropdown ">
                                <a class="dropdown-item" href="index-my-data.html"><i class="mdi mdi-database m-r-5"></i> My data</a>
                                <a class="dropdown-item" href="index-my-tasks.html"><i class="mdi mdi-file-document-box-outline m-r-5"></i> My task</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-danger" href="index.html"><i class="mdi mdi-power text-danger"></i> Logout</a>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
</header>
<div class="wrapper py-0">
    <div class="container-fluid">
        <div class="page-title-box py-3">
            <div class="row align-items-center">
                <div class="col-sm-12">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.html"><i class="mdi mdi-home-outline"></i></a>
                        </li>
                        <li class="breadcrumb-item active">VENAS</li>
                    </ol>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="card card-box">
                    <div class="card-body">
                        <h4 class="mt-0 header-title mb-3">Ts020101 Analysis Result</h4>
                        <div class="position-relative" id="myTabContent">
                            <div class="chart" id="clade-graph" style="width: 100%; "></div>
                            <div id="filter4" class="chart-filter d-none">
                                <div class="num-range">
                                    <input type="range" class="custom-range" min="0" step="1" value="1">
                                </div>
                                <div class="row no-gutters flex-column">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <p>Copyright ©2024 Bioland Laboratory, CAS-MPG Partner Institute for Computational Biology(PICB)</p>
            </div>
        </div>
    </div>
</footer>
<!-- End Footer -->



<script src="assets/js/jquery.min.js"></script>
<script src="assets/libs/bootstrap-4.3.1-dist/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/jquery.slimscroll.js"></script>
<script src="assets/js/waves.min.js"></script>
<script src="assets/libs/bootstrap-filestyle/js/bootstrap-filestyle.min.js"></script>
<script src="assets/js/common.js"></script>
<script src="assets/js/echarts.js"></script>
<script src="assets/js/shine.js"></script>
<script src="assets/js/dataTool.js"></script>
<script src="assets/js/console.save.js"></script>
<script src="assets/data/countriesGroup.js"></script>
<script src="assets/data/coord.js"></script>
<script>
    function setChartHeight() {
        $('.chart').css('height', $(window).height() - 280)
    }
    setChartHeight()
    $('#clade-graph').css('width', $('#myTabContent').width())
   

    // 颜色区间取色方法
    var colorPalette = [
        '#339ca8', '#0098d9', '#e6b600', '#3fb1e3',
        '#005eaa', '#96dee8', '#a0a7e6', '#c12e34',
        '#c4ebad', '#626c91', '#6be6c1', '#32a487',
        '#cda819', '#2b821d',
        '#516b91', '#59c4e6', '#edafda', '#93b7e3',
        '#a5e7f0', '#cbb0e3', '#d87c7c', '#919e8b',
        '#d7ab82', '#6e7074', '#61a0a8', '#efa18d',
        '#787464', '#cc7e63', '#724e58', '#4b565b'
    ];

    //生成base64


    function base64(data, chartColor) {
        data = data.map(function (item) {
            if (item.country) {
                item.itemStyle = {color: chartColor[item.country]}
            } else if (item.host) {
                item.itemStyle = {color: chartColor[item.host]}
            } else {
                item.itemStyle = {color: chartColor[item.clade]}
            }

            return item
        })
        var base64 = echarts.init(document.getElementById('base64'), 'shine', {renderer: 'svg'})
        base64.setOption({
            series: [
                {
                    type: 'pie',
                    radius: '100%',
                    center: ['50%', '50%'],
                    data: data,
                    label: {show: false}
                }
            ],
            animation: false
        })
        const svg = $('#base64').find('svg')[0];
        const s = new XMLSerializer().serializeToString(svg);
        const ImgBase64 = `data:image/svg+xml;base64,${window.btoa(s)}`;
        // console.info(ImgBase64)
        return ImgBase64
    }

    var cladeColor = {}

    function drawCladeGraph() {
        var cladeGraphChart = echarts.init(document.getElementById('clade-graph'), '', {renderer: 'svg'});
        $.ajax({
            url: './assets/data/clade.json',
            beforeSend: function () {
                cladeGraphChart.showLoading();
            },
            success: function (result) {
                cladeGraphChart.hideLoading();
                var mafs = result.data.mafs
                var filterText = ''
                if (mafs.length > 1) {
                    mafs.forEach(function (item) {
                        filterText += '<div class="col d-flex align-items-center">' + item + '</div>'
                    })
                    var mafIndex = $("input[name=mafIndex]").val() == "" ? 0 : $("input[name=mafIndex]").val();
                    $('#filter4 .row').html(filterText)
                    var inputRangeWidth = $('.chart-filter').height() - $('.chart-filter').height() / mafs.length + 14
                    $('#filter4 .num-range').html('<input type="range" class="custom-range" min="0" max="' + (mafs.length - 1) + '" step="1" value="' + mafIndex + '" onclick="rangeClick(this)" style="width: ' + inputRangeWidth + 'px;left:-' + (inputRangeWidth / 2) + 'px">')


                }
                var clade = result.data.categories.map(function (item) {
                    return item.name
                })
                var cladeColor = {}
                for (var i = 0; i < clade.length; i++) {
                    cladeColor[clade[i]] = colorPalette[i % colorPalette.length]
                }
                var data = result.data.nodes.map(function (item) {
                    if (item.value <= 20) {
                        item.symbolSize = item.value / 5 + 10;
                    } else if (20 < item.value <= 40) {
                        item.symbolSize = item.value / 10 + 14;
                    } else if (40 < item.value <= 80) {
                        item.symbolSize = item.value / 20 + 18;
                    } else if (80 < item.value <= 160) {
                        item.symbolSize = item.value / 40 + 22;
                    } else if (160 < item.value <= 320) {
                        item.symbolSize = item.value / 80 + 26;
                    } else if (320 < item.value <= 640) {
                        item.symbolSize = item.value / 160 + 30;
                    } else if (640 < item.value <= 1280) {
                        item.symbolSize = item.value / 320 + 34;
                    }
                    item.value = null;
                    if (item.outgroup) {
                        item.symbol = 'path://M519.9 458.2c0 65.3-52.9 118.2-118.3 118.2-65.3 0-118.2-52.9-118.2-118.2 0-65.3 52.9-118.2 118.2-118.2 65.3 0 118.3 52.9 118.3 118.2z m169.4-5.8c-34.3 0-62 27.8-62 62s27.7 62 62 62 62-27.8 62-62-27.7-62-62-62z m-151 170c-42.1 0-76.2 34.1-76.2 76.2s34.1 76.2 76.2 76.2 76.2-34.1 76.2-76.2-34.2-76.2-76.2-76.2z m97.2-271.9c0-11.6-9.4-21-21-21s-21 9.4-21 21 9.4 21 21 21 21-9.4 21-21z m-275.9 335c-13.8 0-25 11.2-25 25s11.2 25 25 25 25-11.2 25-25-11.2-25-25-25zM446.3 269c9.4 0 17.1-7.6 17.1-17.1 0-9.4-7.6-17.1-17.1-17.1-9.4 0-17.1 7.6-17.1 17.1 0 9.5 7.7 17.1 17.1 17.1z m462.4 210.1c19.3 0 34.9 15.6 34.9 34.9 0 19.3-15.6 34.9-34.9 34.9h-37.1c-3.5 36.5-12.3 71.3-25.9 103.8l42.4 24.5v0.1c0.3 0.1 0.5 0.2 0.8 0.3 16.7 9.6 22.4 31 12.8 47.7-9.6 16.7-31 22.4-47.7 12.8-0.3-0.1-0.4-0.4-0.7-0.5v0.1l-40.4-23.3c-20.6 30.9-45.9 58.4-74.8 81.5l26.2 45.5c9.6 16.7 3.9 38-12.8 47.7-16.7 9.6-38 3.9-47.7-12.8l-24.3-42.2c-41 21.4-86.6 35.1-135 39.3v36.9h-0.3c-1.4 18-16.3 32.3-34.6 32.3-18.4 0-33.3-14.2-34.6-32.3h-0.3v-37.5c-46.9-5-91.2-19-130.9-40.2l-25.2 43.7c-9.6 16.7-31 22.4-47.7 12.8-16.7-9.6-22.4-31-12.8-47.7l27.5-47.6c-28.5-23.3-53.4-50.8-73.6-81.7l-40.3 23.3c-16.7 9.6-38 3.9-47.7-12.8-9.6-16.7-3.9-38 12.8-47.7l42.7-24.6c-13-31.8-21.5-65.9-24.9-101.5h-34.9c-1.4 0.2-2.8 0.4-4.3 0.4-19.3 0-34.9-15.6-34.9-34.9 0-19.2 15.5-34.7 34.6-34.9v-0.5h39.6c3.9-39.3 14.1-76.8 29.6-111.3l-38.8-22.4c-16.7-9.6-22.4-31-12.8-47.7 9.6-16.7 31-22.4 47.7-12.8l38.4 22.2c23.6-33.5 52.8-62.7 86.2-86.4l-23.2-40.2c-9.6-16.7-3.9-38 12.8-47.7 16.7-9.6 38-3.9 47.7 12.8l23.3 40.4c33.9-15.3 70.6-25.5 109.2-29.6v-39.7c0-19.3 15.6-34.9 34.9-34.9 19.3 0 34.9 15.6 34.9 34.9v39.1c41.4 3.6 80.7 14.1 116.9 30.5l23.1-40.1c0.1-0.2 0.2-0.5 0.3-0.7 9.6-16.7 31-22.4 47.7-12.8 16.7 9.6 22.4 30.9 12.8 47.6h0.1L722 220.7c34.2 24.4 64 54.6 87.9 89.2l43.8-25.3v0.1c0.2-0.1 0.4-0.4 0.7-0.5 16.7-9.6 38-3.9 47.7 12.8 9.6 16.7 3.9 38-12.8 47.7-0.3 0.1-0.5 0.2-0.8 0.4v0.1l-44.8 25.9c14.6 33.6 24.2 69.9 27.9 107.9h37.1v0.1zM513.1 834.2c176.6 0 319.8-143.2 319.8-319.8S689.7 194.7 513.1 194.7c-109.7 0-206.4 55.3-264 139.4l-24.5 42.5c-20 41.7-31.2 88.5-31.2 137.8-0.1 176.6 143.1 319.8 319.7 319.8z'
                        item.symbolSize = 30
                    }
                    item.itemStyle = {color: cladeColor[item.category]}
                    if (item.highOrDark == 'dark') {
                        item.itemStyle.opacity = 0.2
                    } else {
                        item.itemStyle.opacity = 1
                    }
                    return item;
                });
                var categories = result.data.categories;
                var links = result.data.links;
                cladeGraphChart.setOption({
                    color: colorPalette,
                    tooltip: {
                        formatter: function (program) {
                            var toolTip = '';
                            if (program.dataType == 'edge') {
                                toolTip += '<div style="padding-left: 5px">' + 'Distance: ' + program.data.value + '</div>'
                                var diffLength = program.data.diffs.length <= 6 ? program.data.diffs.length : 6;
                                toolTip += '<div class="mb-1 border-top mt-1 pt-1" style="padding: 0 5px;">Variations:' + program.data.diffs.length + '</div>'
                                for (var i = 0; i < diffLength; i++) {

                                    toolTip += '<span class="tooltip-tag">' + program.data.diffs[i].pos + ':' + program.data.diffs[i].geno1 + '-' + program.data.diffs[i].geno2 + '</span>'
                                    if (i % 3 == 2) {
                                        toolTip += '<br/>'
                                    }
                                }
                                toolTip += '</div>'
                            } 
                            // else {
                            //     toolTip += '<div style="padding-left: 5px">' + 'Distance: ' + program.data.value + '</div>'
                            //     var diffLength = program.data.diffs.length <= 6 ? program.data.diffs.length : 6;
                            //     toolTip += '<div class="mb-1 border-top mt-1 pt-1" style="padding: 0 5px;">Variations:' + program.data.diffs.length + '</div>'
                            //     for (var i = 0; i < diffLength; i++) {

                            //         toolTip += '<span class="tooltip-tag">' + program.data.diffs[i].pos + ':' + program.data.diffs[i].geno1 + '-' + program.data.diffs[i].geno2 + '</span>'
                            //         if (i % 3 == 2) {
                            //             toolTip += '<br/>'
                            //         }
                            //     }
                            //     toolTip += '</div>'
                            // }
                            return toolTip
                        }
                    },
                    legend: {
                        type: 'scroll',
                        data: categories.map(function (category) {
                            return category
                        }),
                        orient: 'vertical',
                        left: 'left',
                        top: 'top',
                        itemHeight :22,
                        // show:false
                    },
                    series: [{
                        type: 'graph',
                        layout: 'force',
                        draggable: true,
                        roam: true,
                        categories: categories,
                        data: data,
                        links: links,
                        emphasis: {
                            label: {show: false}
                        },
                        force: {
                            repulsion: 10,
                            gravity: 0.05,
                            edgeLength: [1, 60]
                        }
                    }]
                });
                chartZoom(cladeGraphChart, document.getElementById('clade-graph'))
            }
        });
    }
    function chartZoom(dom, dom1) {
        var tag = dom1.getElementsByTagName('div')[0]
        var labelShow = false
        tag.onmousewheel = function (e) {
            e = e || window.event;
            var chartZoom = dom.getOption().series[0].zoom

            if (e.wheelDelta) { //第一步：先判断浏览器IE，谷歌滑轮事件
                console.info('1')
                if (e.wheelDelta > 50 && chartZoom > 2 && !labelShow) { //当滑轮向上滚动时
                    console.info('2')
                    dom.setOption({
                        series: [{
                            label: {
                                show: true,
                                position: 'top',
                                fontSize: 12,
                                formatter: function (params) {
                                    // console.info(params)
                                    return params.data.name
                                }
                            }
                        }]
                    })
                    labelShow = true
                }
                if (e.wheelDelta < 50 && chartZoom < 2 && labelShow) { //当滑轮向下滚动时
                    console.info('3')
                    dom.setOption({
                        series: [{
                            label: {
                                show: false
                            }
                        }]
                    })
                    labelShow = false
                }
            } else if (e.detail) { //Firefox滑轮事件
                if (e.detail > 50 && chartZoom > 2 && !labelShow) { //当滑轮向上滚动时
                    dom.setOption({
                        series: [{
                            label: {
                                show: true,
                                position: 'top',
                                fontSize: 12,
                                formatter: function (params) {
                                    // console.info(params)
                                    return params.data.name
                                }
                            }
                        }]
                    })
                    labelShow = true
                }
                if (e.detail < 50 && chartZoom < 2 && labelShow) { //当滑轮向下滚动时
                    dom.setOption({
                        series: [{
                            label: {
                                show: false
                            }
                        }]
                    })
                    labelShow = false
                }
            }
        }
    }
    $('#filter4 .custom-range').click(function () {
        console.info(cladeColor)
        var val = $(this).val()
        var maf = $(this).parents(".chart-filter").find(".col").eq(val).text();
        var url = ''
        if (maf == '>=0.1') {
            url = 'data/clade2.json'
        }
        $.ajax({
            url: url,
            success: function (result) {
                console.info(result)
                var nodes = result.data.nodes
                var nodesNum = result.data.nodes.length
                var linksNum = result.data.nodes.length
                $('#clade-graph svg path').each(function (i) {
                    if (i > (linksNum - 1) && i < (linksNum + nodesNum)) {
                        console.info('11')
                        $(this).css('fill', cladeColor[nodes[i - linksNum].category])
                        // console.info($(this).attr('style'))
                    }
                })
            }
        })

    })
    
    $(document).ready(function () {
        drawCladeGraph();
    })

</script>
</body>
</html>
