<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>病原微生物综合分析云平台</title>
    <link rel="stylesheet" href="assets/libs/bootstrap-4.3.1-dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/libs/treetable/css/jquery.treetable.css">
    <link rel="stylesheet" href="assets/libs/treetable/css/jquery.treetable.theme.default.css">
    <link rel="stylesheet" href="assets/libs/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/css/icons.css">
    <link rel="stylesheet" href="assets/js/jquery-ui/jquery-ui.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<header>
    <div class="container-fluid">
        <nav class="navbar navbar-expand-lg">
            <a class="navbar-brand" href="index.html"><img src="assets/images/logo-ipac.png" alt=""/></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent">
                <span class="fa fa-bars"></span>
            </button>
            <div class="collapse navbar-collapse active" id="navbarSupportedContent">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">HOME</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index-ipp.html">IPP</a>
                    </li>
                    <li class="nav-item active">
                        <a class="nav-link" href="index-venas.html">VENAS</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index-help.html">HELP</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index-contact.html">CONTACT US</a>
                    </li>
                    <!-- <li class="nav-item">
                        <div class="d-flex align-items-center justify-content-center py-1">
                            <a href="register.html" class="btn btn-primary waves-effect waves-light btn-sm mx-2">SIGN UP</a>  
                            <a href="login.html" class="btn btn-outline-primary waves-effect waves-light btn-sm ml-2">SIGN IN</a>
                        </div>
                    </li> -->
                    <li class="dropdown notification-list list-inline-item ml-4">
                        <div class="dropdown notification-list pt-2">
                            <a class="dropdown-toggle nav-user text-primary" data-toggle="dropdown" href="javascript:void(0);" aria-haspopup="false" aria-expanded="false">Anonymous</a>
                            <div class="dropdown-menu dropdown-menu-right profile-dropdown ">
                                <a class="dropdown-item" href="index-my-data.html"><i class="mdi mdi-database m-r-5"></i> My data</a>
                                <a class="dropdown-item" href="index-my-tasks.html"><i class="mdi mdi-file-document-box-outline m-r-5"></i> My task</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-danger" href="index.html"><i class="mdi mdi-power text-danger"></i> Logout</a>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
</header>
<div class="wrapper py-0">
    <div class="container-fluid">
        <div class="page-title-box py-3">
            <div class="row align-items-center">
                <div class="col-sm-12">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.html"><i class="mdi mdi-home-outline"></i></a>
                        </li>
                        <li class="breadcrumb-item active">VENAS</li>
                    </ol>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="card card-box">
                    <div class="card-body">
                        <h4 class="mt-0 header-title mb-3">VENAS2</h4>
                        <div class="info-box">
                            <h6>Instruction</h6>
                            <p class="mb-1">VENAS2 is an updated Viral genome Evolution Network Analysis System.Comprehensive analyses of viral genomes can provide a global picture of SARS-CoV-2 transmission and help to predict the oncoming trends of the pandemic. However, the rapid accumulation of SARS-CoV-2 genomes presents an unprecedented data size and complexity that has exceeded the capacity of existing methods in constructing evolution network through virus genotyping.</p>
                        </div>
                       <!-- Nav tabs -->
                        <ul class="nav nav-tabs-custom-2" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link" href="index-venas.html" >
                                    <span class="d-sm-block">Construct haplotype network </span> 
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link active" href="index-venas-renew.html">
                                    <span class="d-sm-block">Trace new samples</span> 
                                </a>
                            </li>
                        </ul>
                        <form action="" class="ana-form">
                            <div class="row">
                                <div class="offset-md-1 col-md-10">
                                    <div class="card border shadow-sm">
                                        <div class="card-body">
                                            <div class="form-group row align-items-center">
                                                <label for="" class="col-md-3 col-form-label">Select Task</label>
                                                <div class="col-md-5">
                                                    <select class="form-control select2">
                                                        <option>Select</option>
                                                        <option value="t01">Ts020101</option>
                                                        <option value="t02">Ts020102</option>
                                                        <option value="t03">Ts020103</option>
                                                        <option value="t04">Ts020104</option>
                                                        <option value="t05">Ts020105</option>
                                                        <option value="t06">Ts020106</option>
                                                        <option value="t07">Ts020107</option>
                                                        <option value="t08">Ts020108</option>
                                                        <option value="t09">Ts020109</option>
                                                        <option value="t10">Ts020110</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center">
                                                <label for="" class="col-md-3 col-form-label">Pathogen Species</label>
                                                <div class="col-md-8">
                                                    <div class="btn-group btn-group-toggle flex-wrap" data-toggle="buttons">
                                                        <label class="btn btn-outline-primary btn-sm px-3"><input type="radio" name="ps" id="" >SARS-Cov-19</label>
                                                        <label class="btn btn-outline-primary btn-sm px-3"><input type="radio" name="ps" id="">M.tuberculosis</label>
                                                        <label class="btn btn-outline-primary btn-sm px-3"><input type="radio" name="ps" id="">others</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-baseline mb-0">
                                                <label for="" class="col-md-3 col-form-label">Upload samples files</label>
                                                <div class="col-md-8">
                                                    <div class="form-group">
                                                        <label><a href="javascript:void(0);">example file</a></label>
                                                        <input type="file" class="filestyle input-group-sm" data-buttonname="btn-secondary">
                                                        <textarea name="" class="form-control" rows="4" disabled>
The format of input samples file:
Samplename   Mutations   Time    Country
EPI_ISL_784970  240(SNP:C->T);1059(SNP:C->T) 2020-01-01 China
EPI_ISL_784972  240(SNP:C->T);1059(SNP:C->T);2448(SNP:G->-) 2020-01-01 China</textarea>
                                                    </div>
                                                    <!-- <div class="type-cont">
                                                        <div class="type-group">
                                                            <div class="custom-control custom-radio custom-control-inline">
                                                                <input type="radio" class="custom-control-input" id="tm1" name="tm" value="001" checked>
                                                                <label for="tm1" class="custom-control-label">Upload new samples files</label>
                                                            </div>
                                                            <div class="custom-control custom-radio custom-control-inline">
                                                                <input type="radio" class="custom-control-input" id="tm2" name="tm" value="002">
                                                                <label for="tm2" class="custom-control-label">Upload new haplotype network files</label>
                                                            </div>
                                                        </div>
                                                        <ul class="type-content py-2">
                                                            <li class="">
                                                                <div class="form-group">
                                                                    <label>Sample</label>
                                                                    <input type="file" class="filestyle input-group-sm" data-buttonname="btn-secondary">
                                                                </div>
                                                            </li>
                                                            <li class="d-none">
                                                                <div class="form-group">
                                                                    <label>Nodes</label>
                                                                    <input type="file" class="filestyle input-group-sm" data-buttonname="btn-secondary">
                                                                </div>
                                                                <div class="form-group">
                                                                    <label>Edges</label>
                                                                    <input type="file" class="filestyle input-group-sm" data-buttonname="btn-secondary">
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div> -->
                                                </div>
                                            </div>

                                        </div>
                                        <div class="card-footer bg-white">
                                            <div class="text-center">
                                                <button type="button" class="btn btn-primary mr-2">Run</button>
                                                <button type="reset" class="btn btn-secondary">Reset</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <p>Copyright ©2024 Bioland Laboratory, CAS-MPG Partner Institute for Computational Biology(PICB)</p>
            </div>
        </div>
    </div>
</footer>

<div class="modal fade" id="modal-host">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">新冠病毒属主类型</div>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            </div>
            <div class="table-responsive">
                <table class="table table-sm mb-1">
                    <thead>
                    <tr class="bg-gray">
                        <th class="text-center">属主</th>
                        <th class="text-center">基因数量</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>Homo sapiens</td>
                        <td class="text-center"><a href="" class="badge badge-primary">3</a></td>
                    </tr>
                    <tr>
                        <td>environmental samples</td>
                        <td class="text-center"><a href="" class="badge badge-primary">3</a></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- End Footer -->

<div class="modal fade sel-modal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title font-14 mt-0">选择文件</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm mb-0 treetable">
                            <thead class="thead-light">
                                <tr>
                                    <th>文件名</th>
                                    <th>上传时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr data-tt-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='folder'>fasta_0209</span></td>
                                    <td>--</td>
                                </tr>
                                <tr data-tt-id='1-1' data-tt-parent-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>aaa.fa</span></td>
                                    <td>2020-02-09 11:15:30</td>
                                </tr>
                                <tr data-tt-id='1-2' data-tt-parent-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>bbb.fa</span></td>
                                    <td>2020-02-09 11:15:35</td>
                                </tr>
                                <tr data-tt-id='1-3' data-tt-parent-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>ccc.fa</span></td>
                                    <td>2020-02-09 11:15:40</td>
                                </tr>
                                <tr data-tt-id='1-4' data-tt-parent-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>ddd.fa</span></td>
                                    <td>2020-02-09 11:18:40</td>
                                </tr>
                                <tr data-tt-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='folder'>fasta_0208</span></td>
                                    <td>--</td>
                                </tr>
                                <tr data-tt-id='2-1' data-tt-parent-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>aaa.fa</span></td>
                                    <td>2020-02-09 11:15:30</td>
                                </tr>
                                <tr data-tt-id='2-2' data-tt-parent-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>bbb.fa</span></td>
                                    <td>2020-02-09 11:15:35</td>
                                </tr>
                                <tr data-tt-id='2-3' data-tt-parent-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>ccc.fa</span></td>
                                    <td>2020-02-09 11:15:40</td>
                                </tr>
                                <tr data-tt-id='2-4' data-tt-parent-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>ddd.fa</span></td>
                                    <td>2020-02-09 11:18:40</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-link waves-effect" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary waves-effect waves-light">确定</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

<script src="assets/js/jquery.min.js"></script>
<script src="assets/libs/bootstrap-4.3.1-dist/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/jquery.slimscroll.js"></script>
<script src="assets/js/waves.min.js"></script>
<script src="assets/libs/bootstrap-filestyle/js/bootstrap-filestyle.min.js"></script>
<script src="assets/libs/treetable/js/jquery.treetable.js"></script>
<script src="assets/libs/select2/js/select2.min.js"></script>
<script src="assets/js/common.js"></script>
<script>
    $(document).ready(function () {
        // $('.type-group input').change(function () {
        //     var aa = $(this).parent().index();
        //     $('.type-content').find('li').addClass('d-none').eq(aa).removeClass('d-none');
        // });
        $(".select2").select2();
    })
</script>
</body>
</html>
