.mauve-viewer {
    font-family: '<PERSON><PERSON>', sans-serif;
}


/**
 * general utils
 */

.mauve-viewer .pull-left {
    float: left;
}

.mauve-viewer .pull-right {
    float: right;
}

.mauve-viewer .align-left {
    text-align: left;
}

.mauve-viewer .align-right {
    text-align: right;
}

.mauve-viewer .caret-down {
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #222;
    display: inline-block;
}

.mauve-viewer .spacer {
    display: inline-block;
    margin: 0 15px;
    height: 30px;
}


/**
 * mv ele styling
 */

.mauve-viewer table.cursor-info {
    font-size: .9em;
    width: inherit;
}

.mauve-viewer table.cursor-info th {
    font-weight: bold;
}

.mauve-viewer table.cursor-info tr>* {
    padding: 0 30px 0 30px;
}


/**
  * track control
  */

.mauve-viewer .track-ctrl button {
    display: flex;
    flex-direction: column;
    height: 20px;
    font-size: 8px;
    font-weight: 600;
}

.region {
    opacity: 0.8 !important;
}

.ref-btn {
    overflow: hidden;
    height: 0!important;
}

.track.id-1 .track-label {
    font-size: 15px!important;
    fill: red!important;
    font-weight: 800;
}

.mauve-viewer .track-ctrl button.ref-btn {
    color: #b50707;
}

.mauve-viewer .track-ctrl button.active {
    background: #b50707;
    color: #fff;
    border-color: #aaa;
}

.mauve-viewer .track-ctrl button[disabled] {
    background-color: #f2f2f2;
}

.mauve-viewer .track-ctrl button[disabled]:hover {
    cursor: not-allowed;
}


/**
 * global buttons
 */

.mauve-viewer button {
    background-color: #fff;
    border: 1px solid #ddd;
}

.mauve-viewer button:hover {
    background-color: #f2f2f2;
}

.mauve-viewer button:focus {
    outline: none;
}

.mauve-viewer .options button {
    height: 30px;
    display: inline-block;
    vertical-align: top;
}

.mauve-viewer .options .pan,
.mauve-viewer .options .zoom {
    width: 37px;
    padding: 2px 5px;
}


/**
 *  dropdowns
 */

.mauve-viewer .dropdown {
    position: relative;
    display: inline;
}

.mauve-viewer .dd-content {
    display: none;
    position: absolute;
    left: 0;
    background-color: #fff;
    min-width: 200px;
    overflow: auto;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    font-size: .9em;
}

.mauve-viewer .dd-content div {
    color: #222;
    user-select: none;
    padding: 5px 10px;
}

.mauve-viewer .dd-content div:hover {
    background: #efefef;
}

.mauve-viewer .dd-content label {
    display: block;
    width: 100%;
    height: 100%;
}

.mauve-viewer .show {
    display: block;
}


/**
 * hover positional info
 */

.mauve-viewer dl {
    position: inline-block;
}


/**
 *  tooltips
 */

.tooltip {
    position: absolute;
    width: inherit;
    height: 150px;
    padding: 7px;
    color: #fff;
    background: #444;
    border: 2px solid #000;
    pointer-events: none;
}

.tooltip div {
    margin: 0 0 10px 0;
}

.tooltip div.help-text {
    position: absolute;
    bottom: 0;
    right: 7;
    font-size: .7em;
    font-style: italic;
}