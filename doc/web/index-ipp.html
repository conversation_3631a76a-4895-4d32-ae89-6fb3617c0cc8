<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>病原微生物综合分析云平台</title>
    <link rel="stylesheet" href="assets/libs/bootstrap-4.3.1-dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/libs/treetable/css/jquery.treetable.css">
    <link rel="stylesheet" href="assets/libs/treetable/css/jquery.treetable.theme.default.css">
    <link rel="stylesheet" href="assets/css/icons.css">
    <link rel="stylesheet" href="assets/js/jquery-ui/jquery-ui.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<header>
    <div class="container-fluid">
        <nav class="navbar navbar-expand-lg">
            <a class="navbar-brand" href="index.html"><img src="assets/images/logo-ipac.png" alt=""/></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent">
                <span class="fa fa-bars"></span>
            </button>
            <div class="collapse navbar-collapse active" id="navbarSupportedContent">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">HOME</a>
                    </li>
                    <li class="nav-item active">
                        <a class="nav-link" href="index-ipp.html">IPP</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index-venas.html">VENAS</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index-help.html">HELP</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index-contact.html">CONTACT US</a>
                    </li>
                    <!-- <li class="nav-item">
                        <div class="d-flex align-items-center justify-content-center py-1">
                            <a href="register.html" class="btn btn-primary waves-effect waves-light btn-sm mx-2">SIGN UP</a>  
                            <a href="login.html" class="btn btn-outline-primary waves-effect waves-light btn-sm ml-2">SIGN IN</a>
                        </div>
                    </li> -->
                    <li class="dropdown notification-list list-inline-item ml-4">
                        <div class="dropdown notification-list pt-2">
                            <a class="dropdown-toggle nav-user text-primary" data-toggle="dropdown" href="javascript:void(0);" aria-haspopup="false" aria-expanded="false">Anonymous</a>
                            <div class="dropdown-menu dropdown-menu-right profile-dropdown ">
                                <a class="dropdown-item" href="index-my-data.html"><i class="mdi mdi-database m-r-5"></i> My data</a>
                                <a class="dropdown-item" href="index-my-tasks.html"><i class="mdi mdi-file-document-box-outline m-r-5"></i> My task</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-danger" href="index.html"><i class="mdi mdi-power text-danger"></i> Logout</a>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
</header>
<div class="wrapper py-0">
    <div class="container-fluid">
        <div class="page-title-box py-3">
            <div class="row align-items-center">
                <div class="col-sm-12">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.html"><i class="mdi mdi-home-outline"></i></a>
                        </li>
                        <li class="breadcrumb-item active">IPP</li>
                    </ol>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="card card-box">
                    <div class="card-body">
                        <h4 class="mt-0 header-title mb-3">IPP</h4>
                        <div class="info-box">
                            <h6>Instruction</h6>
                            <p class="mb-1"><img src="assets/images/pic-flow01.png" class="img-info" alt=""></p>
                            <p class="mb-1">To speed up the identification of pathogens, we developed the automated Identification Platform for Pathogens (IPP), which takes the metatranscriptomic data measured by next-generation sequencing as input and first filters out the low-quality sequencing data by quality control of the sequence data. Next, we used FastViromeExplorer to compare the remaining sequences with the known pathogenic reference databases to identify possible pathogen species in the samples; and finally, we assembled the genome of the pathogens in the samples based on the reference genomes of the identified pathogens.</p>
                            <p class="mb-1">
                                The system is capable of automatically pairing files named in a certain style, or you can pair them manually. Please do not change the file name after successful pairing. Please try to name your files according to the following style before uploading:
                            </p>
                        </div>
                        <div class="alert alert-warning alert-wth-icon alert-dismissible fade show" role="alert">
                            <span class="alert-icon-wrap"><i class="fas fa-exclamation-circle fa-2x"></i></span> 
                            The system is capable of automatically pairing files with some naming styles, or you can pair them manually. Please do not change the file name after successful pairing. Please try to name them according to the following style before uploading:<br>
                            <strong>XXX1.fq.gz，XXX2.fq.gz</strong> <br>
                            <strong>XXX1.fastq.gz，XXX2.fastq.gz</strong> 
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <form action="" class="ana-form">
                            <div class="row">
                                <div class="offset-md-1 col-md-10">
                                    <div class="card border shadow-sm">
                                        <div class="card-header bg-white">
                                            <div class="d-flex align-content-center justify-content-between">
                                                <h5 class="font-600 font-14 mt-2 mb-0">Select the paired fq.gz files</h5>
                                                <span><a href="javascript:void(0);" class="btn btn-info btn-sm">Adding paired groups</a></span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mx-md-n2">
                                                <div class="col-md-2"><label class="py-1 text-nowrap">Paired group 1：</label></div>
                                                <div class="col-md-4 sel-ele">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <a href="javascript:void(0);" data-toggle="modal" data-target=".sel-modal" class="btn btn-info btn-sm ">Select</a>
                                                        <div class="sel-result">
                                                            <span class="seled">Selected：<b class="text-primary">a_1.fq.gz</b></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4 sel-ele">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <a href="javascript:void(0);" data-toggle="modal" data-target=".sel-modal" class="btn btn-info btn-sm">Select</a>
                                                        <div class="sel-result">
                                                            <span class="seled">Selected：<b class="text-primary">a_1.fq.gz</b></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mx-md-n2">
                                                <div class="col-md-2"><label class="py-1 text-nowrap">Paired group 2：</label></div>
                                                <div class="col-md-4 sel-ele">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <a href="javascript:void(0);" data-toggle="modal" data-target=".sel-modal" class="btn btn-info btn-sm ">Select</a>
                                                        <div class="sel-result">
                                                            <span class="seled">Selected：<b class="text-primary" title="121211212a_1.fq.gz">121211212a_1.fq.gz</b></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4 sel-ele">
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <a href="javascript:void(0);" data-toggle="modal" data-target=".sel-modal" class="btn btn-info btn-sm">Select</a>
                                                        <div class="sel-result">
                                                            <span class="seled">Selected：<b class="text-primary">a_1.fq.gz</b></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2 action-del">
                                                    <a href="javascript:void(0);" class="btn btn-danger btn-sm">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-white">
                                            <div class="text-center">
                                                <button type="button" class="btn btn-primary mr-2">Analysis</button>
                                                <button type="reset" class="btn btn-secondary">Reset</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <p>Copyright ©2024 Bioland Laboratory, CAS-MPG Partner Institute for Computational Biology(PICB)</p>
            </div>
        </div>
    </div>
</footer>

<div class="modal fade" id="modal-host">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">新冠病毒属主类型</div>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            </div>
            <div class="table-responsive">
                <table class="table table-sm mb-1">
                    <thead>
                    <tr class="bg-gray">
                        <th class="text-center">属主</th>
                        <th class="text-center">基因数量</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>Homo sapiens</td>
                        <td class="text-center"><a href="" class="badge badge-primary">3</a></td>
                    </tr>
                    <tr>
                        <td>environmental samples</td>
                        <td class="text-center"><a href="" class="badge badge-primary">3</a></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- End Footer -->

<div class="modal fade sel-modal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title font-14 mt-0">选择文件</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm mb-0 treetable">
                            <thead class="thead-light">
                                <tr>
                                    <th>文件名</th>
                                    <th>上传时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr data-tt-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='folder'>fasta_0209</span></td>
                                    <td>--</td>
                                </tr>
                                <tr data-tt-id='1-1' data-tt-parent-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>aaa.fa</span></td>
                                    <td>2020-02-09 11:15:30</td>
                                </tr>
                                <tr data-tt-id='1-2' data-tt-parent-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>bbb.fa</span></td>
                                    <td>2020-02-09 11:15:35</td>
                                </tr>
                                <tr data-tt-id='1-3' data-tt-parent-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>ccc.fa</span></td>
                                    <td>2020-02-09 11:15:40</td>
                                </tr>
                                <tr data-tt-id='1-4' data-tt-parent-id='1'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>ddd.fa</span></td>
                                    <td>2020-02-09 11:18:40</td>
                                </tr>
                                <tr data-tt-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='folder'>fasta_0208</span></td>
                                    <td>--</td>
                                </tr>
                                <tr data-tt-id='2-1' data-tt-parent-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>aaa.fa</span></td>
                                    <td>2020-02-09 11:15:30</td>
                                </tr>
                                <tr data-tt-id='2-2' data-tt-parent-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>bbb.fa</span></td>
                                    <td>2020-02-09 11:15:35</td>
                                </tr>
                                <tr data-tt-id='2-3' data-tt-parent-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>ccc.fa</span></td>
                                    <td>2020-02-09 11:15:40</td>
                                </tr>
                                <tr data-tt-id='2-4' data-tt-parent-id='2'>
                                    <td><input type="checkbox" style="position:relative;top:3px;margin-right:3px;"><span class='file'>ddd.fa</span></td>
                                    <td>2020-02-09 11:18:40</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-link waves-effect" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary waves-effect waves-light">确定</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

<script src="assets/js/jquery.min.js"></script>
<script src="assets/libs/bootstrap-4.3.1-dist/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/jquery.slimscroll.js"></script>
<script src="assets/js/waves.min.js"></script>
<script src="assets/libs/bootstrap-filestyle/js/bootstrap-filestyle.min.js"></script>
<script src="assets/libs/treetable/js/jquery.treetable.js"></script>
<script src="assets/js/common.js"></script>
<script>
    $(document).ready(function () {
        $(".treetable").treetable({expandable: true});
    })
</script>
</body>
</html>
