FROM tomcat:8.5.100-jdk8
LABEL lfyang=<EMAIL>
WORKDIR /usr/local
COPY require.txt  /usr/local/require.txt
RUN  apt-get -y update && \
     apt-get -y upgrade && \
	ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
	echo 'Asia/Shanghai' > /etc/timezone && \
	apt-get -y install python3.9 && \
	apt-get -y install python3.8-dev && \
	apt-get -y install python3-distutils && \
    wget https://bootstrap.pypa.io/get-pip.py -o /usr/local/get-pip.py && \
    python3 /usr/local/get-pip.py && \
	pip3 install -r /usr/local/require.txt  -i https://pypi.tuna.tsinghua.edu.cn/simple --default-timeout=1000
EXPOSE 8080
CMD ["/usr/local/tomcat/bin/catalina.sh", "run"]