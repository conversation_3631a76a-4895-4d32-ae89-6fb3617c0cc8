
## 2024-07-19 venas脚本更新
> **ipac-app** 
>
>
>
1.执行sql，新增字段
```
#
ALTER TABLE `user_task`
	ADD COLUMN `attr4` VARCHAR(255) NULL DEFAULT NULL AFTER `attr3`;
```

2.将项目中"doc/venas分析/脚本目录结构/scripts"下的所有python脚本上传到务器


## 2024-07-29 反馈修改
> **ipac-app** 
>
>
1.执行sql，新增字段
```
ALTER TABLE `user_task`
	ADD COLUMN `remark` VARCHAR(500) NULL DEFAULT NULL AFTER `task_id`;
```

2.执行sql
```
INSERT INTO `user_task` (`id`, `task_id`, `input_path`, `out_path`, `status`, `type`, `attr1`) VALUES ('reference_haplotype_network_covid19', 'reference_haplotype_network_covid19', '{}', 'reference_haplotype_network_covid19', '7', 'venas1', 'SARS-Cov-19');
INSERT INTO `user_task` (`id`, `task_id`, `input_path`, `out_path`, `status`, `type`, `attr1`) VALUES ('reference_haplotype_network_mtb', 'reference_haplotype_network_mtb', '{}', 'reference_haplotype_network_mtb', '7', 'venas2', 'M.tuberculosis');
```
3.将项目中"doc/venas分析/脚本目录结构/reference_haplotype_network_covid19" 
 "doc/venas分析/脚本目录结构/reference_haplotype_network_mtb" 两个目录上传到务器，与rawdata目录同级



