#spring.rabbitmq.host=*************
#spring.rabbitmq.port=24005
#spring.rabbitmq.username=vic
#spring.rabbitmq.password=vic
#spring.rabbitmq.virtual-host=/vic

spring.rabbitmq.host=analysis-rabbitmq.analysis
spring.rabbitmq.port=5672
spring.rabbitmq.username=ipac
spring.rabbitmq.password=ipac
spring.rabbitmq.virtual-host=/ipac
spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true
spring.rabbitmq.template.mandatory=true
spring.rabbitmq.listener.simple.acknowledge-mode=manual
spring.rabbitmq.listener.simple.concurrency=5
spring.rabbitmq.listener.simple.max-concurrency=10
ncov.rabbitmq.task.queueName=analysis-task-queue
ncov.rabbitmq.result.queueName=analysis-result-queue
# è½¬ç§»è³åææå¡å¨çå­æ¾è·¯å¾
ncov.web.rootPath=/usr/local/webdata/analysis/result
ncov.ftp.rootPath=/data/ftp
ncov.analysis.input= /data/analysis/input
ncov.analysis.output= /data/analysis/output
logging.level.org.picb=debug
