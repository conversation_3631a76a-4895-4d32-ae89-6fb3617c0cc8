spring.rabbitmq.host=**************
spring.rabbitmq.port=5374
spring.rabbitmq.username=ncov
spring.rabbitmq.password=ncov
spring.rabbitmq.virtual-host=/ncov
spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true
spring.rabbitmq.template.mandatory=true
spring.rabbitmq.listener.simple.acknowledge-mode=manual
spring.rabbitmq.listener.simple.concurrency=5
spring.rabbitmq.listener.simple.max-concurrency=10
ncov.rabbitmq.task.queueName=analysis-task-queue
ncov.rabbitmq.result.queueName=analysis-result-queue
# è½¬ç§»è³åææå¡å¨çå­æ¾è·¯å¾
ncov.web.rootPath=/Users/<USER>/test/web
ncov.ftp.rootPath=/Users/<USER>/test/ftp
ncov.analysis.input=/Users/<USER>/test/analysis/input
ncov.analysis.output=/Users/<USER>/test/analysis/output
logging.level.org.picb=debug
