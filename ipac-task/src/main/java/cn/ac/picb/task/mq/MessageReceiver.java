package cn.ac.picb.task.mq;

import cn.ac.picb.task.service.FileService;
import cn.ac.picb.task.vo.*;
import com.rabbitmq.client.Channel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MessageReceiver {

    private final MessageSender messageSender;
    private final FileService fileService;

    public MessageReceiver(MessageSender messageSender, FileService fileService) {
        this.messageSender = messageSender;
        this.fileService = fileService;
    }

    /**
     * 处理web端新增任务时发送端文件传输
     *
     * @param msg     msg
     * @param channel channel
     * @param headers headers?
     */
    @RabbitListener(queues = "${ncov.rabbitmq.task.queueName}")
    @RabbitHandler
    @SneakyThrows
    public void receiveTaskMessage(@Payload AnalysisTask msg, Channel channel, @Headers Map<String, Object> headers) {
        log.info("接收到web服务器移动文件的任务---------------------");
        log.info("消费端Payload: " + msg);

        try {
            // 重命名文件并转移至指定目录
            String path = fileService.moveSourceFile(msg.getTaskId(), msg.getType(),  msg.getFiles());
            log.debug("移动之后的地址为：[{}]", path);

            // 存放在分析服务器的地址
            String output = msg.getTaskId();
            log.debug("分析结果存放的地址为：[{}]", output);

            // 发送任务状态  2: 数据准备完成
            messageSender.sendStatusMessage(new AnalysisStatus(msg.getTaskId(), 2));

            AnalysisFile analysisFile = new AnalysisFile(msg.getTaskId(), path, output);
            messageSender.sendFileMessage(analysisFile);
        } catch (Exception e) {
            messageSender.sendErrorMessage(new AnalysisError(msg.getTaskId(), 10000, e.getMessage()));
        }

        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        channel.basicAck(deliveryTag, false);
    }


    /**
     * 处理分析端分析完成的任务
     *
     * @param msg     msg
     * @param channel channel
     * @param headers headers?
     */
    @RabbitListener(queues = "${ncov.rabbitmq.result.queueName}")
    @RabbitHandler
    @SneakyThrows
    public void receiveResultMessage(@Payload AnalysisResult msg, Channel channel, @Headers Map<String, Object> headers) {
        log.info("接收到web服务器移动文件的任务---------------------");
        log.info("消费端Payload: " + msg);

        try {
            // 将分析结果转移至指定目录
            String path = fileService.moveResultFile(msg.getTaskId(), msg.getPath());
            log.debug("移动之后的地址为：[{}]", path);

            messageSender.sendWebMessage(new AnalysisResult(msg.getTaskId(), path));
        } catch (Exception e) {
            messageSender.sendErrorMessage(new AnalysisError(msg.getTaskId(), 30000, e.getMessage()));
        }
        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        channel.basicAck(deliveryTag, false);
    }
}
