package cn.ac.picb.task.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisError implements Serializable {

    private String taskId;

    @JsonProperty("error_code")
    private Integer errorCode;

    private String message;
}
