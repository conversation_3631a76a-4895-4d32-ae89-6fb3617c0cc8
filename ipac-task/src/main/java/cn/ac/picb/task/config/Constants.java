package cn.ac.picb.task.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class Constants {

    public static String webRootPath;
    /**
     * ftp存放根目录
     */
    public static String ftpRootPath;

    /**
     * 分析服务器原始文件存放根路径
     */
    public static String analysisInput;

    /**
     * 分析服务器分析结果存放根路径
     */
    public static String analysisOutput;

    @Value("${ncov.web.rootPath}")
    public void setWebRootPath(String webRootPath) {
        Constants.webRootPath = webRootPath;
    }

    @Value("${ncov.ftp.rootPath}")
    public void setFtpRootPath(String ftpRootPath) {
        Constants.ftpRootPath = ftpRootPath;
    }

    @Value("${ncov.analysis.input}")
    public void setAnalysisInput(String analysisInput) {
        Constants.analysisInput = analysisInput;
    }

    @Value("${ncov.analysis.output}")
    public void setAnalysisOutput(String analysisOutput) {
        Constants.analysisOutput = analysisOutput;
    }
}
