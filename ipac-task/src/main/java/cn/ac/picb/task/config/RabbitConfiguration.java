package cn.ac.picb.task.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class RabbitConfiguration {

    private final MqProperties mqProperties;

    public RabbitConfiguration(MqProperties mqProperties) {
        this.mqProperties = mqProperties;
    }

    @Bean
    public DirectExchange directExchange() {
        return ExchangeBuilder
                .directExchange(mqProperties.getExchange().getName())
                .ignoreDeclarationExceptions()
                .build();
    }

    /**
     * web创建分析任务推送消息队列
     *
     * @return queue
     */
    @Bean
    public Queue taskQueue() {
        return new Queue(mqProperties.getTask().getQueueName());
    }

    /**
     * 错误创建分析任务推送消息队列
     *
     * @return queue
     */
    @Bean
    public Queue errorQueue() {
        return new Queue(mqProperties.getError().getQueueName());
    }

    /**
     * 错误创建分析任务状态推送消息队列
     *
     * @return queue
     */
    @Bean
    public Queue statusQueue() {
        return new Queue(mqProperties.getStatus().getQueueName());
    }

    /**
     * 分析程序分析完成知乎推送结果队列
     *
     * @return
     */
    @Bean
    public Queue fileQueue() {
        return new Queue(mqProperties.getFile().getQueueName());
    }

    /**
     * 分析程序分析完成知乎推送结果队列
     *
     * @return
     */
    @Bean
    public Queue webQueue() {
        return new Queue(mqProperties.getWeb().getQueueName());
    }


    @Bean
    public Queue resultQueue() {
        return new Queue(mqProperties.getResult().getQueueName());
    }


    @Bean
    public Binding bindingAnalysisQueue(DirectExchange directExchange, Queue taskQueue) {
        return BindingBuilder
                .bind(taskQueue)
                .to(directExchange)
                .with(mqProperties.getTask().getRoutingKey());
    }

    @Bean
    public Binding bindingFileQueue(DirectExchange directExchange, Queue fileQueue) {
        return BindingBuilder
                .bind(fileQueue)
                .to(directExchange)
                .with(mqProperties.getFile().getRoutingKey());
    }

    @Bean
    public Binding bindingWebQueue(DirectExchange directExchange, Queue webQueue) {
        return BindingBuilder
                .bind(webQueue)
                .to(directExchange)
                .with(mqProperties.getWeb().getRoutingKey());
    }

    @Bean
    public Binding bindingErrorQueue(DirectExchange directExchange, Queue errorQueue) {
        return BindingBuilder
                .bind(errorQueue)
                .to(directExchange)
                .with(mqProperties.getError().getRoutingKey());
    }

    @Bean
    public Binding bindingStatusQueue(DirectExchange directExchange, Queue statusQueue) {
        return BindingBuilder
                .bind(statusQueue)
                .to(directExchange)
                .with(mqProperties.getStatus().getRoutingKey());
    }

    @Bean
    public Binding bindingResultQueue(DirectExchange directExchange, Queue resultQueue) {
        return BindingBuilder
                .bind(resultQueue)
                .to(directExchange)
                .with(mqProperties.getResult().getRoutingKey());
    }

    @Bean
    public Jackson2JsonMessageConverter jackson2MessageConverter() {
        return new Jackson2JsonMessageConverter();
    }

}
