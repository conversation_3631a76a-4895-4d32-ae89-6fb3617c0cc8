package cn.ac.picb.task;

import cn.ac.picb.task.config.MqProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableConfigurationProperties(value = {MqProperties.class})
public class NcovTaskApplication extends SpringBootServletInitializer {

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(NcovTaskApplication.class);
	}

    public static void main(String[] args) {
        SpringApplication.run(NcovTaskApplication.class, args);
    }
}

