package cn.ac.picb.task.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisStatus implements Serializable {

    private String taskId;

    @JsonProperty("status_code")
    private Integer statusCode;
}
