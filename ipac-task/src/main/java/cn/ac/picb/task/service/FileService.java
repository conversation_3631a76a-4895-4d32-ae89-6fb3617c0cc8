package cn.ac.picb.task.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.ac.picb.task.config.Constants;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileService {

//    private static final String RSYNC_CMD = "rsync -aL %s %s";
    private static final String RSYNC_CMD = "cp -rf %s %s";

    /**
     * 将选择的文件重命名之后移动至指定的位置
     *
     * @param taskId taskId
     * @param files  用户ftp空间中的文件路径
     * @return 移动之后的路径
     */
    @SneakyThrows
    public String moveSourceFile(String taskId, String type,  List<List<String>> files) {
        if (CollUtil.isEmpty(files)) {
            throw new RuntimeException("The file specified by the user cannot be empty");
        }

        for (int i = 0; i < files.size(); i++) {
            List<String> pair = files.get(i);

            if (CollUtil.isEmpty(pair) || pair.size() != 2) {
                throw new RuntimeException("The number of files specified by the user is incorrect");
            }

            for (int j = 0; j < pair.size(); j++) {
                int index = j + 1;
                String path = Constants.ftpRootPath + File.separator + pair.get(j);
                String destPath = Constants.analysisInput + File.separator + ("ipp".equalsIgnoreCase(type)? "iPP": "VENAS") + File.separator + taskId + File.separator + "seqfiles" + (i+1) + "_" + index + ".fq.gz";
                log.info("path: {}", path);
                log.info("dest-path: {}", destPath);
                log.info("-------------------------------------------------------------------");
                FileUtil.touch(destPath);
                copyFile(path, destPath);
            }
        }
        return taskId;
    }

    /**
     * 将分析结果文件移动至指定的位置
     *
     * @param taskId taskId
     * @param path   分析结果原始路径
     * @return 移动之后存放的结果路径
     */
    @SneakyThrows
    public String moveResultFile(String taskId, String path) {
        String report = Constants.analysisOutput + path + File.separator + "reports";
        String assembler = Constants.analysisOutput + path + File.separator + "results_assembler";

        if (!FileUtil.exist(report) || !FileUtil.isDirectory(report)) {
            log.error("分析结果reports文件夹不存在，文件路径为: {}", report);
            throw new RuntimeException("There was an error in the analysis");
        }

        if (!FileUtil.exist(assembler) || !FileUtil.isDirectory(assembler)) {
            log.error("分析结果results_assembler文件夹不存在，文件路径为: {}", assembler);
            throw new RuntimeException("There was an error in the analysis");
        }
        // rsync 方式
//        String reportDestPath = Constants.webRootPath + File.separator + taskId  + File.separator + "reports" ;
//        String assemblerDestPath = Constants.webRootPath + File.separator + taskId  + File.separator + "results_assembler" ;

        //copy方式
        String reportDestPath = Constants.webRootPath + File.separator + taskId  + File.separator ;
        String assemblerDestPath = Constants.webRootPath + File.separator + taskId  + File.separator;

        FileUtil.mkdir(reportDestPath);
        FileUtil.mkdir(assemblerDestPath);
        copyFile(report + File.separator, reportDestPath);
        copyFile(assembler + File.separator, assemblerDestPath);
        //FileUtil.del(source);
        return File.separator + taskId;
    }

    @SneakyThrows
    private void copyFile(String path, String destPath) {
        log.info("copy file: source path: [{}] -----> dest path : [{}]", path, destPath);
        if (!FileUtil.exist(path)) {
            throw new RuntimeException("The file specified by the user does not exist, and the file path is：{}" + path);
        }

        String cmd = String.format(RSYNC_CMD, path, destPath);
        try {
            log.debug("进行文件移动：command: {}", cmd);
            Runtime.getRuntime().exec(cmd).waitFor();
        } catch (Exception e) {
            log.error("执行文件移动命令出错：command: {}", cmd);
            throw new RuntimeException("There was an error in data preparation");
        }
    }
}
