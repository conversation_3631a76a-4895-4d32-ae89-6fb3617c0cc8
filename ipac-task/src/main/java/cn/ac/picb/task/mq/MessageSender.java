package cn.ac.picb.task.mq;

import cn.ac.picb.task.config.MqProperties;
import cn.ac.picb.task.vo.AnalysisError;
import cn.ac.picb.task.vo.AnalysisFile;
import cn.ac.picb.task.vo.AnalysisResult;
import cn.ac.picb.task.vo.AnalysisStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MessageSender {

    private final RabbitTemplate rabbitTemplate;
    private final MqProperties mqProperties;

    final RabbitTemplate.ConfirmCallback confirmCallback = (correlationData, ack, cause) -> {
        log.debug("correlationData: {}", correlationData);
        log.debug("ack: {}", ack);
        if (!ack) {
            log.error("未确认。。。。。。异常处理");
        } else {
            log.debug("投递成功");
        }
    };

    final RabbitTemplate.ReturnCallback returnCallback = (message, replyCode, replyText, exchange, routingKey) -> log.debug("exchange: {} ---- routingKey: {} ---- replyCode: {} ----replyText: {}", exchange, routingKey, replyCode, replyText);

    /**
     * 发生错误消息
     *
     * @param msg task
     */
    public void sendErrorMessage(AnalysisError msg) {
        rabbitTemplate.setConfirmCallback(confirmCallback);
        rabbitTemplate.setReturnCallback(returnCallback);

        CorrelationData correlationData = new CorrelationData(msg.getTaskId() + System.currentTimeMillis());
        rabbitTemplate.convertAndSend(mqProperties.getExchange().getName(), mqProperties.getError().getRoutingKey(), msg, correlationData);
        log.debug("错误消息发送成功，消息为：{}", msg);
    }

    /**
     * 状态修改消息
     *
     * @param msg task
     */
    public void sendStatusMessage(AnalysisStatus msg) {
        rabbitTemplate.setConfirmCallback(confirmCallback);
        rabbitTemplate.setReturnCallback(returnCallback);

        CorrelationData correlationData = new CorrelationData(msg.getTaskId() + System.currentTimeMillis());
        rabbitTemplate.convertAndSend(mqProperties.getExchange().getName(), mqProperties.getStatus().getRoutingKey(), msg, correlationData);
        log.debug("状态变更消息发送成功，消息为：{}", msg);
    }

    /**
     * 发送分析任务开始的消息
     *
     * @param msg task
     */
    public void sendFileMessage(AnalysisFile msg) {
        rabbitTemplate.setConfirmCallback(confirmCallback);
        rabbitTemplate.setReturnCallback(returnCallback);

        CorrelationData correlationData = new CorrelationData(msg.getTaskId() + System.currentTimeMillis());
        rabbitTemplate.convertAndSend(mqProperties.getExchange().getName(), mqProperties.getFile().getRoutingKey(), msg, correlationData);

        log.debug("发送至 file 队列的消息为：[{}]", msg);
    }

    /**
     * 分析完成结果消息
     *
     * @param msg task
     */
    public void sendWebMessage(AnalysisResult msg) {
        rabbitTemplate.setConfirmCallback(confirmCallback);
        rabbitTemplate.setReturnCallback(returnCallback);

        CorrelationData correlationData = new CorrelationData(msg.getTaskId() + System.currentTimeMillis());
        rabbitTemplate.convertAndSend(mqProperties.getExchange().getName(), mqProperties.getWeb().getRoutingKey(), msg, correlationData);

        log.debug("发送至 web 队列的消息为：[{}]", msg);
    }

}
