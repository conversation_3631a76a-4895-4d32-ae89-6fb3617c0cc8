package org.picb.ncov.mq;

import cn.ac.picb.task.mq.MessageSender;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.picb.ncov.NcovTaskApplicationTests;
import cn.ac.picb.task.vo.AnalysisFile;
import cn.ac.picb.task.vo.AnalysisStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

/**
 * <AUTHOR>
 */
class MessageSenderTest extends NcovTaskApplicationTests {

    @Autowired
    private MessageSender messageSender;

    @Test
    void sendErrorMessage() {
    }

    @Test
    @SneakyThrows
    void sendStatusMessage() {
        for (int i = 0; i < 1000; i++) {
            messageSender.sendStatusMessage(new AnalysisStatus(UUID.randomUUID().toString(), 2));
        }
    }

    @Test
    void sendFileMessage() {

        for (int i = 0; i < 100; i++) {
            String id = UUID.randomUUID().toString();
            messageSender.sendFileMessage(new AnalysisFile(id, "/input/" + id, "/output"));
        }
    }

    @Test
    void sendWebMessage() {
    }
}
