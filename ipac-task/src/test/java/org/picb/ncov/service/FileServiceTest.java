package org.picb.ncov.service;

import cn.ac.picb.task.service.FileService;
import org.junit.jupiter.api.Test;
import org.picb.ncov.NcovTaskApplicationTests;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
class FileServiceTest extends NcovTaskApplicationTests {

    @Autowired
    private FileService fileService;

    @Test
    void moveSourceFile() {
        String taskId = UUID.randomUUID().toString();
        List<List<String>> files = new ArrayList<>();
        files.add(Arrays.asList("a11111.fq.gz", "a2222.fq.gz"));
        files.add(Arrays.asList("b1111.fq.gz", "b2222.fq.gz"));
        fileService.moveSourceFile(taskId, "ipp", files);
    }

    @Test
    void moveResultFile() {
        String taskId = "fedd7134-0dcd-4999-bd46-227016e6bfdd";
        String path = "/fedd7134-0dcd-4999-bd46-227016e6bfdd";
        String moveResultFile = fileService.moveResultFile(taskId, path);
        System.out.println(moveResultFile);
    }
}
