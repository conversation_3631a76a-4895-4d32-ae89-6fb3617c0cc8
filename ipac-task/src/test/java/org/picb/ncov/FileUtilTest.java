package org.picb.ncov;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 */
@Slf4j
public class FileUtilTest {

    private static final String RSYNC_CMD = "rsync -aL %s %s";

    @SneakyThrows
    public static void main(String[] args) {
//        FileUtil.mkdir("/Users/<USER>/test/aaa/bbbb");

        String cmd = String.format(RSYNC_CMD, "/Users/<USER>/test/ftp/a11111.fq.gz /Users/<USER>/test/a & rm -r /Users/<USER>/test/b/a.txt", "/Users/<USER>/test/a");
        log.debug("进行文件移动：command: {}", cmd);

        Process process = Runtime.getRuntime().exec(cmd);
        process.waitFor();

        BufferedReader b = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line = "";
        StringBuffer sb = new StringBuffer();
        while ((line = b.readLine()) != null) {
            sb.append(line).append("\n");
        }
        System.out.println("result： " + sb.toString());
        b.close();
    }
}
