ARG buildVersion=picb

# 基础环境
FROM airdock/oraclejdk:1.8 AS base

MAINTAINER <EMAIL>

ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-Djava.security.egd=file:/dev/./urandom"

RUN apt-get update && apt-get install -y --no-install-recommends rsync && \
  rm -rf /var/lib/apt/lists/* && \
  ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone && \
  mkdir -p /jar

COPY target/*.jar /jar/app.jar

# picb env
FROM base AS branch-version-picb

RUN groupadd -g 1102 bdp && \
  useradd -u 5294 -g bdp analysiswww && \
  echo 'root:imac@2020' | chpasswd && \
  chown -R analysiswww:bdp /jar

RUN echo "build image for picb..."

USER analysiswww

# other env

FROM base AS branch-version-other
RUN echo "build image for other..."

FROM branch-version-${buildVersion} AS final

WORKDIR /jar

EXPOSE 8080

VOLUME /data/ftp
VOLUME /data/logs

CMD java $JAVA_OPTS -jar app.jar
