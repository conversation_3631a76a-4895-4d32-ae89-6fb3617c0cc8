package cn.ac.picb.file.service;

import cn.ac.picb.common.framework.util.ServiceExceptionUtil;
import cn.ac.picb.file.config.FileProperties;
import cn.ac.picb.file.dto.FileDTO;
import cn.ac.picb.file.dto.TitleDTO;
import cn.ac.picb.file.enums.ErrorCodeConstants;
import cn.ac.picb.file.util.FilePathUtil;
import cn.ac.picb.file.util.FileSizeUtil;
import cn.ac.picb.file.vo.*;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributeView;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FileService {

    private final FileProperties fileProperties;

    public List<TreeNode> findTreeData(PathParamVO param, boolean withChildren) {
        String currentPath = FilePathUtil.getCurrentPath(param.getPath());
        String rootPath = FilePathUtil.getRootPath(param.getUsername(), fileProperties.getHome());
        return getNodes(currentPath, rootPath, withChildren);
    }


    private List<TreeNode> getNodes(String currentPath, String basePath, boolean withChildren) {
        File current = new File(basePath, currentPath);
        if (!FileUtil.exist(current) || FileUtil.isFile(current)) {
            return Collections.emptyList();
        }
        List<TreeNode> nodes;
        if (FileUtil.isDirectory(current)) {
            nodes = Arrays.stream(Objects.requireNonNull(current.listFiles()))
                    .map(file -> fileToTreeNode(file, basePath, withChildren))
                    .sorted(Comparator.comparing(TreeNode::getTitle))
                    .collect(Collectors.toList());
        } else {
            TreeNode node = fileToTreeNode(current, basePath, withChildren);
            nodes = Collections.singletonList(node);
        }
        return nodes;
    }

    @SneakyThrows
    private TreeNode fileToTreeNode(File file, String basePath, boolean withChildren) {
        String absolutePath = FileUtil.getAbsolutePath(file);
        BasicFileAttributeView view = Files.getFileAttributeView(Paths.get(file.toURI()), BasicFileAttributeView.class, LinkOption.NOFOLLOW_LINKS);
        BasicFileAttributes attributes = view.readAttributes();

        boolean isDirectory = attributes.isDirectory();

        TreeNode build = TreeNode.builder()
                .key(IdUtil.simpleUUID())
                .title(file.getName())
                .data(TreeNodeData.builder()
                        .name(file.getName())
                        .path(FileUtil.subPath(basePath, absolutePath))
                        .size(FileSizeUtil.sizeFormat(attributes.size()))
                        .time(Date.from(attributes.lastModifiedTime().toInstant()))
                        .build())
                .folder(isDirectory)
                .expanded(!isDirectory)
                .lazy(isDirectory)
                .build();
        if (isDirectory && withChildren) {
            build.setChildren(getChildren(file, basePath, withChildren));
        }
        return build;
    }

    private List<TreeNode> getChildren(File file, String basePath, boolean withChildren) {
        String currentPath = FileUtil.subPath(basePath, file);
        return getNodes(currentPath, basePath, withChildren);
    }

    public FileList listDir(PathParamVO param) {
        // 当前路径
        String currentPath = FilePathUtil.getCurrentPath(param.getPath());
        // 根路径
        String rootPath = FilePathUtil.getRootPath(param.getUsername(), fileProperties.getHome());

        FileList fileList = new FileList();

        fileList.setCurrentPath(currentPath);

        List<TitleDTO> titles = getPathTitle(currentPath);
        fileList.setTitles(titles);

        File current = new File(rootPath, currentPath);
        if (!FileUtil.exist(current)) {
            // 路径不存在，则显示家目录
            current = new File(rootPath);
        }

        if (FileUtil.isDirectory(current)) {
            List<FileDTO> files = Arrays.stream(Objects.requireNonNull(current.listFiles()))
                    .map(file -> fileToDTO(file, rootPath))
                    .sorted(Comparator.comparing(FileDTO::getName))
                    .collect(Collectors.toList());
            fileList.setFiles(files);
        } else {
            FileDTO dto = fileToDTO(current, rootPath);
            fileList.setFiles(Collections.singletonList(dto));
        }

        return fileList;
    }

    @SneakyThrows
    private FileDTO fileToDTO(File file, String basePath) {
        FileDTO dto = new FileDTO();
        dto.setName(file.getName());

        String absolutePath = FileUtil.getAbsolutePath(file);
        dto.setPath(absolutePath.replace(basePath, ""));

        BasicFileAttributeView view = Files.getFileAttributeView(Paths.get(file.toURI()), BasicFileAttributeView.class, LinkOption.NOFOLLOW_LINKS);
        BasicFileAttributes attributes = view.readAttributes();
        dto.setType(attributes.isDirectory() ? "D" : "F");
        dto.setSize(FileSizeUtil.sizeFormat(attributes.size()));
        dto.setLastModifyTime(Date.from(attributes.lastModifiedTime().toInstant()));
        return dto;
    }


    /**
     * 获取路径表头信息
     *
     * @param path 路径
     * @return titles
     */
    private List<TitleDTO> getPathTitle(String path) {
        List<String> paths = StrUtil.split(path, CharUtil.SLASH);
        log.debug("paths: {}", paths);
        paths.remove(0);

        List<TitleDTO> titles = new ArrayList<>();
        for (int i = 0; i < paths.size(); i++) {
            TitleDTO title = new TitleDTO();
            title.setName(paths.get(i));

            log.debug("subList: {}", paths.subList(0, i + 1));

            title.setPath("/" + StrUtil.join("/", paths.subList(0, i + 1)));
            titles.add(title);
        }
        return titles;
    }

    public List<DirectoryTreeNode> listDirectory(PathParamVO param) {
        if (StrUtil.isBlank(param.getPath())) {
            return Collections.singletonList(DirectoryTreeNode.builder()
                    .name("根目录")
                    .path("/")
                    .parentPath("")
                    .build());
        }

        String currentPath = FilePathUtil.getCurrentPath(param.getPath());
        String rootPath = FilePathUtil.getRootPath(param.getUsername(), fileProperties.getHome());

        File current = new File(rootPath, currentPath);
        if (!FileUtil.exist(current) || FileUtil.isFile(current)) {
            return Collections.emptyList();
        }

        return Arrays.stream(Objects.requireNonNull(current.listFiles()))
                .filter(File::isDirectory)
                .map(file -> fileToDirectoryTreeNode(file, currentPath, rootPath))
                .sorted(Comparator.comparing(DirectoryTreeNode::getName))
                .collect(Collectors.toList());
    }

    @SneakyThrows
    private DirectoryTreeNode fileToDirectoryTreeNode(File file, String currentPath, String basePath) {
        String absolutePath = FileUtil.getAbsolutePath(file);

        return DirectoryTreeNode.builder()
                .name(file.getName())
                .path(absolutePath.replace(basePath, ""))
                .parentPath(currentPath)
                .build();
    }

    public void createFolder(FolderParamVO folderParamVO) {

        if (StrUtil.containsAny(folderParamVO.getName(), StrUtil.DOT, StrUtil.SPACE)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ERROR_INFO, "The folder name cannot contain '.' and spaces");
        }

        String rootPath = FilePathUtil.getRootPath(folderParamVO.getUsername(), fileProperties.getHome());

        File root = new File(rootPath, folderParamVO.getPath());
        if (!FileUtil.exist(root) || FileUtil.isFile(root)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ERROR_INFO, "The path is incorrect, and the addition fails");
        }

        File file = FileUtil.file(root, folderParamVO.getName());
        if (FileUtil.exist(file)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ERROR_INFO, "The path is incorrect, and the addition fails");
        }
        FileUtil.mkdir(file);
    }

    @SneakyThrows
    public void uploadFile(String username, String parentPath, MultipartFile file) {
        String currentPath = FilePathUtil.getCurrentPath(parentPath);
        String rootPath = FilePathUtil.getRootPath(username, fileProperties.getHome());

        File current = new File(rootPath, currentPath);
        if (!FileUtil.exist(current) || FileUtil.isFile(current)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.UPLOAD_PATH_ERROR);
        }

        String fileName = StrUtil.blankToDefault(file.getOriginalFilename(), DateUtil.format(new Date(), "yyyyMMddHHmmmm"));
        File dest = new File(current, fileName);
        if (dest.exists()) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.UPLOAD_PATH_ERROR);
        }
        FileUtil.writeFromStream(file.getInputStream(), dest);
    }


    @SneakyThrows
    public void uploadFiles(String username, String parentPath) {
        File file = new File(parentPath);
        String currentPath = FilePathUtil.getCurrentPath(parentPath);
        String rootPath = FilePathUtil.getRootPath(username, fileProperties.getHome());

        File current = new File(rootPath, currentPath);
        if (!FileUtil.exist(current) || FileUtil.isFile(current)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.UPLOAD_PATH_ERROR);
        }

        String fileName = StrUtil.blankToDefault(file.getName(), DateUtil.format(new Date(), "yyyyMMddHHmmmm"));
        File dest = new File(current, fileName);
        if (dest.exists()) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.UPLOAD_PATH_ERROR);
        }
        InputStream inputStream = new FileInputStream(file);
        FileUtil.writeFromStream(inputStream, dest);
    }


    public void deleteFile(PathVO vo) {
        String[] paths = vo.getPaths();
        String username = vo.getUsername();
        if (ArrayUtil.isEmpty(paths)) {
            return;
        }
        Arrays.stream(paths).forEach(path -> {
            String currentPath = FilePathUtil.getCurrentPath(path);
            String rootPath = FilePathUtil.getRootPath(username, fileProperties.getHome());
            File current = new File(rootPath, currentPath);
            if (FileUtil.exist(current)) {
                FileUtil.del(current);
            }
        });
    }


    public static void main(String[] args) throws Exception {
        String parentPath = "D:\\Users\\maling\\test\\logs\\app-scs-admim.log\\spring.log";
        System.out.println(parentPath);
        File file1 = new File(parentPath);
        FileInputStream fileInputStream = null;

        try {
            fileInputStream = new FileInputStream(file1);

        } catch (Exception e) {
            e.printStackTrace();
        }

        String currentPath = FilePathUtil.getCurrentPath(parentPath);
//        String rootPath = FilePathUtil.getRootPath("xkfa", "D:\\work\\test\\ftp\\ftp");
        String rootPath = "D:\\work\\test\\ftp\\ftp\\xkfa\\";
        File file = new File(rootPath);
        file.createNewFile();

        File current = new File(rootPath, currentPath);
        if (FileUtil.isFile(current)) {
            System.out.println(FileUtil.exist(current));
            System.out.println(FileUtil.isFile(current));
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.UPLOAD_PATH_ERROR);
        }

        String fileName = StrUtil.blankToDefault(file1.getName(), DateUtil.format(new Date(), "yyyyMMddHHmmmm"));
        File dest = new File(current, fileName);
        if (dest.exists()) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.UPLOAD_PATH_ERROR1);
        }
        FileUtil.writeFromStream(fileInputStream, dest);
    }
}
