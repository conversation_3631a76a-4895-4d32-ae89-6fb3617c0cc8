package cn.ac.picb.file;

import cn.ac.picb.file.config.FileProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableConfigurationProperties({FileProperties.class})
public class FileApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(FileApplication.class);
    }


   public static void main(String[] args) {
       SpringApplication.run(FileApplication.class, args);
   }
}
