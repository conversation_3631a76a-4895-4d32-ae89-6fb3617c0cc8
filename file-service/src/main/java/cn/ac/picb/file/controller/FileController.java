package cn.ac.picb.file.controller;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.file.client.FileServiceApi;
import cn.ac.picb.file.service.FileService;
import cn.ac.picb.file.vo.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class FileController implements FileServiceApi {

    private final FileService fileService;

    /**
     * 列出当前目录结果
     *
     * @param param param
     * @return list
     */
    @Override
    public CommonResult<FileList> listDir(PathParamVO param) {
        FileList data = fileService.listDir(param);
        return success(data);
    }

    /**
     * 只列出文件夹
     *
     * @param param param
     * @return dir list
     */
    @Override
    public CommonResult<List<DirectoryTreeNode>> listDirectory(PathParamVO param) {
        List<DirectoryTreeNode> nodes = fileService.listDirectory(param);
        return success(nodes);
    }

    /**
     * 新增目录
     *
     * @param folderParamVO vo
     * @return res
     */
    @Override
    public CommonResult<Boolean> createFolder(FolderParamVO folderParamVO) {
        fileService.createFolder(folderParamVO);
        return success(true);
    }

    /**
     * 指定目录上传文件
     *
     * @param username   username
     * @param parentPath parentPath
     * @param file       file
     * @return res
     */
    @Override
    public CommonResult<Boolean> uploadFile(String username, String parentPath, MultipartFile file) {
        fileService.uploadFile(username, parentPath, file);
        return success(true);
    }

    /**
     * 删除指定文件
     *
     * @param vo vo
     * @return res
     */
    @Override
    public CommonResult<Boolean> deleteFile(PathVO vo) {
        fileService.deleteFile(vo);
        return success(true);
    }


    /**
     * 获取路径
     *
     * @param pathParam param
     * @return res
     */
    @Override
    public CommonResult<List<TreeNode>> fileTreeNodes(PathParamVO pathParam) {
        List<TreeNode> data = fileService.findTreeData(pathParam, false);
        return success(data);
    }

    @PostMapping({"/fileTreeNodesWithChildren"})
    public CommonResult<List<TreeNode>> fileTreeNodesWithChildren(@Validated @RequestBody PathParamVO pathParam) {
        List<TreeNode> data = fileService.findTreeData(pathParam, true);
        return success(data);
    }
}
