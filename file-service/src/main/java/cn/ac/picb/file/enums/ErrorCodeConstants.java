package cn.ac.picb.file.enums;

import cn.ac.picb.common.framework.exception.ErrorCode;

/**
 * <AUTHOR>
 */
public interface ErrorCodeConstants {

    ErrorCode ERROR_INFO = new ErrorCode(1003002001, "{}");

    ErrorCode UPLOAD_PATH_ERROR = new ErrorCode(1004001001, "There was an error in the upload path");
    ErrorCode UPLOAD_PATH_ERROR1 = new ErrorCode(1004001001, "There was an error in the upload path1");

}
