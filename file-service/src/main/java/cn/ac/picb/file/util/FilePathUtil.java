package cn.ac.picb.file.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class FilePathUtil {

    /**
     * 获取当前路径 以/开头
     *
     * @param path path
     * @return 当前路径
     */
    public String getCurrentPath(String path) {
        path = StrUtil.blankToDefault(path, "/");
        path = path.startsWith("/") ? path : "/" + path;
        log.debug("currentPath: {}", path);
        return path;
    }

    /**
     * 获取用户跟目录
     *
     * @param username 当前登录用户
     * @return 根目录
     */
    public String getRootPath(String username, String home) {
        String rootPath = home + "/" + username;
        if (!FileUtil.exist(rootPath)) {
            FileUtil.mkdir(rootPath);
        }
        log.debug("rootPath: {}", rootPath);
        return rootPath;
    }
}
