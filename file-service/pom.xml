<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.5.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>cn.ac.picb</groupId>
    <artifactId>file-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>file-service</name>
    <description>文件服务</description>
    <packaging>war</packaging>

    <properties>
        <java.version>1.8</java.version>
        <lombok.version>1.18.12</lombok.version>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <easyexcel.version>2.2.6</easyexcel.version>

        <docker.host>unix:///var/run/docker.sock</docker.host>
        <docker.registry>docker.io</docker.registry>
        <docker.namespace>docker4maling</docker.namespace>
        <docker.plugin.version>0.33.0</docker.plugin.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>common-spring-boot-starter-web</artifactId>
            <version>2021.03.24-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>file-service-api</artifactId>
            <version>2021.07.01-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>


    <build>
        <finalName>file-service</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
