pipeline {

  agent any



    environment {
        DOCKER_CREDENTIAL_ID = 'dev-biosino-dockerhub-builder'
        KUBECONFIG_CREDENTIAL_ID = 'kubecfg-ipac-admin'

        REGISTRY = 'dev.biosino.org'
        DOCKERHUB_NAMESPACE = 'biosino'
        APP_NAME = 'ipac'
        PROJ_ROOTFS = '/zjbdp/nservice/ipac'

        PROJ_WBEAPP = '/zjbdp/nservice/ipac/webapp'
    }


    stages {

        stage ('checkout scm') {
            steps {
                checkout(scm)
            }

        }

        stage('build'){
            steps {
                echo "starting deploy....."
                sh "mvn  clean package -Dmaven.test.skip=true -P prod"
            }

        }

        stage('deploy to www') {
            steps {

                // update war
                sh "cp ipac-app/target/ipac.war ${PROJ_WBEAPP}/ipac.war.${BUILD_ID}"
                sh 'cp ${PROJ_WBEAPP}/ipac.war.${BUILD_ID} ${PROJ_WBEAPP}/ipac.war'
                withKubeConfig( [ credentialsId: "$KUBECONFIG_CREDENTIAL_ID" ] ) {
                   sh "kubectl -n smdb delete po --selector=tier=tomcat --insecure-skip-tls-verify"
                }
            }
        }
    }
}