package cn;

import cn.hutool.core.io.FileUtil;

import java.io.File;
import java.util.Arrays;
import java.util.List;

public class A {

    public static void main(String[] args) {
        List<File> list = Arrays.asList(
                new File("D:\\gitProjectNew\\iPACN\\ipac-app\\src\\main\\java\\cn\\ac\\picb\\ipac"),
                new File("D:\\gitProjectNew\\iPACN\\file-service\\src\\main\\java\\cn\\ac\\picb\\file"),
                new File("D:\\gitProjectNew\\iPACN\\ipac-task\\src\\main\\java\\cn\\ac\\picb\\task"),
                new File("D:\\gitProjectNew\\iPACN\\ipac-tools\\ipac-tools-api\\src\\main\\java\\cn\\ac\\picb\\api")
                );
        List<File> list1 = Arrays.asList(
                new File("D:\\gitProjectNew\\iPACN\\ipac-app\\src"),
                new File("D:\\gitProjectNew\\iPACN\\file-service\\src"),
                new File("D:\\gitProjectNew\\iPACN\\ipac-task\\src"),
                new File("D:\\gitProjectNew\\iPACN\\ipac-tools\\ipac-tools-api\\src")
        );

        int count = 0;
        for (File f : list1) {
            for (File loopFile : FileUtil.loopFiles(f)) {
                count += FileUtil.getTotalLines(loopFile);
            }
        }

        System.out.println(count);
    }
}
