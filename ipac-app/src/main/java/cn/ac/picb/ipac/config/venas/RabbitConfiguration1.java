package cn.ac.picb.ipac.config.venas;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfiguration1 {

    private final MqProperties1 mqProperties;

    public RabbitConfiguration1(MqProperties1 mqProperties) {
        this.mqProperties = mqProperties;
    }

    @Bean
    public DirectExchange venas1DirectExchange() {
        return ExchangeBuilder
                .directExchange(mqProperties.getExchange().getName())
                .ignoreDeclarationExceptions()
                .build();
    }

    /**
     * web创建分析队列
     *
     * @return queue
     */
    @Bean
    public Queue venas1TaskQueue() {
        return new Queue(mqProperties.getTask().getQueueName());
    }


    @Bean
    public Binding venas1BindingTaskQueue(DirectExchange venas1DirectExchange, Queue venas1TaskQueue) {
        return BindingBuilder
                .bind(venas1TaskQueue)
                .to(venas1DirectExchange)
                .with(mqProperties.getTask().getRoutingKey());
    }
}
