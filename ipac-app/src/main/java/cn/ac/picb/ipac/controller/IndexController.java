package cn.ac.picb.ipac.controller;


import cn.ac.picb.ipac.common.core.Result;
import cn.ac.picb.ipac.common.core.ResultGenerator;
import cn.ac.picb.ipac.service.UserService;
import cn.ac.picb.ipac.vo.UserVo;
import cn.ac.picb.ipac.vo.UserVo2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
public class IndexController {
    @Autowired
    private UserService userService;


    @RequestMapping(path = {"/home", "/"})
    public String home() {
        return "home";
    }

    @RequestMapping("/reg")
    public String reg() {
        return "reg";
    }


    @RequestMapping("/regApply")
    @ResponseBody
    public Result regApply(@Validated UserVo2 userVo) {
        userService.regApply(userVo);
        return ResultGenerator.genSuccessResult();
    }



    @RequestMapping("/register")
    public String register() {
        return "register";
    }

    @RequestMapping("/registered")
    @ResponseBody
    public Result registered(@Validated UserVo userVo) {
        userService.saveUser(userVo);
        return ResultGenerator.genSuccessResult();
    }

    @RequestMapping("/forgetPwd")
    public String forgetPwd() {
        return "recoverpw";
    }

    /**
     * 发送重置密码邮件
     * @param useremail
     * @return
     */
    @RequestMapping("/forgetPwd2")
    @ResponseBody
    public Result forgetPwd2(String useremail) {
        userService.forgetPwd2(useremail);
        return ResultGenerator.genSuccessResult();
    }

    @RequestMapping("/resetPwd")
    @ResponseBody
    public Result resetPwd(String token) {
        userService.resetPwd(token);
        return ResultGenerator.genSuccessResult("重置成功，密码为：" + UserService.DEFAULT_PWD);
    }

}
