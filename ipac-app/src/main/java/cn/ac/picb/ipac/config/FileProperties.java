package cn.ac.picb.ipac.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "file")
@Data
public class FileProperties {

    private String inputHome = "/data/analysis/input";

    private String outputHome = "/data/analysis/output";

    private String ftpHome = "/data/ftp";

    private String pitsDirName = "pathogens-identification";

    private String pitsServerInputDataHome = "/public/home/<USER>/imacanalysis/imac/iPAC/input/pathogens-identification";

    private String pitsServerOutputDataHome = "/public/home/<USER>/imacanalysis/imac/iPAC/output/pathogens-identification";

    private String gvapDirName = "genome-variation";

    private String gvapServerInputDataHome = "/public/home/<USER>/imacanalysis/imac/iPAC/input/genome-variation";

    private String gvapServerOutputDataHome = "/public/home/<USER>/imacanalysis/imac/iPAC/output/genome-variation";


}
