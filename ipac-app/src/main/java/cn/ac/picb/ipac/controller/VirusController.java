package cn.ac.picb.ipac.controller;

import cn.ac.picb.ipac.common.core.PageBaseUrl;
import cn.ac.picb.ipac.common.core.Result;
import cn.ac.picb.ipac.common.core.ResultGenerator;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.model.VicVirusVisualization;
import cn.ac.picb.ipac.vo.VicVirusSearch;
import cn.ac.picb.ipac.vo.VicVirusVo;
import lombok.RequiredArgsConstructor;
import cn.ac.picb.ipac.repository.VicVirusVisualizationRepository;
import cn.ac.picb.ipac.service.VirusService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequiredArgsConstructor
public class VirusController extends BaseController {

    private final VirusService virusService;
    private final VicVirusVisualizationRepository vicVirusVisualizationRepository;

    /**
     * 检出病毒列表
     *
     * @param abbr     病毒
     * @param search   search
     * @param pageable page
     * @param model    model
     * @return page
     */
    @RequestMapping("/virus/list")
    @PageBaseUrl(path = "/virus/list")
    public String virusList(@ModelAttribute("abbr") String abbr, @ModelAttribute("search") VicVirusSearch search, @PageableDefault Pageable pageable, Model model) {
        User user = getUser();
        search.setRefAbbr(abbr);
        search.setUserId(user.getId());
        Page<VicVirusVo> page = virusService.findVicVirusPage(search, pageable, user);
        model.addAttribute("page", page);
        return "virus_list";
    }

    /**
     * 删除病毒记录
     *
     * @param id virus id
     * @return page
     */
    @RequestMapping("/virus/delete")
    @ResponseBody
    public Result deleteVirus(String id) {
        virusService.deleteVicVirus(id);
        return ResultGenerator.genSuccessResult();
    }

    /**
     * 重新扫描
     *
     * @param abbr 病毒
     * @return page
     */
    @RequestMapping("/virus/rescan")
    @ResponseBody
    public Result rescan(String abbr) {
        String userId = getUser().getId();
        virusService.scanUserTask(abbr, userId);
        return ResultGenerator.genSuccessResult();
    }


    /**
     * 生成jBrowse 配置
     *
     * @param abbr     病毒
     * @param virusIds ids
     * @return page
     */
    @RequestMapping("/virus/config")
    @ResponseBody
    public Result createConfig(String abbr, @RequestParam("virusIds[]") String[] virusIds) {
        VicVirusVisualization vicVirusVisualization = virusService.createVicVirusVisualization(getUser(), abbr, virusIds);
        return ResultGenerator.genSuccessResult(vicVirusVisualization.getId());
    }

    /**
     * 检出病毒可视化列表
     *
     * @param search   search
     * @param pageable page
     * @param model    model
     * @return page
     */
    @RequestMapping("/virusVisualization/list")
    @PageBaseUrl(path = "/virusVisualization/list")
    public String virusVisualizationList(@ModelAttribute("search") VicVirusSearch search, @PageableDefault Pageable pageable, Model model) {
        search.setUserId(getUser().getId());
        Page<VicVirusVisualization> page = virusService.findVicVirusVisualizationPage(search, pageable);
        model.addAttribute("page", page);
        return "virus_visualization_list";
    }

    /**
     * 删除可视化记录
     *
     * @param id VirusVisualization id
     * @return page
     */
    @RequestMapping("/virusVisualization/delete")
    @ResponseBody
    public Result deleteVirusVisualization(String id) {
        virusService.deleteVicVirusVisualization(id);
        return ResultGenerator.genSuccessResult();
    }

    /**
     * 检出病毒可视化
     *
     * @param id    id
     * @param model model
     * @return page
     */
    @RequestMapping("/virusVisualization/detail")
    public String virusVisualizationDetail(String id, Model model) {
        VicVirusVisualization visualization = vicVirusVisualizationRepository.findById(id).orElseThrow(() -> new RuntimeException("The record does not exist"));
        model.addAttribute("visualization", visualization);
        return "virus_visualization_detail";
    }
}
