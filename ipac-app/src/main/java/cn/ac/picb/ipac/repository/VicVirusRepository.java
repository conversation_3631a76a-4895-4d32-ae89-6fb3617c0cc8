package cn.ac.picb.ipac.repository;

import cn.ac.picb.ipac.model.VicVirus;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface VicVirusRepository extends MongoRepository<VicVirus, String>, VicVirusRepositoryCustom {

    /**
     * is_default_ref=true
     *
     * @return 基础数据
     */
    List<VicVirus> findAllByDefaultRefIsTrue();

    /**
     * 删除用户 当前病毒的所有记录
     *
     * @param userId  用户id
     * @param refAbbr 病毒
     */
    void deleteByUserIdAndRefAbbrAndDefaultRefIsFalse(String userId, String refAbbr);

    List<VicVirus> findByIdIn(String[] ids);

    /**
     * optional
     *
     * @param refAbbr refAbbr
     * @return optional
     */
    Optional<VicVirus> findByRefAbbrAndDefaultRefIsTrue(String refAbbr);

}
