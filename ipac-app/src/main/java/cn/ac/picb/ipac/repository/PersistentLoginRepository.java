package cn.ac.picb.ipac.repository;

import cn.ac.picb.ipac.model.PersistentLogin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface PersistentLoginRepository extends JpaRepository<PersistentLogin, String>, JpaSpecificationExecutor<PersistentLogin> {
    PersistentLogin findBySeries(String series);

    void deleteByUsername(String username);
}
