package cn.ac.picb.ipac.repository.impl;

import cn.ac.picb.ipac.model.VicVirus;
import cn.ac.picb.ipac.vo.VicVirusSearch;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import cn.ac.picb.ipac.repository.VicVirusRepositoryCustom;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class VicVirusRepositoryImpl implements VicVirusRepositoryCustom {

    private final MongoTemplate mongoTemplate;

    /**
     * 病毒分析列表
     *
     * @param search   search
     * @param pageable pageable
     * @return page
     */
    @Override
    public Page<VicVirus> findByPage(VicVirusSearch search, Pageable pageable) {
        Query query = new Query();
        Criteria criteria = Criteria.where("is_default_ref").is(false);

        if (StringUtils.isNotBlank(search.getUserId())) {
            criteria.and("user_id").is(search.getUserId());
        }
        if (StringUtils.isNotBlank(search.getRefAbbr())) {
            criteria.and("ref_abbr").is(search.getRefAbbr());
        }
        if (search.getStart() != null && search.getEnd() != null) {
            criteria.and("create_time").gte(search.getStart()).lte(search.getEnd());
        } else if (search.getStart() != null) {
            criteria.and("create_time").gte(search.getStart());
        } else if (search.getEnd() != null) {
            criteria.and("create_time").lte(search.getStart());
        }

        query.addCriteria(criteria);
        query.with(Sort.by(Sort.Order.desc("create_time")));

        long count = mongoTemplate.count(query, VicVirus.class);
        List<VicVirus> content = mongoTemplate.find(query.with(pageable), VicVirus.class);
        return new PageImpl<>(content, pageable, count);
    }
}
