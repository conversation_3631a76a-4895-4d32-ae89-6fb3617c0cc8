package cn.ac.picb.ipac.mq.venas;

import cn.ac.picb.ipac.common.core.ServiceException;
import cn.ac.picb.ipac.common.enums.venas.VenasTaskStatusEnum;
import cn.ac.picb.ipac.config.venas.VenasProperties;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.repository.UserTaskRepository;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

@Component
@Slf4j
@AllArgsConstructor
public class MessageReceiver2 {
    private final UserTaskRepository userTaskRepository;
    private VenasProperties venasProperties;

    @RabbitListener(queues = "${venas2.rabbitmq.task.queueName}")
    @RabbitHandler
    @SneakyThrows
    public void receiveResult2Message(@Payload Venas1Task msg, Channel channel, @Headers Map<String, Object> headers) {
        log.info("venas2消费端Payload: " + msg);
        UserTask userTask = userTaskRepository.findByTaskId(msg.getTaskId()).get();
        if (!ObjectUtil.equal(userTask.getStatus(), VenasTaskStatusEnum.draft.getCode())) {
            return;
        }
        upStatus(userTask, VenasTaskStatusEnum.analyzing, null);
        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        try {
            UserTask parentTask = userTaskRepository.findById(userTask.getPid()).get();
            analysis(userTask, parentTask);
            upStatus(userTask, VenasTaskStatusEnum.complete, null);
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            e.printStackTrace();
            upStatus(userTask, VenasTaskStatusEnum.error, e.getMessage());
            channel.basicAck(deliveryTag, false);
        }
    }

    private void analysis(UserTask userTask, UserTask parentTask) throws Exception {
        String workingDirectory = venasProperties.getScriptParentPath();
        String pythonScript = "scripts/venas2_addnew.py";
        if ("M.tuberculosis".equals(userTask.getAttr1())) {
            pythonScript = "scripts/venas2_addnew_mtb.py";
        }
        String[] command = {
                "python3", pythonScript,
                "-venas2_output_path", parentTask.getOutPath(),
                "-input_name", userTask.getTaskId()
        };
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File(workingDirectory));
        log.info("venas2 script: {}", JSONObject.toJSONString(command));
        try {
            Process process = processBuilder.start();
            // 读取标准输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("venas2 taskId:{}, python log: :", userTask.getTaskId(), line);
            }
            int exitCode = process.waitFor();
            log.info("Exited with result code : " + exitCode);
            if (exitCode != 0) {
                throw new ServiceException("分析失败！");
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
            throw e;
        }
    }

    private void upStatus(UserTask userTask, VenasTaskStatusEnum statusEnum, String errMsg) {
        userTask.setStatus(statusEnum.getCode());
        userTask.setErrMsg(errMsg);
        userTaskRepository.save(userTask);
    }
}
