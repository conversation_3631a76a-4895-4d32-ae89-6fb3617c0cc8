package cn.ac.picb.ipac.common.util;

import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipUtil {

    public static void zipFiles(String source,String destit) {
        File file = new File( source );
        ZipOutputStream zipOutputStream = null;
        FileOutputStream fileOutputStream = null;
        try {
            fileOutputStream = new FileOutputStream( destit );
            zipOutputStream = new ZipOutputStream( fileOutputStream );
            if (file.isDirectory()) {
                directory( zipOutputStream, file, "" );
            } else {
                zipFile( zipOutputStream, file, "" );
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                zipOutputStream.close();
                fileOutputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }
    private static void zipFile(ZipOutputStream zipOutputStream, File file, String parentFileName){
        FileInputStream in = null;
        try {
            ZipEntry zipEntry;
            //如果是一个文件夹，则拼接
            if(file.getName().equals(parentFileName)){
                zipEntry = new ZipEntry(file.getName() );
            }else{
                String path = parentFileName.replace(file.getName(), "");
                zipEntry = new ZipEntry(path+file.getName());
            }
            zipOutputStream.putNextEntry( zipEntry );
            in = new FileInputStream( file);
            int len;
            byte [] buf = new byte[8*1024];
            while ((len = in.read(buf)) != -1){
                zipOutputStream.write(buf, 0, len);
            }
            zipOutputStream.closeEntry(  );
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try{
                in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    private static void directory(ZipOutputStream zipOutputStream,File file,String parentFileName){
        File[] files = file.listFiles();
        String parentFileNameTemp = null;
        for (File fileTemp:
                files) {
            parentFileNameTemp =  (parentFileName == null || parentFileName == "") ?fileTemp.getName():parentFileName+"\\"+fileTemp.getName();
            if(fileTemp.isDirectory()){
                directory(zipOutputStream,fileTemp, parentFileNameTemp);
            }else{
                zipFile(zipOutputStream,fileTemp,parentFileNameTemp);
            }
        }
    }

}
