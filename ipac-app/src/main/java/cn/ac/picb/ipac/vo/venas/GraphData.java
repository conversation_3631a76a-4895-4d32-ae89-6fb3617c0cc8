package cn.ac.picb.ipac.vo.venas;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class GraphData {
    /**
     * 主节点信息
     */
    private List<GraphNode> nodes;
    /**
     * 连接线信息
     */
    private List<GraphLink> links;

    @Data
    public static class GraphNode {

        private String id;

        private String hapid;

        private String nodeId;

        private String clusterId;

        private String clusterSize;

        private String mutations;

        private String name;

        private Integer value;

        private String label;

        private Float x;

        private Float y;

        private boolean fixed;

        //cluster所有的信息
        private Map<String, Long> countries;
        //keyNode的信息，countries的子集
        private Map<String, Long> countries2;

        //cluster所有的信息
        private Map<String, Long> virus;
        //keyNode的信息，virus的子集
        private Map<String, Long> virus2;

        private String highOrDark;

        public interface HighOrDark {
            String dark = "dark";
            String highlight = "highlight";
        }

        //ClusterId
        private List<String> lineage;
        private List<String> sampleIds;
        private List<String> haploidIds;
    }


    @Data
    public static class GraphLink {

        private String source;

        private String target;

        private Integer value;

        private List<String> diffs;

        /**
         * 高亮
         * "lineStyle": {
         * "color": "source",
         * "width": 3,
         * "curveness": 0
         * },
         */
        private JSONObject lineStyle;

        public static JSONObject lineStyleHeigh = new JSONObject();

        static {
            lineStyleHeigh.fluentPut("color", "black").fluentPut("width", 4).fluentPut("curveness", 0);
        }
    }
}
