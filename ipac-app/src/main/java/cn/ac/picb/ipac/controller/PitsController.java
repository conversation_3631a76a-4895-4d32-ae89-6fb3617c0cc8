package cn.ac.picb.ipac.controller;

import cn.ac.picb.ipac.common.core.Result;
import cn.ac.picb.ipac.common.core.ResultGenerator;
import cn.ac.picb.ipac.common.enums.PitsTaskStatusEnum;
import cn.ac.picb.ipac.dto.PitsTaskDTO;
import cn.ac.picb.ipac.dto.VenasTaskSearch;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.service.PitsService;
import cn.ac.picb.ipac.vo.UserTaskVo;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> Li
 * @date 2025/8/20
 */
@Controller
@RequiredArgsConstructor
@RequestMapping("/pits")
public class PitsController extends BaseController {

    private final PitsService pitsService;

    @RequestMapping("/main")
    public String main(Model model) {
        return "pits/main";
    }

    @RequestMapping("/addTask")
    @ResponseBody
    public Result addTask(@Validated PitsTaskDTO dto) {
        UserTask task = pitsService.addTask(getUser(), dto);
        return ResultGenerator.genSuccessResult();
    }

    /**
     * PITS任务列表
     *
     * @param model
     * @param pageable
     * @return
     */
    @RequestMapping("/tasks")
    public String gvapTasks(Model model, @PageableDefault Pageable pageable, @ModelAttribute("search") VenasTaskSearch search) {
        Page<UserTaskVo> page = pitsService.findPitsTaskPage(getUser(), pageable, search);
        Map<Integer, String> code$DescMap = PitsTaskStatusEnum.statusMap("en");
        model.addAttribute("codeDescMap", code$DescMap);
        model.addAttribute("page", page);

        return "user/pits/task-list";
    }

    /**
     * PITS任务列表-下载结果
     *
     * @param id
     * @return
     */
    @RequestMapping("/downLoadResult")
    public void gvapDownLoadResult(String id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        pitsService.downloadResult(id, request, response);
    }

}
