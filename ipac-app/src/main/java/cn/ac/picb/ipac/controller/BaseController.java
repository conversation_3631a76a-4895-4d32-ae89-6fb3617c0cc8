package cn.ac.picb.ipac.controller;

import cn.ac.picb.ipac.common.core.ServiceException;
import cn.ac.picb.ipac.config.CasSecurityConfiguration;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.model.VicVirus;
import cn.ac.picb.ipac.repository.UserRepository;
import cn.ac.picb.ipac.repository.VicVirusRepository;
import cn.ac.picb.ipac.security.CustomeUserDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.ModelAttribute;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class BaseController {

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private VicVirusRepository vicVirusRepository;

    protected User getUser() {
        if ("anonymousUser".equals(SecurityContextHolder.getContext().getAuthentication().getPrincipal())) {
            return null;
        }
        SecurityContext context = SecurityContextHolder.getContext();
        CasSecurityConfiguration.SecurityUser principal;
        if (context.getAuthentication().getPrincipal() instanceof CasSecurityConfiguration.SecurityUser) {
            principal = (CasSecurityConfiguration.SecurityUser) context.getAuthentication().getPrincipal();
        } else {
            principal = (CasSecurityConfiguration.SecurityUser) context.getAuthentication().getDetails();
        }
        return Optional.of(userRepository.findFirstByAccountName(principal.getAccountName())).orElseThrow(() -> new ServiceException("The user does not exist！"));
    }

    @ModelAttribute("virusRefAbbr")
    protected List<String> virusRefAbbr() {
        return vicVirusRepository.findAllByDefaultRefIsTrue().stream().map(VicVirus::getRefAbbr).collect(Collectors.toList());
    }
}



