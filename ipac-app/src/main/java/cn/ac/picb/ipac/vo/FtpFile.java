package cn.ac.picb.ipac.vo;

import lombok.Data;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class FtpFile {

    private String id;
    private String uploader;
    private String type;
    private String parentPath;
    private String fileName;
    private Long fileSize;
    private String fileSuffix;
    private String fileOriginName;
    private Date lastModifiedTime;
    private Boolean hidden;

    public String getBaseName() {
        if (StringUtils.isBlank(fileName)) {
            return fileName;
        }
        return FilenameUtils.getBaseName(fileName);
    }

    public static void main(String[] args) {
        System.out.println(FilenameUtils.getBaseName("aaa.vcf.gz"));
    }
}
