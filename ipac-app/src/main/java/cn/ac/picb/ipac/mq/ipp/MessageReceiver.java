package cn.ac.picb.ipac.mq.ipp;

import cn.ac.picb.ipac.mq.AnalysisError;
import cn.ac.picb.ipac.mq.AnalysisResult;
import cn.ac.picb.ipac.mq.AnalysisStatus;
import com.rabbitmq.client.Channel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import cn.ac.picb.ipac.service.TaskService;
import cn.ac.picb.ipac.service.VirusService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MessageReceiver {

    private final TaskService taskService;
    private final VirusService virusService;

    public MessageReceiver(TaskService taskService, VirusService virusService) {
        this.taskService = taskService;
        this.virusService = virusService;
    }

    @RabbitListener(queues = "${ncov.rabbitmq.web.queueName}")
    @RabbitHandler
    @SneakyThrows
    public void receiveResultMessage(@Payload AnalysisResult msg, Channel channel, @Headers Map<String, Object> headers) {
        log.debug("接收到分析服务器的分析结果---------------------");
        log.debug("消费端Payload: " + msg);
        // 更新任务状态，并给用户发送提醒
        taskService.updateTaskComplete(msg.getTaskId(), msg.getPath());
        // 扫描分析结果
        virusService.scanTask(msg.getTaskId());

        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        channel.basicAck(deliveryTag, false);
    }

    @RabbitListener(queues = "${ncov.rabbitmq.error.queueName}")
    @RabbitHandler
    @SneakyThrows
    public void receiveErrorMessage(@Payload AnalysisError msg, Channel channel, @Headers Map<String, Object> headers) {
        log.debug("receiveErrorMessage---------------------");
        log.debug("消费端Payload: " + msg);

        // 更新任务状态
        Integer errorCode = msg.getErrorCode();
        switch (errorCode) {
            case 10000:
                log.error("移动ftp文件至分析端时出错, 消息体为：{}", msg);
                break;
            case 20000:
                log.error("分析服务端 分析出错, 消息体为：{}", msg);
                break;
            case 30000:
                log.error("移动分析文件至 web端出错, 消息体为：{}", msg);
                break;
            default:
                log.error("消息体code出错, 消息体为：{}", msg);
                break;
        }
        taskService.updateTaskErrorStatus(msg.getTaskId(), msg.getMessage());

        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        channel.basicAck(deliveryTag, false);
    }

    @RabbitListener(queues = "${ncov.rabbitmq.status.queueName}")
    @RabbitHandler
    @SneakyThrows
    public void receiveStatusMessage(@Payload AnalysisStatus msg, Channel channel, @Headers Map<String, Object> headers) {
        log.debug("receiveStatusMessage---------------------");
        log.debug("消费端Payload: " + msg);

        // 更新任务状态
        taskService.updateTaskStatus(msg.getTaskId(), msg.getStatusCode());

        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        channel.basicAck(deliveryTag, false);
    }
}
