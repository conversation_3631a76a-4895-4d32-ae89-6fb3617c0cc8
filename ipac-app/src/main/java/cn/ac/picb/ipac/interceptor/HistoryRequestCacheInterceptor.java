package cn.ac.picb.ipac.interceptor;

import cn.ac.picb.ipac.common.util.WebUtils;
import com.alibaba.fastjson.JSONArray;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Component
public class HistoryRequestCacheInterceptor implements HandlerInterceptor {

    public static final String HISTORY_REQUEST_CACHE = "history_request_cache";

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) {
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse httpServletResponse, Object handler, Exception e) {
        if (!(handler instanceof HandlerMethod)) {
            return;
        }
        if (e != null) {
            return;
        }
        HandlerMethod method = (HandlerMethod) handler;
        if (WebUtils.isAjax(method)) {
            return;
        }
        ResponseBody responseBody = method.getMethodAnnotation(ResponseBody.class);
        if (responseBody != null) {
            return;
        }
        if (!String.class.isAssignableFrom(method.getReturnType().getParameterType())) {
            return;
        }
        if (request.getRequestURI().equalsIgnoreCase("go_history")) {
            return;
        }

        HttpSession session;
        try {
            session = request.getSession();
        } catch (Exception ex) {
            return;
        }
        Map<String, String> data;
        if (session.getAttribute(HISTORY_REQUEST_CACHE) == null) {
            data = new LinkedHashMap<>();
            session.setAttribute(HISTORY_REQUEST_CACHE, data);
        } else {
            data = (Map<String, String>) session.getAttribute(HISTORY_REQUEST_CACHE);
        }
        if (data.containsKey(request.getRequestURI())) {
            data.remove(request.getRequestURI());
        }
        if (data.size() > 10) { //为了节省内存，也为了速度，这里只缓存最近10个连接信息
            List<String> keys = new LinkedList<>(data.keySet()).subList(0, data.size() - 10);
            keys.forEach((key) -> data.remove(key));
        }
        Map<String, String[]> param = request.getParameterMap();
        data.put(request.getRequestURI(), JSONArray.toJSONString(param));
    }
}

