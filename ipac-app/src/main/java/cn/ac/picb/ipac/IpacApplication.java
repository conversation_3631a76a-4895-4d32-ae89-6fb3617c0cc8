package cn.ac.picb.ipac;

import cn.ac.picb.api.annotation.EnableApi;
import cn.ac.picb.ipac.config.FileProperties;
import cn.ac.picb.ipac.config.ipp.MqProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.openfeign.EnableFeignClients;


@SpringBootApplication
@EnableFeignClients
@EnableApi
@EnableConfigurationProperties(value = {MqProperties.class, FileProperties.class})
public class IpacApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(IpacApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(IpacApplication.class, args);
    }
}
