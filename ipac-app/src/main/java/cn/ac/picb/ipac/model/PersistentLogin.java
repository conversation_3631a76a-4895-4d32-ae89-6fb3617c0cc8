package cn.ac.picb.ipac.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "persistent_login")
public class PersistentLogin {
    private String id;
    private String username;
    private String token;
    private String series;
    private Date lastUsed;

    @Id
    @Column(name = "id", unique = true, nullable = false, length = 36)
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Column(name = "username")
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Column(name = "token")
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Column(name = "series")
    public String getSeries() {
        return series;
    }

    public void setSeries(String series) {
        this.series = series;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_used")
    public Date getLastUsed() {
        return lastUsed;
    }

    public void setLastUsed(Date lastUsed) {
        this.lastUsed = lastUsed;
    }
}
