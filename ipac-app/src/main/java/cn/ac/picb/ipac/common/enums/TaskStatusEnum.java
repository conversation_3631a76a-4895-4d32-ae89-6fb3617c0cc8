package cn.ac.picb.ipac.common.enums;

import lombok.Getter;

import java.util.*;

/**
 * 任务状态
 *
 * <AUTHOR>
 */
public enum TaskStatusEnum {

    // 任务出错
    error(-1, "Task error"),

    // 任务已删除
    delete(0, "Deleted"),

    // 任务准备中
    draft(1, "Task preparing"),

    // 分析数据准备完成
    ready(2, "Data prepared"),

    // 测序数据质控QC
    qc(3, "Quality control"),

    // 宿主/环境序列过滤
    filter(4, "Trimming"),

    // 病毒序列检测
    fev(5, "Detection"),

    // 病毒基因组拼接
    assembler(6, "Assembly"),

    // 分析完成
    complete(7, "Analysis done");


    @Getter
    private Integer code;

    @Getter
    private String desc;

    TaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> getCode$DescMap() {
        Map<Integer, String> map = new HashMap<>();
        for (TaskStatusEnum value : TaskStatusEnum.values()) {
            map.put(value.getCode(), value.getDesc());
        }
        return map;
    }

    public static TaskStatusEnum getEnum(Integer code) {
        for (TaskStatusEnum value : TaskStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 获取有效的状态描述
     * @return
     */
    public static Set<String> getValidDescSet() {
        Set<String> set = new LinkedHashSet<>();
        for (TaskStatusEnum value : TaskStatusEnum.values()) {
            if(delete.equals(value)){
                continue;
            }
            set.add(value.getDesc());
        }
        return set;
    }

    public static List<Integer> getCodesByDesc(String desc) {
        List<Integer> list = new ArrayList<>();
        for (TaskStatusEnum value : TaskStatusEnum.values()) {
            if(value.getDesc().equals(desc)){
                list.add(value.getCode());
            }
        }
        return list;
    }

}
