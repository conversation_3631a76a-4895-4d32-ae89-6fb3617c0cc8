package cn.ac.picb.ipac.config;

import cn.ac.picb.common.framework.exception.ServiceException;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.repository.UserRepository;
import cn.hutool.core.util.StrUtil;
import com.kakawait.spring.boot.security.cas.autoconfigure.CasSecurityConfigurerAdapter;
import lombok.Getter;
import lombok.Setter;
import org.jasig.cas.client.validation.Assertion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.cas.userdetails.AbstractCasAssertionUserDetailsService;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;


@Configuration
public class CasSecurityConfiguration extends CasSecurityConfigurerAdapter {

    private final LogoutSuccessHandler casLogoutSuccessHandler;

    public CasSecurityConfiguration(LogoutSuccessHandler casLogoutSuccessHandler) {
        this.casLogoutSuccessHandler = casLogoutSuccessHandler;
    }

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.csrf().disable()
                .logout()
                .permitAll()
                .logoutSuccessHandler(null)
                .logoutSuccessUrl("/usercenter/files")
                .logoutRequestMatcher(new AntPathRequestMatcher("/logout"));
        LogoutFilter filter = new LogoutFilter(casLogoutSuccessHandler, new SecurityContextLogoutHandler());
        filter.setFilterProcessesUrl("/cas/logout");
        http.addFilterBefore(filter, LogoutFilter.class);
    }

    @Component
    static class CustomUserDetailsService extends AbstractCasAssertionUserDetailsService {

        @Autowired
        private UserRepository userRepository;

        @Override
        protected UserDetails loadUserDetails(Assertion assertion) {
            String username = assertion.getPrincipal().getName();
            if (!StringUtils.hasText(username)) {
                throw new UsernameNotFoundException("Unable to retrieve username from CAS assertion");
            }

            Map<String, Object> principalAttributes = assertion.getPrincipal().getAttributes();
            String userId = String.valueOf(principalAttributes.getOrDefault("id", ""));
            String name = String.valueOf(principalAttributes.getOrDefault("name", ""));
            String status = String.valueOf(principalAttributes.getOrDefault("status", "enable"));
            boolean enabled = StrUtil.equals("enable", status);
            if (!userRepository.existsById(userId)) {
                User user = new User();
                user.setId(userId);
                user.setName(name);
                user.setAccountName(username);
                userRepository.save(user);
            }
            List<GrantedAuthority> authorities = AuthorityUtils.createAuthorityList("ROLE_USER");
            return new SecurityUser(userId, name, username, enabled, authorities);
        }
    }

    @Getter
    public static class SecurityUser extends org.springframework.security.core.userdetails.User {

        private static final String NON_EXISTENT_PASSWORD_VALUE = "NO_PASSWORD";

        private final String id;

        @Setter
        private String name;

        @Setter
        private String accountName;

        public SecurityUser(String id, String name, String username, boolean enabled, Collection<? extends GrantedAuthority> authorities) {
            super(username, NON_EXISTENT_PASSWORD_VALUE, enabled, true, true, true, authorities);
            this.id = id;
            this.name = name;
            this.accountName = username;
        }

        public SecurityUser(String id, String name, String username, String password, boolean enabled, Collection<? extends GrantedAuthority> authorities) {
            super(username, password, enabled, true, true, true, authorities);
            this.id = id;
            this.name = name;
            this.accountName = username;
        }
    }
}
