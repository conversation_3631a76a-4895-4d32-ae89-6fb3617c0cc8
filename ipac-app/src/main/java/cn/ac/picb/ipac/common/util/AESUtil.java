package cn.ac.picb.ipac.common.util;

import cn.ac.picb.ipac.common.core.ServiceException;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.Base64;

public class AESUtil {

    private static Logger logger = LoggerFactory.getLogger(AESUtil.class);

    /**
     * 密码
     */
    public static final String DEFAULT_KEY = "DFYYGXBXUKEYERTY";

    /**
     * 加密
     * 
     * sKey 必须是16个长度字节
     *
     * @return
     */
    public static String encrypt(String data, Charset charset, String sKey) throws ServiceException {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(sKey)) {
            throw new IllegalArgumentException("参数错误");
        }
        if (charset == null) {
            charset = Charset.forName("UTF-8");
        }
        byte[] src = data.getBytes(charset);
        byte[] buf = encrypt(src, sKey);
        return Hex.encodeHexString(buf);
    }

    public static String encrypt(String data, Charset charset) throws ServiceException {
        return encrypt(data,charset,DEFAULT_KEY);
    }

    /**
     * 解密
     * sKey 必须是16个长度字节
     *
     * @return
     * @throws Exception
     */
    public static String decrypt(String data, Charset charset, String sKey) throws ServiceException {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(sKey)) {
            throw new IllegalArgumentException("参数错误");
        }
        if (charset == null) {
            charset = Charset.forName("UTF-8");
        }
        byte[] bytes;
        try {
            bytes = Hex.decodeHex(data.toCharArray());
        } catch (DecoderException e) {
            throw new ServiceException(e.getMessage());
        }
        byte[] buf = decrypt(bytes, sKey);
        return new String(buf, charset);
    }


    public static String decrypt(String data, Charset charset) throws ServiceException {
        return decrypt(data, charset, DEFAULT_KEY);
    }

    /**
     * 加密
     *
     * @return
     */
    public static byte[] encrypt(byte[] data, String sKey) throws ServiceException {
        byte[] key = sKey.getBytes();
        try {
            // 初始化向量
            // 创建一个密匙工厂，然后用它把DESKeySpec转换成securekey
            SecretKeySpec desKey = new SecretKeySpec(key, "AES");

            // Cipher对象实际完成加密操作
            Cipher cipher = Cipher.getInstance("AES");
            // 用密匙初始化Cipher对象
            cipher.init(Cipher.ENCRYPT_MODE, desKey);
            // 现在，获取数据并加密
            // 正式执行加密操作
            return cipher.doFinal(data);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new ServiceException("Encryption failed");
        }
    }

    /**
     * 解密
     *
     * @param sKey
     * @return
     * @throws Exception
     */
    public static byte[] decrypt(byte[] data, String sKey) throws ServiceException {
        byte[] key = sKey.getBytes();
        // 创建一个DESKeySpec对象
        try {

            SecretKeySpec desKey = new SecretKeySpec(key, "AES");

            // Cipher对象实际完成加密操作
            Cipher cipher = Cipher.getInstance("AES");
            // 用密匙初始化Cipher对象
            cipher.init(Cipher.DECRYPT_MODE, desKey);
            // 现在，获取数据并加密
            // 正式执行加密操作
            return cipher.doFinal(data);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new ServiceException("Decryption failed");
        }
    }

    public static String encodeReturnJson(String jsonString, String appSec) throws UnsupportedEncodingException {
        byte[] encrypt = AESUtil.encrypt((jsonString).getBytes("utf-8"), appSec);
        byte[] encode = Base64.getEncoder().encode(encrypt);
        return new String(encode);
    }

    /**将16进制转换为二进制
     * @param hexStr
     * @return
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1)
            return null;
        byte[] result = new byte[hexStr.length()/2];
        for (int i = 0;i< hexStr.length()/2; i++) {
            int high = Integer.parseInt(hexStr.substring(i*2, i*2+1), 16);
            int low = Integer.parseInt(hexStr.substring(i*2+1, i*2+2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }
}
