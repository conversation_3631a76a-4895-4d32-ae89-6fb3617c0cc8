package cn.ac.picb.ipac.vo;

import lombok.Data;
import org.apache.commons.collections4.FactoryUtils;
import org.apache.commons.collections4.list.LazyList;

import java.util.ArrayList;
import java.util.List;

@Data
public class TaskFromVo {

    private String type;

    private List<UserTaskIdArr> userTaskIdArrList;

    public TaskFromVo() {
        this.userTaskIdArrList = LazyList.lazyList(new ArrayList(), FactoryUtils.instantiateFactory(UserTaskIdArr.class));
    }

    @Data
    public static class UserTaskIdArr{
        private List<String> ids;
    }

}
