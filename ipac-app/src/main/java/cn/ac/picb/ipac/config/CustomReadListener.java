package cn.ac.picb.ipac.config;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.read.metadata.holder.ReadHolder;
import com.alibaba.excel.read.metadata.property.ExcelReadHeadProperty;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @date 2023/11/3
 */
public class CustomReadListener extends AnalysisEventListener {
    @Override
    public void invokeHeadMap(Map headMap, AnalysisContext context) {
        // 获取bo对象head名称
        List<String> expectHeadList = Optional.ofNullable(context)
                .map(AnalysisContext::currentReadHolder)
                .map(ReadHolder::excelReadHeadProperty)
                .map(ExcelReadHeadProperty::getHeadMap)
                .map(Map::values)
                .orElse(Collections.emptyList())
                .stream().map(Head::getHeadNameList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        // 表头数量检测
        if (!Objects.equals(headMap.size(), expectHeadList.size())) {
            throw new ExcelAnalysisException("excel 表头数量不一致");
        }
        // 表头名称检测
        for (int i = 0; i < headMap.size(); i++) {
            if (!Objects.equals(headMap.get(i), expectHeadList.get(i))) {
                throw new ExcelAnalysisException("excel 表头内容不一致");
            }
        }
    }

    @Override
    public void invoke(Object o, AnalysisContext analysisContext) {

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
