package cn.ac.picb.ipac.mq.pits;

import cn.ac.picb.ipac.common.enums.GvapTaskStatusEnum;
import cn.ac.picb.ipac.mq.msg.AnalysisResultMsg;
import cn.ac.picb.ipac.mq.msg.StatusMsg;
import cn.ac.picb.ipac.mq.msg.TaskCreateMsg;
import cn.ac.picb.ipac.mq.msg.TaskFinishedMsg;
import cn.ac.picb.ipac.service.PitsService;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PitsMessageReceiver {

    private final PitsService pitsService;
    private final PitsMessageSender messageSender;

    @RabbitListener(queues = "speciesfinder_status_flow_queue")
    @RabbitHandler
    @SneakyThrows
    public void handlerStatusChange(@Payload StatusMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) {
        log.debug("[speciesfinder_status_flow_queue] receive msg: {}", msg);

        try {
            pitsService.handlerStatusChange(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        channel.basicAck(tag, false);
    }

    @RabbitListener(queues = "speciesfinder_task_create_queue")
    @RabbitHandler
    @SneakyThrows
    public void handlerTaskCreate(@Payload TaskCreateMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) {
        log.debug("[speciesfinder_task_create_queue] receive msg: {}", msg);
        StatusMsg statusMsg = new StatusMsg();
        statusMsg.setTaskId(msg.getTaskId());

        try {
            pitsService.handlerTaskCreate(msg);

            channel.basicAck(tag, false);

            statusMsg.setStatusCode(GvapTaskStatusEnum.ready.getCode());
        } catch (Exception e) {
            channel.basicAck(tag, false);
            log.error(e.getMessage(), e);

            statusMsg.setStatusCode(30000);
            statusMsg.setMessage(e.getMessage());
        }
        messageSender.statusChange(statusMsg);
    }

    @RabbitListener(queues = "speciesfinder_analysis_complete_queue")
    @RabbitHandler
    @SneakyThrows
    public void handlerAnalysisResult(@Payload AnalysisResultMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) {
        log.debug("[speciesfinder_analysis_complete_queue] receive msg: {}", msg);

        StatusMsg statusMsg = new StatusMsg();
        statusMsg.setTaskId(msg.getTaskId());

        try {
            pitsService.handlerAnalysisResult(msg);
            channel.basicAck(tag, false);

            statusMsg.setStatusCode(GvapTaskStatusEnum.complete.getCode());
        } catch (Exception e) {
            channel.basicAck(tag, false);
            log.error(e.getMessage(), e);

            statusMsg.setStatusCode(30000);
            statusMsg.setMessage(e.getMessage());
        }

        messageSender.statusChange(statusMsg);
    }

    @RabbitListener(queues = "speciesfinder_task_finished_queue")
    @RabbitHandler
    @SneakyThrows
    public void handlerTaskFinished(@Payload String textMsg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) {
        final TaskFinishedMsg msg = JSON.parseObject(textMsg, TaskFinishedMsg.class);
        log.debug("[speciesfinder_task_finished_queue] receive msg: {}", msg);
        channel.basicAck(tag, false);
    }
}
