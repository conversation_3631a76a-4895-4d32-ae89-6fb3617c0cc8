package cn.ac.picb.ipac.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import cn.ac.picb.ipac.interceptor.HistoryRequestCacheInterceptor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


@Controller
public class LoginController {

/*
    @RequestMapping({"/login"})
    public String login() {
        return "login";
    }*/

    @RequestMapping("/go_history")
    public String history(String[] uri, HttpServletRequest request, RedirectAttributes modelMap) throws ServletException, IOException {
        if (uri == null || uri.length == 0) {
            return "redirect:/login";
        }
        Map<String, String> cache = (Map<String, String>) request.getSession().getAttribute(HistoryRequestCacheInterceptor.HISTORY_REQUEST_CACHE);
        if (cache == null || cache.size() == 0) {
            return "redirect:/login";
        }
        List<String> keys = new ArrayList<>(cache.keySet());
        int max = -1;
        for (String key : uri) {
            if (!key.startsWith(request.getContextPath())) {
                key = request.getContextPath() + (key.startsWith("/") ? key : "/" + key);
            } else {
                key = key.startsWith("/") ? key : "/" + key;
            }
            if (cache.containsKey(key)) {
                int i = keys.indexOf(key);
                max = max < i ? i : max;
            }
        }
        if (max == -1) {
            return "redirect:/login";
        }
        Map<String, String[]> param = new LinkedHashMap<>();
        String json = cache.get(keys.get(max));
        JSONObject root = ((JSONObject) JSONObject.parse(json));
        for (String k : root.keySet()) {
            JSONArray v = root.getJSONArray(k);
            if (v != null) param.put(k, v.toArray(new String[]{}));
        }
        for (String k : param.keySet()) {
            modelMap.addAttribute(k, param.get(k));
        }
        return "redirect:" + keys.get(max).replace(request.getContextPath(), "");
    }
}
