package cn.ac.picb.ipac.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Document(collection = "vic_virus")
@Data
public class VicVirus {

    @Id
    private String id;

    //@GenerateValue(prefix = SequenceType.OEAV)
    @Field(name = "virus_id")
    private String virusId;

    @Field(name = "virus_name")
    private String virusName;

    @Field(name = "security")
    private String security;

    @Field(name = "gff_file_path")
    private String gffFilePath;

    @Field(name = "vcf_file_path")
    private String vcfFilePath;

    @Field(name = "ref_abbr")
    private String refAbbr;

    @Field(name = "is_default_ref")
    private boolean defaultRef;

    @Field(name = "ref_ac_version")
    private String refAcVersion;

    @Field(name = "user_id")
    private String userId;

    @Field("task_id")
    private String taskId;

    @Field(name = "create_time")
    private Date createTime;
}
