package cn.ac.picb.ipac.mongo;

import cn.ac.picb.ipac.model.VicVirus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.event.AbstractMongoEventListener;
import org.springframework.data.mongodb.core.mapping.event.BeforeConvertEvent;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.text.NumberFormat;

@Component
public class SaveMongoEventListener extends AbstractMongoEventListener<VicVirus> {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void onBeforeConvert(BeforeConvertEvent<VicVirus> event) {
        final VicVirus virus = event.getSource();
        ReflectionUtils.doWithFields(virus.getClass(), field -> {
            ReflectionUtils.makeAccessible(field);
            if (field.isAnnotationPresent(GenerateValue.class) && field.get(virus) == null) {
                GenerateValue annotation = field.getAnnotation(GenerateValue.class);
                //设置自增ID
                field.set(virus, getNextId(annotation.prefix()));
            }
        });
    }

    private Object getNextId(SequenceType prefix) {
        Query query = new Query(Criteria.where("index_name").is(prefix.getPojo()));
        Update update = new Update();
        update.inc("value", 1);
        FindAndModifyOptions options = new FindAndModifyOptions();
        options.upsert(true);
        options.returnNew(true);
        SequenceId sequenceId = mongoTemplate.findAndModify(query, update, options, SequenceId.class);
        long seqId = sequenceId.getValue();
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        nf.setMaximumIntegerDigits(6);
        nf.setMinimumIntegerDigits(6);
        return prefix.getPrefix() + nf.format(seqId);
    }

}
