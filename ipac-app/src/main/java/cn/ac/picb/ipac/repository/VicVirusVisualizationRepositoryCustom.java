package cn.ac.picb.ipac.repository;

import cn.ac.picb.ipac.model.VicVirusVisualization;
import cn.ac.picb.ipac.vo.VicVirusSearch;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 */
public interface VicVirusVisualizationRepositoryCustom {

    /**
     * 病毒分析可视化列表
     *
     * @param search   search
     * @param pageable pageable
     * @return page
     */
    Page<VicVirusVisualization> findByPage(VicVirusSearch search, Pageable pageable);
}
