package cn.ac.picb.ipac.common.enums.venas;

import lombok.Getter;

import java.util.*;

public enum VenasTaskStatusEnum {

    // 任务出错
    error(-1, "Error"),

    // 任务已删除
    delete(0, "Deleted"),

    // 任务准备中
    draft(1, "Waiting"),

    // 分析中
    analyzing(2, "Analyzing"),

    // 分析完成
    complete(7, "Finished");


    @Getter
    private Integer code;

    @Getter
    private String desc;

    VenasTaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> getCode$DescMap() {
        Map<Integer, String> map = new HashMap<>();
        for (VenasTaskStatusEnum value : VenasTaskStatusEnum.values()) {
            map.put(value.getCode(), value.getDesc());
        }
        return map;
    }

    public static VenasTaskStatusEnum getEnum(Integer code) {
        for (VenasTaskStatusEnum value : VenasTaskStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 获取有效的状态描述
     * @return
     */
    public static Set<String> getValidDescSet() {
        Set<String> set = new HashSet<>();
        for (VenasTaskStatusEnum value : VenasTaskStatusEnum.values()) {
            if(delete.equals(value)){
                continue;
            }
            set.add(value.getDesc());
        }
        return set;
    }

    public static List<Integer> getCodesByDesc(String desc) {
        List<Integer> list = new ArrayList<>();
        for (VenasTaskStatusEnum value : VenasTaskStatusEnum.values()) {
            if(value.getDesc().equals(desc)){
                list.add(value.getCode());
            }
        }
        return list;
    }

}
