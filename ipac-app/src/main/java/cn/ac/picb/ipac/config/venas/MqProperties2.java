package cn.ac.picb.ipac.config.venas;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "venas2.rabbitmq")
@Data
public class MqProperties2 {

    private Exchange exchange = new Exchange();

    private TaskQueueConfig task = new TaskQueueConfig();

    @Data
    public class TaskQueueConfig {

        private String routingKey = "venas2.analysis.task";

        private String queueName = "venas2-analysis-task-queue";
    }

    @Data
    public class Exchange {

        private String name = "venas2-analysis";

        private String ignoreDeclarationExceptions = "true";
    }
}
