package cn.ac.picb.ipac.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class WebConstant {
    public static final String SESSION_ACCOUNT = "session_account";

    public static String REGIST_URL;
    public static String FORGET_PASSWORD_URL;
    public static String CHANGE_PASSWORD_URL;
    public static String CAS_BASE_URL;
    public static String FTP_PATH;

    public static String getRegistUrl() {
        return WebConstant.REGIST_URL;
    }

    @Value("${biosino.regist_url}")
    public void setRegistUrl(String registUrl) {
        WebConstant.REGIST_URL = registUrl;
    }

    public static String getForgetPasswordUrl() {
        return WebConstant.FORGET_PASSWORD_URL;
    }

    @Value("${biosino.forget_pwd_url}")
    public void setForgetPasswordUrl(String forgetPasswordUrl) {
        WebConstant.FORGET_PASSWORD_URL = forgetPasswordUrl;
    }

    public static String getChangePasswordUrl() {
        return WebConstant.CHANGE_PASSWORD_URL;
    }

    @Value("${biosino.change_pwd_url}")
    public void setChangePasswordUrl(String changePasswordUrl) {
        WebConstant.CHANGE_PASSWORD_URL = changePasswordUrl;
    }

    public static String getCasBaseUrl() {
        return CAS_BASE_URL;
    }

    @Value("${biosino.cas-base-url}")
    public void setCasBaseUrl(String casBaseUrl) {
        CAS_BASE_URL = casBaseUrl;
    }

    public static String getFtpPath() {
        return FTP_PATH;
    }

    @Value("${biosino.ftp-path}")
    public void setFtpPath(String ftpPath) {
        FTP_PATH = ftpPath;
    }
}
