package cn.ac.picb.ipac.vo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

@Data
public class UserVo {
    @NotBlank
    private String accountname;
    @NotBlank
    @Email
    private String useremail;
    @NotBlank
    private String password1;
    @NotBlank
    private String password2;

    private String name;
    private String tel;
    private String organization;
}
