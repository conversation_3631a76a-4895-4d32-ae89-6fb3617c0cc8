package cn.ac.picb.ipac.config;

import cn.ac.picb.ipac.common.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.ServletRequestDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.util.HtmlUtils;

import java.beans.PropertyEditorSupport;
import java.sql.Timestamp;
import java.util.Date;

@ControllerAdvice
public class AppControllerAdvice {

    @InitBinder
    public void initBinder(ServletRequestDataBinder binder) {
        binder.registerCustomEditor(String.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) throws IllegalArgumentException {
                String htmlEscape = HtmlUtils.htmlEscape(StringUtils.trimToEmpty(text));
                htmlEscape = StringUtils.replace(htmlEscape, "'", "''");
                htmlEscape = StringUtils.replace(htmlEscape, "&gt;", ">");
                htmlEscape = StringUtils.replace(htmlEscape, "&#39;", "'");
                setValue(htmlEscape);
            }
        });

        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });

        // Timestamp 类型转换
        binder.registerCustomEditor(Timestamp.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                Date date = DateUtils.parseDate(text);
                setValue(date == null ? null : new Timestamp(date.getTime()));
            }
        });


    }
}
