package cn.ac.picb.ipac.mq.venas;

import cn.ac.picb.ipac.config.venas.MqProperties1;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MessageSender1 {

    private final RabbitTemplate rabbitTemplate;
    private final MqProperties1 mqProperties;

    /**
     * 任务创建发送 任务id以及用户选择的文件路径
     *
     * @param msg task
     */
    public void sendTaskMessage(Venas1Task msg) {
        CorrelationData correlationData = new CorrelationData(msg.getTaskId() + System.currentTimeMillis());
        rabbitTemplate.convertAndSend(mqProperties.getExchange().getName(), mqProperties.getTask().getRouting<PERSON>ey(), msg, correlationData);
    }
}
