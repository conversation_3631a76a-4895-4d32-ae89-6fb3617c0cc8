package cn.ac.picb.ipac.service;

import cn.ac.picb.api.service.FileServiceClient;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.file.vo.PathParamVO;
import cn.ac.picb.file.vo.TreeNode;
import cn.ac.picb.ipac.model.User;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class FtpService {
    private final FileServiceClient fileServiceClient;

    public List<TreeNode> findTreeNode(User user, String parentPath) {
        if (user == null) {
            return new ArrayList<>();
        }
        PathParamVO vo = new PathParamVO();
        vo.setUsername(user.getAccountName());
        vo.setPath(parentPath);
        CommonResult<List<TreeNode>> result = fileServiceClient.fileTreeNodes(vo);
        result.checkError();
        return result.getData();
    }
}
