package cn.ac.picb.ipac.controller;

import cn.ac.picb.file.vo.FileList;
import cn.ac.picb.ipac.common.core.PageBaseUrl;
import cn.ac.picb.ipac.common.core.Result;
import cn.ac.picb.ipac.common.core.ResultGenerator;
import cn.ac.picb.ipac.common.enums.TaskStatusEnum;
import cn.ac.picb.ipac.common.enums.TaskType;
import cn.ac.picb.ipac.common.enums.venas.VenasTaskStatusEnum;
import cn.ac.picb.ipac.common.util.FileUtils;
import cn.ac.picb.ipac.dto.VenasTaskSearch;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.mq.ipp.AnalysisTask;
import cn.ac.picb.ipac.mq.ipp.MessageSender;
import cn.ac.picb.ipac.mq.venas.MessageSender1;
import cn.ac.picb.ipac.mq.venas.MessageSender2;
import cn.ac.picb.ipac.mq.venas.Venas1Task;
import cn.ac.picb.ipac.mq.venas.Venas2Task;
import cn.ac.picb.ipac.service.TaskService;
import cn.ac.picb.ipac.service.UserService;
import cn.ac.picb.ipac.vo.UserSaveVo;
import cn.ac.picb.ipac.vo.UserTaskSearch;
import cn.ac.picb.ipac.vo.UserTaskVo;
import cn.ac.picb.ipac.vo.venas.VenasTaskVO;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.Set;

/**
 * 个人中心
 */
@Controller
@RequestMapping("/usercenter")
@AllArgsConstructor
public class UserController extends BaseController {
    private final TaskService taskService;
    private final MessageSender sender;
    private final UserService userService;
    private final MessageSender1 sender1;
    private final MessageSender2 sender2;

    /**
     * 我的文件列表
     */
    @RequestMapping("/files")
    public String files(Model model, String path) {
        FileList result = userService.listUserDirectoryFiles(getUser(), path);
        // 整理导航路径
        Map<String, String> pathMap = FileUtils.wrapFilePath(path);

        model.addAttribute("data", result);
        model.addAttribute("currentPath", pathMap);
        return "user/my-files";
    }

    /**
     * ipp任务列表
     *
     * @param model
     * @param pageable
     * @return
     */
    @RequestMapping("/tasks")
    @PageBaseUrl(path = "/usercenter/tasks")
    public String mytasks(Model model, @PageableDefault Pageable pageable, @ModelAttribute("search") UserTaskSearch search) {
        Page<UserTaskVo> page = taskService.findTaskPage(getUser(), pageable, search);
        Map<Integer, String> code$DescMap = TaskStatusEnum.getCode$DescMap();
        model.addAttribute("code$DescMap", code$DescMap);
        model.addAttribute("page", page);

        Set<String> statusDescSet = TaskStatusEnum.getValidDescSet();
        model.addAttribute("statusDescSet", statusDescSet);
        return "user/my-tasks";
    }

    /**
     * venas任务列表
     *
     * @param model
     * @param pageable
     * @return
     */
    @RequestMapping("/venas/tasks")
    @PageBaseUrl(path = "/usercenter/venas/tasks")
    public String venastasks(Model model, @PageableDefault Pageable pageable, @ModelAttribute("search") VenasTaskSearch search) {
        Page<VenasTaskVO> page = taskService.findVenasTaskPage(getUser(), pageable, search);
        Map<Integer, String> code$DescMap = VenasTaskStatusEnum.getCode$DescMap();
        model.addAttribute("code$DescMap", code$DescMap);
        model.addAttribute("page", page);

        Set<String> statusDescSet = VenasTaskStatusEnum.getValidDescSet();
        model.addAttribute("statusDescSet", statusDescSet);
        return "user/venas/task-list";
    }

    /**
     * venas任务列表-下载结果压缩包
     *
     * @param id
     * @param request
     * @param response
     * @throws IOException
     */
    @RequestMapping("/venas/downLoadResult")
    public void venasDownLoadResult(String id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        UserTask userTask = taskService.findUserTaskById(id);
        taskService.venasDownLoadResult(userTask, response, request);
    }

    /**
     * venas任务列表-重新分析
     *
     * @param id
     * @return
     */
    @RequestMapping("/venas/reanalysis")
    @ResponseBody
    public Result venas1Reanalysis(String id) {
        UserTask userTask = taskService.findUserTaskById(id);
        if (StrUtil.equals(userTask.getType(), TaskType.venas1.name())) {
            Venas1Task msg = taskService.venas1Reanalysis(userTask, getUser());
            sender1.sendTaskMessage(msg);
        } else {
            Venas2Task msg = taskService.venas2Reanalysis(userTask, getUser());
            sender2.sendTaskMessage(msg);
        }
        return ResultGenerator.genSuccessResult();
    }


    /**
     * venas任务列表-编辑备注
     * @param id
     * @return
     */
    @RequestMapping("/venas/saveRemark")
    @ResponseBody
    public Result saveRemark(String id, String remark) {
        taskService.saveRemark(id,  remark, getUser());
        return ResultGenerator.genSuccessResult();
    }

    /**
     * 任务列表-重新分析(ipp)
     *
     * @param id
     * @return
     */
    @RequestMapping("/reanalysis")
    @ResponseBody
    public Result reanalysis(String id) {
        AnalysisTask msg = taskService.reanalysis(id, getUser());
        sender.sendTaskMessage(msg);
        return ResultGenerator.genSuccessResult();
    }

    /**
     * 任务列表-删除任务
     *
     * @param id
     * @return
     */
    @RequestMapping("/deleteTask")
    @ResponseBody
    public Result deleteTask(String id) {
        taskService.deleteTask(id);
        return ResultGenerator.genSuccessResult();
    }

    /**
     * 我的文件-删除文件
     *
     * @param id
     * @return
     */
    @RequestMapping("/deleteFile")
    @ResponseBody
    public Result deleteFile(String id) {
        taskService.deleteFile(id, getUser());
        return ResultGenerator.genSuccessResult();
    }


    /**
     * ipp任务列表-下载pdf文件
     *
     * @param id
     * @param request
     * @param response
     * @throws IOException
     */
    @RequestMapping("/downLoadPdf")
    public void downLoadPdf(String id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        UserTask userTask = taskService.findUserTaskById(id);
        taskService.downLoadPdfZip(userTask, request, response);
    }

    /**
     * ipp任务列表-下载结果压缩包
     *
     * @param id
     * @param request
     * @param response
     * @throws IOException
     */
    @RequestMapping("/downLoadResult")
    public void downLoadResult(String id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        UserTask userTask = taskService.findUserTaskById(id);
        taskService.downLoadResult(userTask, response, request);
    }

    @RequestMapping("/info")
    public String info(Model model) {
        User user = getUser();
        model.addAttribute("user", user);
        return "user_info";
    }

    @RequestMapping("/saveInfo")
    @ResponseBody
    public Result saveInfo(UserSaveVo vo) {
        userService.saveInfo(vo, getUser());
        return ResultGenerator.genSuccessResult();
    }

    @RequestMapping("/savePwd")
    @ResponseBody
    public Result savePwd(String pwd1) {
        userService.savePwd(pwd1, getUser());
        return ResultGenerator.genSuccessResult();
    }


}
