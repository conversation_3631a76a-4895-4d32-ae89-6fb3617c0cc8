package cn.ac.picb.ipac.interceptor;


import cn.ac.picb.ipac.common.core.PageBaseUrl;
import cn.ac.picb.ipac.config.Constants;
import org.springframework.util.Assert;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class PageUrlInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        if (modelAndView != null && modelAndView.getModel().containsKey(Constants.BASE_URL)) {
            modelAndView.addObject(Constants.BASE_URL, null);
        }

        if (handler instanceof HandlerMethod) {
            if (((HandlerMethod) handler).hasMethodAnnotation(PageBaseUrl.class)) {
                PageBaseUrl tag = ((HandlerMethod) handler).getMethodAnnotation(PageBaseUrl.class);
                Assert.hasText(tag.path(), "Page url is empty");
                modelAndView.addObject(Constants.BASE_URL, tag == null ? null : tag.path());
            }
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
