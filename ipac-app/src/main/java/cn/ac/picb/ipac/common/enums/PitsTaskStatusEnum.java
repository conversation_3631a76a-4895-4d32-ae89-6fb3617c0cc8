package cn.ac.picb.ipac.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

public enum PitsTaskStatusEnum {
    // 任务出错
    error(-1, "Analysis error", "任务出错"),

    // 任务已删除
    delete(0, "Analysis delete", "任务已删除"),

    // 任务准备中
    draft(1, "Analysis is in preparation", "分析准备中"),

    // 分析数据准备完成
    ready(2, "Data preparation", "数据准备"),

    // 多样性分析
    start(3, " Analysis start", "分析开始"),
    
    // running
    running(4, "Analysis Running", "分析进行中"),

    complete(5, "Analysis done", "分析完成");

    @Getter
    private final Integer code;

    @Getter
    private final String enDesc;

    @Getter
    private final String cnDesc;

    PitsTaskStatusEnum(Integer code, String enDesc, String cnDesc) {
        this.code = code;
        this.enDesc = enDesc;
        this.cnDesc = cnDesc;
    }

    public static Map<Integer, String> statusMap(String locale) {
        Map<Integer, String> map = new HashMap<>();
        for (PitsTaskStatusEnum status : values()) {
            if ("en".equals(locale)) {
                map.put(status.getCode(), status.getEnDesc());
            } else {
                map.put(status.getCode(), status.getCnDesc());
            }
        }
        return map;
    }

    public static PitsTaskStatusEnum getEnum(Integer code) {
        for (PitsTaskStatusEnum value : PitsTaskStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
