package cn.ac.picb.ipac.repository;

import cn.ac.picb.ipac.model.UserTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface UserTaskRepository extends JpaRepository<UserTask, String>, JpaSpecificationExecutor<UserTask> {
    boolean existsByTaskId(String taskId);

    @Query("select a from UserTask a  left join fetch a.user u where a.taskId = ?1")
    Optional<UserTask> findByTaskId(String taskId);


    /**
     * 用户任务列表
     *
     * @param userId user id
     * @param status status
     * @return list
     */
    List<UserTask> findByUser_IdAndStatus(String userId, Integer status);

}
