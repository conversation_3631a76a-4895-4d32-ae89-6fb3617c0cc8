package cn.ac.picb.ipac.config;

import cn.ac.picb.ipac.interceptor.CommonAttributesInterceptor;
import cn.ac.picb.ipac.interceptor.HistoryRequestCacheInterceptor;
import cn.ac.picb.ipac.interceptor.HtmlTileInterceptor;
import cn.ac.picb.ipac.interceptor.PageUrlInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.view.tiles3.TilesConfigurer;
import org.springframework.web.servlet.view.tiles3.TilesView;
import org.springframework.web.servlet.view.tiles3.TilesViewResolver;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

    /**
     * 统一异常处理
     */
    @Override
    public void configureHandlerExceptionResolvers(List<HandlerExceptionResolver> exceptionResolvers) {
        exceptionResolvers.add(new AppHandlerExceptionResolver());
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addRedirectViewController("/","/usercenter/files");
        registry.addViewController("accessDenied").setViewName("error/accessDenied");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HistoryRequestCacheInterceptor());
        registry.addInterceptor(new PageUrlInterceptor());
        registry.addInterceptor(new HtmlTileInterceptor());
/*        registry.addInterceptor(new CommonAttributesInterceptor());*/
        registry.addInterceptor(new CurrentUserInterceptor());
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new UserArgumentResolver());
    }

}
