package cn.ac.picb.ipac.config.venas;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "venas1.rabbitmq")
@Data
public class MqProperties1 {

    private Exchange exchange = new Exchange();

    private TaskQueueConfig task = new TaskQueueConfig();

    @Data
    public class TaskQueueConfig {

        private String routingKey = "venas1.analysis.task";

        private String queueName = "venas1-analysis-task-queue";
    }

    @Data
    public class Exchange {

        private String name = "venas1-analysis";

        private String ignoreDeclarationExceptions = "true";
    }
}
