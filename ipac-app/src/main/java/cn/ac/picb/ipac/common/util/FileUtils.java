package cn.ac.picb.ipac.common.util;

import cn.ac.picb.ipac.common.enums.DirectoryEnum;
import cn.ac.picb.ipac.config.Constants;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.LinkedHashMap;
import java.util.Map;


public class FileUtils extends org.apache.commons.io.FileUtils {

    private static final String[] IMAGES_SUFFIXES = {"bmp", "jpg", "jpeg", "gif", "png"};
    private static final String[] PDF_SUFFIXES = {"pdf"};

    private static final String[] DEFAULT_ALLOWED_EXTENSION = {
            // 图片
            "bmp", "gif", "jpg", "jpeg", "png",
            // word excel powerpoint
            "doc", "docx", "xls", "xlsx", "ppt", "pptx", "html", "htm", "txt",
            // 压缩文件
            "rar", "zip", "gz", "bz2",
            // pdf
            "pdf"};

    public static boolean assertAllowed(String filename) {
        if (!(filename != null && filename.trim().length() != 0)) {
            return false;
        }
        return ArrayUtils.contains(DEFAULT_ALLOWED_EXTENSION, FilenameUtils.getExtension(filename).toLowerCase());
    }


    public static File getDataHome(DirectoryEnum directoryEnum) {
        String dataHome = Constants.dataHome;
        if (StringUtils.isBlank(dataHome))
            dataHome = System.getProperty("user.home") + File.separator + Constants.appName;
        File homeDir = new File(dataHome);
        if (!homeDir.exists() || !homeDir.isDirectory())
            homeDir.mkdirs();
        if (directoryEnum == null)
            return homeDir;
        File dir = new File(homeDir, directoryEnum.name());
        if (!dir.exists() || !dir.isDirectory())
            dir.mkdirs();
        return dir;
    }

    public static Map<String, String> wrapFilePath(String path) {

        Map<String, String> pathMap = new LinkedHashMap<>();
        //先指定根目录
        if (pathMap.size() == 0) {
            pathMap.put("root", "/");
        }
        if (StringUtils.isBlank(path)) {
            return pathMap;
        }
        String[] pathNames = StringUtils.split(path, "/");
        String currentPath = "/";
        for (String pathName : pathNames) {
            String value;
            if (StrUtil.equals("/", currentPath)) {
                value = currentPath += pathName;
            } else {
                value = currentPath += "/" + pathName;
            }
            pathMap.put(pathName, value);
        }
        return pathMap;
    }
}
