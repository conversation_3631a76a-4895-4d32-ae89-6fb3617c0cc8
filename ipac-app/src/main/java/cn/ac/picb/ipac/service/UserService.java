package cn.ac.picb.ipac.service;

import cn.ac.picb.api.service.FileServiceClient;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.file.vo.FileList;
import cn.ac.picb.file.vo.PathParamVO;
import cn.ac.picb.ipac.common.core.ServiceException;
import cn.ac.picb.ipac.common.util.AESUtil;
import cn.ac.picb.ipac.common.util.IdUtil;
import cn.ac.picb.ipac.config.Constants;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.repository.UserRepository;
import cn.ac.picb.ipac.vo.UserSaveVo;
import cn.ac.picb.ipac.vo.UserVo;
import cn.ac.picb.ipac.vo.UserVo2;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.UUID;

@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private MailService mailService;
    @Autowired
    private FileServiceClient fileServiceClient;

    public static final String DEFAULT_PWD = "111111";

    @Transactional(rollbackFor = Exception.class)
    public void saveUser(UserVo userVo) {
        User dbUserName = userRepository.findFirstByAccountName(userVo.getAccountname());
        if (dbUserName != null) {
            throw new ServiceException("The account name has already been registered");
        }
        if (!StringUtils.equals(userVo.getPassword1(), userVo.getPassword2())) {
            throw new ServiceException("Two passwords do not match");
        }

        User dbUserEmail = userRepository.findFirstByEmail(userVo.getUseremail());
        if (dbUserEmail != null) {
            throw new ServiceException("This email has already been registered");
        }
        Date date = new Date();
        User user = new User();
        user.setId(IdUtil.getShortUUID());
        user.setAccountName(userVo.getAccountname());
        user.setPassword(passwordEncoder.encode(userVo.getPassword1()));
        user.setEmail(userVo.getUseremail());
        user.setCreateTime(date);
        user.setModifyTime(date);
        user.setFtpName(userVo.getAccountname());

        user.setName(userVo.getName());
        user.setTel(userVo.getTel());
        user.setOrganization(userVo.getOrganization());

        user.setFtpToken(AESUtil.encrypt(user.getFtpName() + "::" + userVo.getPassword1(), Charset.defaultCharset(), Constants.secretKey));
        userRepository.saveAndFlush(user);
    }

    public void forgetPwd2(String useremail) {
        if (StringUtils.isBlank(useremail)) {
            throw new ServiceException("data exception！");
        }
        User dbUserEmail = userRepository.findFirstByEmail(useremail);
        if (dbUserEmail == null) {
            throw new ServiceException("This email is not registered！");
        }
        String subject = "病原微生物综合分析云平台";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("email", useremail);
        jsonObject.put("date", new Date());
        jsonObject.put("str", UUID.randomUUID().toString());
        String token = AESUtil.encrypt(jsonObject.toJSONString(), null);
        String url;
        if (Constants.baseUrl.contains(Constants.appName)) {
            url = Constants.baseUrl + "/resetPwd?token=" + token;
        } else {
            url = Constants.baseUrl + "/" + Constants.appName + "/resetPwd?token=" + token;
        }
        String htmlContent = "";
        try {
            File file = ResourceUtils.getFile("classpath:static/mail/mail.html");
            Document document = Jsoup.parse(file, "utf-8");
            Element resetPwdUrl = document.getElementById("resetPwdUrl");
            resetPwdUrl.attr("href", url);
            resetPwdUrl.text(url);
            htmlContent = document.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        mailService.sendHtmlMail(useremail, subject, htmlContent);

    }

    @Transactional(rollbackFor = Exception.class)
    public void resetPwd(String token) {
        String decrypt = AESUtil.decrypt(token, null);
        JSONObject jsonObject = JSONObject.parseObject(decrypt);
        String email = jsonObject.getString("email");
        Date date = jsonObject.getDate("date");
        if ((System.currentTimeMillis() - date.getTime()) > 10 * 60 * 1000) {
            throw new ServiceException("The link is invalid");
        }
        User dbUserEmail = userRepository.findFirstByEmail(email);
        if (dbUserEmail == null) {
            throw new ServiceException("This email is not registered！");
        }
        dbUserEmail.setPassword(passwordEncoder.encode(DEFAULT_PWD));
        dbUserEmail.setFtpToken(AESUtil.encrypt(dbUserEmail.getFtpName() + "::" + DEFAULT_PWD, Charset.defaultCharset(), Constants.secretKey));

        userRepository.saveAndFlush(dbUserEmail);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveInfo(UserSaveVo vo, User user) {
        User dbUser = userRepository.findById(user.getId()).orElseThrow(() -> new ServiceException("data exception！"));
        dbUser.setName(vo.getName());
        dbUser.setAge(vo.getAge());
        dbUser.setTel(vo.getTel());
        dbUser.setOrganization(vo.getOrganization());

        if (!StringUtils.equals(dbUser.getEmail(), vo.getEmail())) {
            User firstByEmail = userRepository.findFirstByEmail(vo.getEmail());
            if (firstByEmail != null && !firstByEmail.getAccountName().equals(dbUser.getAccountName())) {
                throw new ServiceException("The email has been registered！");
            }
        }
        dbUser.setEmail(vo.getEmail());
        userRepository.saveAndFlush(dbUser);
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePwd(String pwd1, User user) {
        if (StringUtils.isBlank(pwd1)) {
            throw new ServiceException("data exception！");
        }
        User dbUser = userRepository.findById(user.getId()).orElseThrow(() -> new ServiceException("data exception！"));
        dbUser.setPassword(passwordEncoder.encode(pwd1));
        dbUser.setFtpToken(AESUtil.encrypt(user.getFtpName() + "::" + pwd1, Charset.defaultCharset(), Constants.secretKey));
        userRepository.saveAndFlush(dbUser);
    }

    /**
     * 注册申请
     *
     * @param userVo
     */
    public void regApply(UserVo2 userVo) {
        String subject = "病原微生物综合分析云平台";
        String htmlContent = "注册已申请，请耐心等待，工作人员将通过邮件发送审核结果。";
        mailService.sendHtmlMail(userVo.getUseremail(), subject, htmlContent);

        StringBuffer sb = new StringBuffer();
        sb.append("vic用户注册信息：姓名：" + userVo.getName());
        sb.append(";邮件：" + userVo.getUseremail());
        sb.append(";单位：" + userVo.getOrganization());
        sb.append(";联系电话：" + userVo.getTel());
        mailService.sendHtmlMail(Constants.registerEmail, subject, sb.toString());
    }

    public FileList listUserDirectoryFiles(User user, String path) {
        if (StringUtils.isBlank(path)) {
            path = "/";
        }
        PathParamVO vo = new PathParamVO();
        vo.setUsername(user.getAccountName());
        vo.setPath(path);
        CommonResult<FileList> result = fileServiceClient.listDir(vo);
        result.checkError();
        return result.getData();
    }
}
