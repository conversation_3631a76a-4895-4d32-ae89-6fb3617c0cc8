package cn.ac.picb.ipac.service;

import cn.ac.picb.api.service.FileServiceClient;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.file.vo.FileList;
import cn.ac.picb.file.vo.PathParamVO;
import cn.ac.picb.ipac.common.core.ServiceException;
import cn.ac.picb.ipac.common.util.AESUtil;
import cn.ac.picb.ipac.common.util.FIleSizeUtil;
import cn.ac.picb.ipac.common.util.ld.PairMatchUtils;
import cn.ac.picb.ipac.common.util.ld.PairNode;
import cn.ac.picb.ipac.config.Constants;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.vo.FileTreeNode;
import cn.ac.picb.ipac.vo.FtpFileVo;
import com.alibaba.fastjson.JSONArray;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AnalysisService {
    @Autowired
    private FileServiceClient fileServiceClient;

    public FileTreeNode findFileTree(User user) {
        //tood
  /*      FileTreeNode fileTreeNode = new FileTreeNode();
        if (StringUtils.isBlank(user.getFtpName())) {
            return fileTreeNode;
        }
        String token = AESUtil.encrypt(user.getFtpName() + "::" + user.getPassword(), Charset.defaultCharset(), Constants.secretKey);
        ApiResult<List<FtpFile>> result = ftpRemoteService.findAllFiles(token);
        if (result == null) {
            throw new ServiceException("List directory error");
        }
        if (result.getCode() != 200) {
            throw new ServiceException("List directory error: " + result.getMessage());
        }
        return wrapFileTreeNode(result.getData());*/
  return null;
    }

/*    private FileTreeNode wrapFileTreeNode(List<FtpFile> data) {
        Map<String, List<FtpFile>> fileMap = data.stream().filter(f -> StringUtils.isNotBlank(f.getParentPath())).collect(Collectors.groupingBy(FtpFile::getParentPath));
        FileTreeNode fileTreeNode = new FileTreeNode();
        List<FileTreeNode> children = getFileTreeNodeChildren(fileMap, "/", fileTreeNode);
        fileTreeNode.setChild(children);
        return fileTreeNode;
    }*/

   /* private List<FileTreeNode> getFileTreeNodeChildren(Map<String, List<FtpFile>> fileMap, String parentPath, FileTreeNode parent) {
        List<FileTreeNode> list = new ArrayList<>();
        List<FtpFile> ftpFiles = fileMap.get(parentPath);
        if (CollectionUtils.isEmpty(ftpFiles)) {
            return null;
        }
        for (FtpFile file : ftpFiles) {
            FileTreeNode fileTreeNode = new FileTreeNode();
            list.add(fileTreeNode);
            fileTreeNode.setParent(parent);
            FtpFileVo vo = new FtpFileVo();
            vo.setFtpFile(file);
            vo.setSize(FIleSizeUtil.sizeFormat(file.getFileSize(), 1).replace(" ", "").replace("KB", "K"));
            fileTreeNode.setFtpFileVo(vo);
            StringBuilder sb = new StringBuilder();
            sb.append(file.getParentPath());
            if (!file.getParentPath().endsWith("/")) {
                sb.append("/");
            }
            sb.append(file.getFileName());
            List<FileTreeNode> children = getFileTreeNodeChildren(fileMap, sb.toString(), fileTreeNode);
            fileTreeNode.setChild(children);
        }
        return list;
    }*/

    /**
     * FTP已上传数据自动配对
     *
     * @param user
     * @param folderId
     * @return
     */
    /*public JSONArray matchFileGroup(User user, String[] folderId) {
        if (folderId == null || folderId.length == 0) {
            throw new ServiceException("请选择文件夹");
        }
        String token = AESUtil.encrypt(user.getFtpName() + "::" + user.getPassword(), Charset.defaultCharset(), Constants.secretKey);
        ApiResult<List<FtpFile>> result = ftpRemoteService.findAllFiles(token);
        if (result == null) {
            throw new ServiceException("List directory error");
        }
        if (result.getCode() != 200) {
            throw new ServiceException("List directory error: " + result.getMessage());
        }
        List<FtpFile> data = result.getData();
        JSONArray jsonArray = new JSONArray();

        if (CollectionUtils.isNotEmpty(data)) {
            List<String> parentPath = new ArrayList<>();
            if (ArrayUtils.contains(folderId, "root")) {
                parentPath.add("/");
            }
            for (FtpFile ftpFile : data) {
                if ("F".equals(ftpFile.getType())) {
                    continue;
                }
                if (ArrayUtils.contains(folderId, ftpFile.getId())) {
                    parentPath.add(ftpFile.getParentPath() + ("/".equals(ftpFile.getParentPath()) ? "" : "/") + ftpFile.getFileName());
                }
            }
            Map<String, List<FtpFile>> collect = data.stream()
                    .filter(f -> "F".equals(f.getType()) && parentPath.contains(f.getParentPath()))
                    .collect(Collectors.groupingBy(FtpFile::getParentPath));
            for (List<FtpFile> value : collect.values()) {
                JSONArray jsonArray1 = getMatchFileGroup(value);
                jsonArray.addAll(jsonArray1);
            }
        }
        return jsonArray;
    }*/

    /**
     * 同一文件夹下的文件进行配对
     * @return
     */
   /* private JSONArray getMatchFileGroup(List<FtpFile> value) {
        JSONArray jsonArray = new JSONArray();
        if (CollectionUtils.isEmpty(value)) {
            return jsonArray;
        }
        List<String> fileNames = value.stream().filter(f -> checkFileName(f.getFileName())).map(FtpFile::getFileName).collect(Collectors.toList());
        fileNames.sort(Comparator.naturalOrder());
        List<PairNode> pairNodeList = PairMatchUtils.match(fileNames);
        Map<String, FtpFile> fileName$FtpFileMap = value.stream().collect(Collectors.toMap(FtpFile::getFileName, f -> f, (k1, k2) -> k1));
        Set<String> processed = new HashSet<>();
        if (CollectionUtils.isNotEmpty(pairNodeList)) {
            for (PairNode pairNode : pairNodeList) {
                if (processed.contains(pairNode.getK()) || processed.contains(pairNode.getV())) {
                    continue;
                }
                FtpFile next = fileName$FtpFileMap.get(pairNode.getK());
                FtpFile next2 = fileName$FtpFileMap.get(pairNode.getV());
                List<FtpFile> list = new ArrayList<>(2);
                list.add(next);
                list.add(next2);
                jsonArray.add(list);
                processed.add(pairNode.getK());
                processed.add(pairNode.getV());
            }
        }
        return jsonArray;
    }*/

    private boolean checkFileName(String fileName) {
        if (fileName.endsWith(".fq.gz") || fileName.endsWith(".fastq.gz")) {
            return true;
        }
        return false;
    }
}
