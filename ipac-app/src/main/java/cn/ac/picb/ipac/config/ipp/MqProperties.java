package cn.ac.picb.ipac.config.ipp;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "ncov.rabbitmq")
@Data
public class MqProperties {

    private Exchange exchange = new Exchange();

    private ErrorQueueConfig error = new ErrorQueueConfig();

    private StatusQueueConfig status = new StatusQueueConfig();

    private TaskQueueConfig task = new TaskQueueConfig();

    private WebQueueConfig web = new WebQueueConfig();

    @Data
    public class TaskQueueConfig {

        private String routingKey = "ncov.analysis.task";

        private String queueName = "analysis-task-queue";
    }

    @Data
    public class WebQueueConfig {

        private String routingKey = "ncov.analysis.web";

        private String queueName = "analysis-web-queue";
    }

    @Data
    public class ErrorQueueConfig {

        private String routingKey = "ncov.analysis.error";

        private String queueName = "analysis-error-queue";
    }

    @Data
    public class StatusQueueConfig {

        private String routingKey = "ncov.analysis.status";

        private String queueName = "analysis-status-queue";
    }

    @Data
    public class Exchange {

        private String name = "analysis";

        private String ignoreDeclarationExceptions = "true";
    }
}
