package cn.ac.picb.ipac.config.gvap;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@Configuration
public class GvapRabbitmqConfig implements BeanDefinitionRegistryPostProcessor {

    private static final String EXCHANGE_NAME = "analysis_exchange";
    private static final String PREFIX = "snpfromfa_";
    private static final String QUEUE_NAME_SUFFIX = "_queue";
    private static final String ROUTING_KEY_SUFFIX = "_routing_key";


    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        // 配置状态变更队列
        configStatusQueue(registry);

        // 配置 create queue
        configCreateQueue(registry);

        // 配置 start queue
        configStartQueue(registry);

        // 配置 complete queue
        configCompleteQueue(registry);

        // 配置 finished queue
        configFinishedQueue(registry);
    }

    private void configFinishedQueue(BeanDefinitionRegistry registry) {
        BeanDefinitionBuilder queueBuilder = BeanDefinitionBuilder.genericBeanDefinition(Queue.class);
        String queueName = PREFIX + "task_finished" + QUEUE_NAME_SUFFIX;
        queueBuilder.addConstructorArgValue(queueName);
        registry.registerBeanDefinition(PREFIX + "taskFinishedQueue", queueBuilder.getBeanDefinition());

        BeanDefinitionBuilder bindingBuilder = BeanDefinitionBuilder.genericBeanDefinition(Binding.class);
        bindingBuilder.addConstructorArgValue(PREFIX + "task_finished" + QUEUE_NAME_SUFFIX);
        bindingBuilder.addConstructorArgValue(Binding.DestinationType.QUEUE);
        bindingBuilder.addConstructorArgValue(EXCHANGE_NAME);
        bindingBuilder.addConstructorArgValue(PREFIX + "task_finished" + ROUTING_KEY_SUFFIX);
        bindingBuilder.addConstructorArgValue(Collections.emptyMap());
        registry.registerBeanDefinition(PREFIX + "taskFinishedBinding", bindingBuilder.getBeanDefinition());
    }

    private void configCompleteQueue(BeanDefinitionRegistry registry) {
        BeanDefinitionBuilder queueBuilder = BeanDefinitionBuilder.genericBeanDefinition(Queue.class);
        String queueName = PREFIX + "analysis_complete" + QUEUE_NAME_SUFFIX;
        queueBuilder.addConstructorArgValue(queueName);
        registry.registerBeanDefinition(PREFIX + "analysisCompleteQueue", queueBuilder.getBeanDefinition());

        BeanDefinitionBuilder bindingBuilder = BeanDefinitionBuilder.genericBeanDefinition(Binding.class);
        bindingBuilder.addConstructorArgValue(PREFIX + "analysis_complete" + QUEUE_NAME_SUFFIX);
        bindingBuilder.addConstructorArgValue(Binding.DestinationType.QUEUE);
        bindingBuilder.addConstructorArgValue(EXCHANGE_NAME);
        bindingBuilder.addConstructorArgValue(PREFIX + "analysis_complete" + ROUTING_KEY_SUFFIX);
        bindingBuilder.addConstructorArgValue(Collections.emptyMap());
        registry.registerBeanDefinition(PREFIX + "analysisCompleteBinding", bindingBuilder.getBeanDefinition());
    }

    private void configStartQueue(BeanDefinitionRegistry registry) {
        BeanDefinitionBuilder queueBuilder = BeanDefinitionBuilder.genericBeanDefinition(Queue.class);
        String queueName = PREFIX + "analysis_start" + QUEUE_NAME_SUFFIX;
        queueBuilder.addConstructorArgValue(queueName);
        registry.registerBeanDefinition(PREFIX + "analysisStartQueue", queueBuilder.getBeanDefinition());

        BeanDefinitionBuilder bindingBuilder = BeanDefinitionBuilder.genericBeanDefinition(Binding.class);
        bindingBuilder.addConstructorArgValue(PREFIX + "analysis_start" + QUEUE_NAME_SUFFIX);
        bindingBuilder.addConstructorArgValue(Binding.DestinationType.QUEUE);
        bindingBuilder.addConstructorArgValue(EXCHANGE_NAME);
        bindingBuilder.addConstructorArgValue(PREFIX + "analysis_start" + ROUTING_KEY_SUFFIX);
        bindingBuilder.addConstructorArgValue(Collections.emptyMap());
        registry.registerBeanDefinition(PREFIX + "analysisStartBinding", bindingBuilder.getBeanDefinition());
    }

    private void configCreateQueue(BeanDefinitionRegistry registry) {
        BeanDefinitionBuilder queueBuilder = BeanDefinitionBuilder.genericBeanDefinition(Queue.class);
        String queueName = PREFIX + "task_create" + QUEUE_NAME_SUFFIX;
        queueBuilder.addConstructorArgValue(queueName);
        registry.registerBeanDefinition(PREFIX + "taskCreateQueue", queueBuilder.getBeanDefinition());

        BeanDefinitionBuilder bindingBuilder = BeanDefinitionBuilder.genericBeanDefinition(Binding.class);
        bindingBuilder.addConstructorArgValue(PREFIX + "task_create" + QUEUE_NAME_SUFFIX);
        bindingBuilder.addConstructorArgValue(Binding.DestinationType.QUEUE);
        bindingBuilder.addConstructorArgValue(EXCHANGE_NAME);
        bindingBuilder.addConstructorArgValue(PREFIX + "task_create" + ROUTING_KEY_SUFFIX);
        bindingBuilder.addConstructorArgValue(Collections.emptyMap());
        registry.registerBeanDefinition(PREFIX + "taskCreateBinding", bindingBuilder.getBeanDefinition());
    }

    private void configStatusQueue(BeanDefinitionRegistry registry) {
        BeanDefinitionBuilder queueBuilder = BeanDefinitionBuilder.genericBeanDefinition(Queue.class);
        String queueName = PREFIX + "status_flow" + QUEUE_NAME_SUFFIX;
        queueBuilder.addConstructorArgValue(queueName);
        registry.registerBeanDefinition(PREFIX + "statusQueue", queueBuilder.getBeanDefinition());

        BeanDefinitionBuilder bindingBuilder = BeanDefinitionBuilder.genericBeanDefinition(Binding.class);
        bindingBuilder.addConstructorArgValue(PREFIX + "status_flow" + QUEUE_NAME_SUFFIX);
        bindingBuilder.addConstructorArgValue(Binding.DestinationType.QUEUE);
        bindingBuilder.addConstructorArgValue(EXCHANGE_NAME);
        bindingBuilder.addConstructorArgValue(PREFIX + "status_flow" + ROUTING_KEY_SUFFIX);
        bindingBuilder.addConstructorArgValue(Collections.emptyMap());
        registry.registerBeanDefinition(PREFIX + "statusBinding", bindingBuilder.getBeanDefinition());
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {

    }

}
