package cn.ac.picb.ipac.service;

import cn.ac.picb.ipac.bo.EdgeData;
import cn.ac.picb.ipac.bo.NodeData;
import cn.ac.picb.ipac.bo.Venas2ResultBO;
import cn.ac.picb.ipac.common.core.ServiceException;
import cn.ac.picb.ipac.common.enums.TaskType;
import cn.ac.picb.ipac.config.venas.VenasProperties;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.repository.UserTaskRepository;
import cn.ac.picb.ipac.vo.venas.GraphData;
import cn.ac.picb.ipac.vo.venas.Venas2ResultVO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.Reader;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class VenasService {
    private final UserTaskRepository userTaskRepository;
    private final VenasProperties venasProperties;

    public GraphData graphData(String id, String clusterId) {
        Optional<UserTask> byId = userTaskRepository.findById(id);
        if (!byId.isPresent()) {
            return new GraphData();
        }
        UserTask task = byId.get();
        File folder = new File(venasProperties.getScriptParentPath(), task.getOutPath());
        if (StrUtil.equals(task.getType(), TaskType.venas1.name())) {
            File nodeFile = new File(folder, "nodes.csv");
            File edgeFile = new File(folder, "edges.csv");
            List<NodeData> nodeData = parseNodeData(nodeFile);
            List<EdgeData> edgeData = parseEdgeData(edgeFile);
            return wrapGraphData(nodeData, edgeData, clusterId);
        } else if (StrUtil.equals(task.getType(), TaskType.venas2.name())) {
            File nodeFile = new File(folder, "nodes.csv");
            File edgeFile = new File(folder, "edges.csv");
            File nodeNewFile = new File(folder, "nodes_new.csv");
            File edgeNewFile = new File(folder, "edges_new.csv");
            List<NodeData> nodeData = parseNodeData(nodeFile);
            List<EdgeData> edgeData = parseEdgeData(edgeFile);
            List<NodeData> nodeDataN = parseNodeData(nodeNewFile);
            List<EdgeData> edgeDataN = parseEdgeData(edgeNewFile);

            Set<String> hapIdsN = nodeDataN.stream().map(NodeData::getHapid).collect(Collectors.toSet());
            nodeData = nodeData.stream().filter(n -> !hapIdsN.contains(n.getHapid())).collect(Collectors.toList());

            Set<String> dgeN = edgeDataN.stream().map(e -> e.getSource() + "_" + e.getTarget()).collect(Collectors.toSet());
            edgeData = edgeData.stream().filter(e -> !dgeN.contains(e.getSource() + "_" + e.getTarget())).collect(Collectors.toList());

            return wrapGraphDataN(nodeData, nodeDataN, edgeData, edgeDataN, clusterId);
        }
        return new GraphData();
    }

    private GraphData wrapGraphDataN(List<NodeData> nodeData, List<NodeData> nodeDataN, List<EdgeData> edgeData, List<EdgeData> edgeDataN, String clusterId) {
        GraphData graphData = new GraphData();
        List<GraphData.GraphNode> nodesOld = wrapNode(nodeData, clusterId, GraphData.GraphNode.HighOrDark.dark);
        List<GraphData.GraphLink> linksOld = wrapLinks(edgeData);

        Set<String> clusterIdSetN = nodeDataN.stream().map(NodeData::getClusterId).collect(Collectors.toSet());
        if (StrUtil.isBlank(clusterId)) {
            nodesOld.forEach(n -> {
                if (clusterIdSetN.contains(n.getClusterId())) {
                    n.setHighOrDark(GraphData.GraphNode.HighOrDark.highlight);
                }
            });
        }
        List<GraphData.GraphNode> nodesN = wrapNode(nodeDataN, clusterId, GraphData.GraphNode.HighOrDark.highlight);
        Set<String> idsN = nodesN.stream().map(GraphData.GraphNode::getHapid).collect(Collectors.toSet());
        List<GraphData.GraphLink> linksN = wrapLinks(edgeDataN);

        linksN.forEach(link -> {
            link.setLineStyle(GraphData.GraphLink.lineStyleHeigh);
            if (idsN.contains(link.getSource())) {
                link.getLineStyle().put("color", "source");
            }
            if (idsN.contains(link.getTarget())) {
                link.getLineStyle().put("color", "target");
            }
        });
        List<GraphData.GraphNode> nodes = new ArrayList<>(nodesOld);
        List<GraphData.GraphLink> links = new ArrayList<>(linksOld);
        nodes.addAll(nodesN);
        links.addAll(linksN);
        graphData.setNodes(nodes);
        graphData.setLinks(links);
        return graphData;
    }

    private GraphData wrapGraphData(List<NodeData> nodeData, List<EdgeData> edgeData, String clusterId) {
        GraphData graphData = new GraphData();
        List<GraphData.GraphNode> nodes = wrapNode(nodeData, clusterId, GraphData.GraphNode.HighOrDark.highlight);
        List<GraphData.GraphLink> links = wrapLinks(edgeData);
        graphData.setNodes(nodes);
        graphData.setLinks(links);
        return graphData;
    }

    private List<GraphData.GraphLink> wrapLinks(List<EdgeData> edgeData) {
        List<GraphData.GraphLink> links = new ArrayList<>();
        for (EdgeData edgeDatum : edgeData) {
            GraphData.GraphLink link = new GraphData.GraphLink();
            link.setDiffs(Arrays.asList(edgeDatum.getMutations().split(";")));
            link.setSource(edgeDatum.getSource());
            link.setTarget(edgeDatum.getTarget());
            try {
                link.setValue(Integer.valueOf(edgeDatum.getDistance()));
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
            links.add(link);
        }
        return links;
    }

    private List<GraphData.GraphNode> wrapNode(List<NodeData> nodeData, String clusterId, String highOrDark) {
        List<GraphData.GraphNode> nodes = new ArrayList<>();
        Map<String, List<NodeData>> clusterIdNodeDataMap = nodeData.stream().collect(Collectors.groupingBy(NodeData::getClusterId));
        Map<String, Map<String, Long>> countryMap = new HashMap<>(32);
        Map<String, Map<String, Long>> countryMap2 = new HashMap<>(32);

        Map<String, Map<String, Long>> virusMap = new HashMap<>(32);
        Map<String, Map<String, Long>> virusMap2 = new HashMap<>(32);

        for (Map.Entry<String, List<NodeData>> stringListEntry : clusterIdNodeDataMap.entrySet()) {
            List<NodeData> value = stringListEntry.getValue();
            String key = stringListEntry.getKey();
            Map<String, Long> collect = value.stream().collect(Collectors.groupingBy(NodeData::getLocations, Collectors.counting()));
            countryMap.put(key, new TreeMap<>(collect));
            Map<String, Long> collect2 = value.stream().filter(v -> !StrUtil.equals(v.getValue(), "0")).collect(Collectors.groupingBy(NodeData::getLocations, Collectors.counting()));
            countryMap2.put(key, new TreeMap<>(collect2));
            List<String> times = new ArrayList<>();
            List<String> times2 = new ArrayList<>();
            for (NodeData data : value) {
                times.add(data.getTimeMin());
                times.add(data.getTimeMax());
                if (!StrUtil.equals(data.getValue(), "0")) {
                    times2.add(data.getTimeMin());
                    times2.add(data.getTimeMax());
                }
            }
            Map<String, Long> map = times.stream().collect(Collectors.groupingBy(d -> d, Collectors.counting()));
            Map<String, Long> map2 = times2.stream().collect(Collectors.groupingBy(d -> d, Collectors.counting()));
            virusMap.put(key, new TreeMap<>(map));
            virusMap2.put(key, new TreeMap<>(map2));
        }
        for (NodeData nodeDatum : nodeData) {
            List<String> haploidIds = new ArrayList<>();
            List<String> sampleIds = new ArrayList<>();
            if (StrUtil.isBlank(clusterId)) {
                if (StrUtil.equals(nodeDatum.getValue(), "0")) {
                    continue;
                }
                //第一层节点能通过所在cluster的第二层节点的样本id或haploidId进行筛选
                for (NodeData data : clusterIdNodeDataMap.getOrDefault(nodeDatum.getClusterId(), new ArrayList<>())) {
                    if (StrUtil.isNotBlank(data.getSampleNames())) {
                        sampleIds.addAll(Arrays.asList(data.getSampleNames().split(";")));
                    }
                    haploidIds.addAll(Collections.singletonList(data.getHapid()));
                }
            }
            if (StrUtil.isNotBlank(clusterId) && !StrUtil.equals(clusterId, nodeDatum.getClusterId())) {
                continue;
            }
            GraphData.GraphNode node = new GraphData.GraphNode();
            node.setId(nodeDatum.getHapid());
            node.setHapid(nodeDatum.getHapid());

            if (StrUtil.isNotBlank(nodeDatum.getSampleNames())) {
                sampleIds.addAll(Arrays.asList(nodeDatum.getSampleNames().split(";")));
            }
            haploidIds.addAll(Collections.singletonList(nodeDatum.getHapid()));
            node.setSampleIds(sampleIds);
            node.setHaploidIds(haploidIds);
            node.setLineage(Collections.singletonList(nodeDatum.getClusterId()));

            node.setNodeId(nodeDatum.getHapid());
            node.setClusterId(nodeDatum.getClusterId());
            node.setClusterSize(nodeDatum.getClusterLen());
            if (StrUtil.isNotBlank(nodeDatum.getMutations()) && nodeDatum.getMutations().length() > 30) {
                node.setMutations(nodeDatum.getMutations().substring(0, 29) + "......");
            } else {
                node.setMutations(nodeDatum.getMutations());
            }
            node.setLabel(nodeDatum.getLabel());
            node.setHighOrDark(highOrDark);
            node.setName(nodeDatum.getHapid());
            try {
                if (StrUtil.isBlank(clusterId)) {
                    node.setValue(Integer.valueOf(nodeDatum.getClusterLen()));
                    node.setCountries(countryMap.get(nodeDatum.getClusterId()));
                    node.setCountries2(countryMap2.get(nodeDatum.getClusterId()));
                    node.setVirus(virusMap.get(nodeDatum.getClusterId()));
                    node.setVirus2(virusMap2.get(nodeDatum.getClusterId()));
                } else {
                    if (StrUtil.equals(nodeDatum.getValue(), "0")) {

                        HashMap<String, Long> map1 = new HashMap<>(8);
                        map1.put(nodeDatum.getLocations(), 1L);
                        node.setCountries(map1);

                        Map<String, Long> virus = new LinkedHashMap<>();
                        virus.put(nodeDatum.getTimeMin(), 1L);
                        virus.put(nodeDatum.getTimeMax(), 1L);
                        node.setValue(virus.size());
                        node.setVirus(virus);
                    } else {
                        node.setValue(Integer.valueOf(nodeDatum.getClusterLen()));
                        node.setCountries(countryMap.get(nodeDatum.getClusterId()));
                        node.setVirus(virusMap.get(nodeDatum.getClusterId()));
                    }
                }
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
            nodes.add(node);
        }
        return nodes;
    }


    private List<NodeData> parseNodeData(File file) {
        //Hapid	Label	Time_min	Time_max	ClusterId	Cluster_len	Value	Mutations	Locations	Samplenames
        Map<String, Integer> titleMap = new HashMap<>(16);
        List<NodeData> list = new ArrayList<>();
        try (Reader reader = FileUtil.getReader(file, Charset.defaultCharset())) {
            CsvReadConfig csvReadConfig = CsvReadConfig.defaultConfig();
            csvReadConfig.setSkipEmptyRows(false);
            CsvReader csvReader = new CsvReader(reader, csvReadConfig);
            CsvData read = csvReader.read();
            int rowIndex = -1;

            for (CsvRow strings : read) {
                rowIndex++;
                if (rowIndex == 0) {
                    for (int i = 0; i < strings.size(); i++) {
                        titleMap.put(strings.get(i), i);
                    }
                    continue;
                }
                if (strings.size() < 9) {
                    continue;
                }
                NodeData data = new NodeData();
                data.setHapid(strings.get(titleMap.get("Hapid")));
                data.setLabel(strings.get(titleMap.get("Label")));
                data.setTimeMin(strings.get(titleMap.get("Time_min")));
                data.setTimeMax(strings.get(titleMap.get("Time_max")));
                data.setClusterId(strings.get(titleMap.get("ClusterId")));
                data.setClusterLen(strings.get(titleMap.get("Cluster_len")));
                data.setValue(strings.get(titleMap.get("Value")));
                data.setMutations(strings.get(titleMap.get("Mutations")));
                data.setLocations(strings.get(titleMap.get("Locations")));
                if (titleMap.containsKey("Samplenames")) {
                    data.setSampleNames(strings.get(titleMap.get("Samplenames")));
                }
                list.add(data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("csv解析失败：" + file);
        }
        return list;
    }

    private List<EdgeData> parseEdgeData(File file) {
        List<EdgeData> list = new ArrayList<>();
        try (Reader reader = FileUtil.getReader(file, Charset.defaultCharset())) {
            CsvReadConfig csvReadConfig = CsvReadConfig.defaultConfig();
            csvReadConfig.setSkipEmptyRows(false);
            CsvReader csvReader = new CsvReader(reader, csvReadConfig);
            CsvData read = csvReader.read();
            int rowIndex = -1;
            for (CsvRow strings : read) {
                rowIndex++;
                if (rowIndex == 0) {
                    continue;
                }
                if (strings.size() < 4) {
                    continue;
                }
                EdgeData data = new EdgeData();
                data.setSource(strings.get(0));
                data.setTarget(strings.get(1));
                data.setMutations(strings.get(2));
                data.setDistance(strings.get(3));
                list.add(data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("csv解析失败：" + file);
        }
        return list;
    }

    public Page<Venas2ResultVO> getVenas2ResultPage(String id, Pageable pageable) {
        Optional<UserTask> byId = userTaskRepository.findById(id);
        if (!byId.isPresent()) {
            throw new ServiceException("task not exists");
        }
        UserTask task = byId.get();
        File folder = new File(venasProperties.getScriptParentPath(), task.getOutPath());
        File parentDataFile = new File(folder, "parent_data.txt");
        List<Venas2ResultBO> bos = parseParentData(parentDataFile);
        List<Venas2ResultBO> bs = CollUtil.page(pageable.getPageNumber(), pageable.getPageSize(), bos);
        List<Venas2ResultVO> page = bs.stream().map(b -> {
            Venas2ResultVO vo = new Venas2ResultVO();
            BeanUtil.copyProperties(b, vo);
            return vo;
        }).collect(Collectors.toList());
        return new PageImpl<>(page, pageable, bos.size());
    }

    private List<Venas2ResultBO> parseParentData(File file) {
        //New_hapid	New_haplotype	New_samples	New_time_min	New_time_max	New_locations	parent_hapid	parent_haplotype	parent_time_min	parent_time_max	parent_locations	parent_samples
        Map<String, Integer> titleMap = new HashMap<>(16);
        List<Venas2ResultBO> list = new ArrayList<>();
        try (Reader reader = FileUtil.getReader(file, Charset.defaultCharset())) {
            CsvReadConfig csvReadConfig = CsvReadConfig.defaultConfig();
            csvReadConfig.setFieldSeparator('\t');
            csvReadConfig.setSkipEmptyRows(false);
            CsvReader csvReader = new CsvReader(reader, csvReadConfig);
            CsvData read = csvReader.read();
            int rowIndex = -1;
            for (CsvRow strings : read) {
                rowIndex++;
                if (rowIndex == 0) {
                    for (int i = 0; i < strings.size(); i++) {
                        titleMap.put(strings.get(i), i);
                    }
                    continue;
                }
                Venas2ResultBO data = new Venas2ResultBO();
                data.setNewHapid(getDataByIndex(strings, titleMap, "New_hapid"));
                data.setNewHaplotype(getDataByIndex(strings, titleMap, "New_haplotype"));
                data.setNewSamples(getDataByIndex(strings, titleMap, "New_samples"));
                data.setNewTimeMin(getDataByIndex(strings, titleMap, "New_time_min"));
                data.setNewTimeMax(getDataByIndex(strings, titleMap, "New_time_max"));
                data.setNewLocations(getDataByIndex(strings, titleMap, "New_locations"));
                data.setParentHapid(getDataByIndex(strings, titleMap, "parent_hapid"));
                data.setParentHaplotype(getDataByIndex(strings, titleMap, "parent_haplotype"));
                data.setParentTimeMin(getDataByIndex(strings, titleMap, "parent_time_min"));
                data.setParentTimeMax(getDataByIndex(strings, titleMap, "parent_time_max"));
                data.setParentLocations(getDataByIndex(strings, titleMap, "parent_locations"));
                data.setParentSamples(getDataByIndex(strings, titleMap, "parent_samples"));
                list.add(data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("csv解析失败：" + file);
        }
        return list;
    }

    private String getDataByIndex(CsvRow strings, Map<String, Integer> titleMap, String name) {
        if (!titleMap.containsKey(name)) {
            return null;
        }
        if (titleMap.get(name) > strings.size() - 1) {
            return null;
        }
        return strings.get(titleMap.get(name));
    }
}
