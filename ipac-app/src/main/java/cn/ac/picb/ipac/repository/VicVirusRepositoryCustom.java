package cn.ac.picb.ipac.repository;

import cn.ac.picb.ipac.model.VicVirus;
import cn.ac.picb.ipac.vo.VicVirusSearch;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 */
public interface VicVirusRepositoryCustom {

    /**
     * 病毒分析列表
     *
     * @param search   search
     * @param pageable pageable
     * @return page
     */
    Page<VicVirus> findByPage(VicVirusSearch search, Pageable pageable);

}
