package cn.ac.picb.ipac.config;

import cn.ac.picb.ipac.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;


@Slf4j
public class CurrentUserInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (handler instanceof HandlerMethod) {
            getCurrentLoginUser().ifPresent(UserContext::setUser);
            return true;
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserContext.removeUser();
    }

    private Optional<User> getCurrentLoginUser() {
        SecurityContext context = SecurityContextHolder.getContext();
        Authentication authentication = context.getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return Optional.empty();
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof CasSecurityConfiguration.SecurityUser) {
            CasSecurityConfiguration.SecurityUser user = (CasSecurityConfiguration.SecurityUser) principal;
            User currentUser = new User();
            currentUser.setId(user.getId());
            currentUser.setName(user.getName());
            currentUser.setAccountName(user.getUsername());
            return Optional.of(currentUser);
        }
        return Optional.empty();
    }
}
