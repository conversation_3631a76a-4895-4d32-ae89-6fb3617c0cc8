package cn.ac.picb.ipac.controller;

import cn.ac.picb.ipac.common.core.Result;
import cn.ac.picb.ipac.common.core.ResultGenerator;
import cn.ac.picb.ipac.dto.Venas1TaskDTO;
import cn.ac.picb.ipac.dto.Venas2TaskDTO;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.mq.ipp.AnalysisTask;
import cn.ac.picb.ipac.mq.ipp.MessageSender;
import cn.ac.picb.ipac.mq.venas.MessageSender1;
import cn.ac.picb.ipac.mq.venas.MessageSender2;
import cn.ac.picb.ipac.mq.venas.Venas1Task;
import cn.ac.picb.ipac.mq.venas.Venas2Task;
import cn.ac.picb.ipac.service.TaskService;
import cn.ac.picb.ipac.service.VenasService;
import cn.ac.picb.ipac.vo.TaskFromVo;
import cn.ac.picb.ipac.vo.venas.GraphData;
import cn.ac.picb.ipac.vo.venas.Venas2ResultVO;
import com.alibaba.fastjson.JSONArray;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


@Controller
@RequestMapping("/analysis")
@AllArgsConstructor
public class AnalysisController extends BaseController {
    private final TaskService taskService;
    private final MessageSender sender;
    private final MessageSender1 sender1;
    private final MessageSender2 sender2;
    private final VenasService venasService;

    @RequestMapping("/addTask")
    @ResponseBody
    public Result addTask(TaskFromVo vo) {
        AnalysisTask msg = taskService.addTask(getUser(), vo);
        sender.sendTaskMessage(msg);
        return ResultGenerator.genSuccessResult();
    }

    @RequestMapping("/venas1/addTask")
    @ResponseBody
    public Result venas1AddTask(Venas1TaskDTO dto) {
        Venas1Task msg = taskService.venas1AddTask(getUser(), dto);
        sender1.sendTaskMessage(msg);
        return ResultGenerator.genSuccessResult();
    }

    @RequestMapping("/venas1/result")
    public String venas1Result(Model model, @ModelAttribute("id") String id) {
        UserTask userTask = taskService.findUserTaskById(id);
        model.addAttribute("task", userTask);
        return "venas/result1";
    }

    /**
     * 进化分布数据venas
     *
     * @param id 任务id
     * @return
     */
    @RequestMapping("/venas/graphData")
    @ResponseBody
    public Result graphData(@ModelAttribute("id") String id) {
        GraphData graphData = venasService.graphData(id, null);
        return ResultGenerator.genSuccessResult(graphData);
    }

    @RequestMapping("/venas/subGraphData")
    @ResponseBody
    public Result subGraphData(String id, String clusterId) {
        GraphData graphData = venasService.graphData(id, clusterId);
        return ResultGenerator.genSuccessResult(graphData);
    }

    @RequestMapping("/venas2/result")
    public String venas2Result(Model model, @ModelAttribute("id") String id, @PageableDefault() Pageable pageable) {
        UserTask userTask = taskService.findUserTaskById(id);
        Page<Venas2ResultVO> page = venasService.getVenas2ResultPage(id, pageable);
        model.addAttribute("page", page);
        model.addAttribute("task", userTask);
        return "venas/result2";
    }

    @RequestMapping("/venas2/addTask")
    @ResponseBody
    public Result venas1AddTask(Venas2TaskDTO dto) {
        Venas2Task msg = taskService.venas2AddTask(getUser(), dto);
        sender2.sendTaskMessage(msg);
        return ResultGenerator.genSuccessResult();
    }

    @RequestMapping("/venas2/downLoadParentData")
    public void downLoadParentData(String id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        UserTask userTask = taskService.findUserTaskById(id);
        taskService.downLoadParentData(userTask, request, response);
    }

    @RequestMapping("/task/search")
    @ResponseBody
    public Result search(String keyword, @PageableDefault(size = 20) Pageable pageable) {
        JSONArray jsonArray = taskService.search(keyword, getUser(), pageable);
        return ResultGenerator.genSuccessResult(jsonArray);
    }

}
