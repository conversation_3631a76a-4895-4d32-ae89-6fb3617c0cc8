package cn.ac.picb.ipac.mq.pits;

import cn.ac.picb.ipac.mq.msg.AnalysisStartMsg;
import cn.ac.picb.ipac.mq.msg.StatusMsg;
import cn.ac.picb.ipac.mq.msg.TaskCreateMsg;
import cn.ac.picb.ipac.mq.msg.TaskFinishedMsg;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PitsMessageSender {

    private final RabbitTemplate rabbitTemplate;

    public void statusChange(StatusMsg msg) {
        rabbitTemplate.convertAndSend("analysis_exchange", "speciesfinder_status_flow_routing_key", msg);
    }

    public void sendTaskCreateMsg(TaskCreateMsg msg) {
        rabbitTemplate.convertAndSend("analysis_exchange", "speciesfinder_task_create_routing_key", msg);
    }

    public void sendAnalysisStartMsg(AnalysisStartMsg msg) {
        log.info("发送分析任务开始消息");
        rabbitTemplate.convertAndSend("analysis_exchange", "speciesfinder_analysis_start_routing_key", msg);
        log.info(JSON.toJSONString(msg));
    }

    public void sendTaskFinishedMsg(TaskFinishedMsg msg) {
        rabbitTemplate.convertAndSend("analysis_exchange", "speciesfinder_task_finished_routing_key", msg);
    }
}
