package cn.ac.picb.ipac.repository;

import cn.ac.picb.ipac.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface UserRepository extends JpaRepository<User, String>, JpaSpecificationExecutor<User> {
    User findFirstByAccountName(String accountName);

    User findFirstByEmail(String email);
}
