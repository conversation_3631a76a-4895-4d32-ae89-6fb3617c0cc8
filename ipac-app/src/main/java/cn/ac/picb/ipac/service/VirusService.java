package cn.ac.picb.ipac.service;

import cn.ac.picb.ipac.common.enums.TaskStatusEnum;
import cn.ac.picb.ipac.common.util.DateUtils;
import cn.ac.picb.ipac.common.util.FileUtils;
import cn.ac.picb.ipac.common.util.NumberUtil;
import cn.ac.picb.ipac.config.Constants;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.model.VicVirus;
import cn.ac.picb.ipac.model.VicVirusVisualization;
import cn.ac.picb.ipac.repository.UserRepository;
import cn.ac.picb.ipac.repository.UserTaskRepository;
import cn.ac.picb.ipac.repository.VicVirusRepository;
import cn.ac.picb.ipac.repository.VicVirusVisualizationRepository;
import cn.ac.picb.ipac.vo.FtpFileVo;
import cn.ac.picb.ipac.vo.VicVirusSearch;
import cn.ac.picb.ipac.vo.VicVirusVo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VirusService {

    private final UserRepository userRepository;
    private final UserTaskRepository userTaskRepository;
    private final VicVirusRepository vicVirusRepository;
    private final VicVirusVisualizationRepository vicVirusVisualizationRepository;
    private final TaskService taskService;

    /**
     * 检出病毒列表
     *
     * @param search   过滤条件
     * @param pageable page
     * @return page
     */
    public Page<VicVirusVo> findVicVirusPage(VicVirusSearch search, Pageable pageable, User user) {
        Page<VicVirus> page = vicVirusRepository.findByPage(search, pageable);
        if (page.isEmpty()) {
            return new PageImpl<>(Collections.emptyList());
        }
        List<VicVirusVo> vos = page.stream().map(vicVirus -> {
            VicVirusVo vo = new VicVirusVo();
            vo.setVirus(vicVirus);

            String taskId = vicVirus.getTaskId();
            userTaskRepository.findByTaskId(taskId).ifPresent(task -> {
/*                List<List<FtpFileVo>> fileVoList = taskService.getFtpFileVoList(task, user);
                vo.setFtpFileVoList(fileVoList);*/
            });

            return vo;
        }).collect(Collectors.toList());
        return new PageImpl<>(vos, page.getPageable(), page.getTotalElements());
    }


    /**
     * 删除jBrowse 配置记录
     *
     * @param id id
     */
    @SneakyThrows
    public void deleteVicVirus(String id) {
        VicVirus virus = vicVirusRepository.findById(id).orElseThrow(() -> new RuntimeException("The record does not exist"));
        vicVirusRepository.delete(virus);
    }

    /**
     * 扫描单个任务
     *
     * @param taskId 任务id
     */
    public void scanTask(String taskId) {
        List<VicVirus> virusList = vicVirusRepository.findAllByDefaultRefIsTrue();
        // 当前任务
        UserTask task = userTaskRepository.findByTaskId(taskId).orElseThrow(() -> new RuntimeException("The analysis task does not exist"));
        // 当前用户
        User user = userRepository.findById(task.getUser().getId()).orElseThrow(() -> new RuntimeException("The user does not exist"));
        // default virus
        for (VicVirus defaultVirus : virusList) {
            // scan file
            scanDirectory(user, task, defaultVirus);
        }
    }

    /**
     * 重新扫描用户指定病毒类型的全部任务
     *
     * @param refAbbr 病毒类型
     * @param userId  用户id
     * @return 扫描结果
     */
    public void scanUserTask(String refAbbr, String userId) {
        // 删除现有记录
        vicVirusRepository.deleteByUserIdAndRefAbbrAndDefaultRefIsFalse(userId, refAbbr);

        List<VicVirus> virusList = vicVirusRepository.findAllByDefaultRefIsTrue();

        List<UserTask> tasks = userTaskRepository.findByUser_IdAndStatus(userId, TaskStatusEnum.complete.getCode());

        User user = userRepository.findById(userId).orElseThrow(() -> new RuntimeException("The user does not exist"));
        for (VicVirus defaultVirus : virusList) {
            for (UserTask task : tasks) {
                scanDirectory(user, task, defaultVirus);
            }
        }
    }

    /**
     * 扫描指定目录，查找是否有病毒分析结果
     * <p>
     * taskId
     * |-- reports
     * |-- results_assembler
     * |-- refs
     * |-- pair1
     * |-- reads_NC_045512.2_kalli_calls.vcf.gz
     * |-- reads_NC_004996.1_kalli_calls.vcf.gz
     * |-- .....
     * |-- pair2
     * |-- ....
     *
     * @param user            用户
     * @param task            task
     * @param defaultVicVirus 病毒
     */
    private void scanDirectory(User user, UserTask task, VicVirus defaultVicVirus) {
        String outPath = task.getOutPath();
        String refAcVersion = defaultVicVirus.getRefAcVersion();
        String refVersion = refAcVersion.substring(0, refAcVersion.lastIndexOf("."));
        String refAbbr = defaultVicVirus.getRefAbbr();

        File assembler = new File(Constants.analysisResultHome + outPath, "results_assembler");

        if (!assembler.exists()) {
            return;
        }

        File[] files = assembler.listFiles();
        if (files == null) {
            return;
        }
        for (File file : files) {
            FileUtils.listFiles(file, new String[]{"vcf.gz"}, false)
                    .stream()
                    .map(File::getName)
                    .filter(name -> name.contains(refAcVersion))
                    .forEach(name -> {
                        VicVirus virus = new VicVirus();

                        virus.setGffFilePath(refVersion);
                        virus.setVcfFilePath(outPath + "/results_assembler/" + file.getName() + "/" + name);
                        // 拦截器自动更新 virus_id
                        String id = DateUtils.dateTimeFormat2(new Date());
                        virus.setVirusId("VIC"+id+ NumberUtil.getThreeRandom());
                        // todo 设置virus_name
                        virus.setVirusName("");
                        virus.setSecurity("private");
                        virus.setRefAbbr(refAbbr);
                        virus.setDefaultRef(false);
                        virus.setRefAcVersion(refAcVersion);
                        virus.setCreateTime(new Date());
                        virus.setUserId(user.getId());
                        virus.setTaskId(task.getTaskId());
                        vicVirusRepository.save(virus);
                    });
        }
    }

    /**
     * 病毒可视化记录列表
     *
     * @param search   过滤条件
     * @param pageable page
     * @return page
     */
    public Page<VicVirusVisualization> findVicVirusVisualizationPage(VicVirusSearch search, Pageable pageable) {
        return vicVirusVisualizationRepository.findByPage(search, pageable);
    }

    /**
     * 生成 JBrowse 可视化数据
     *
     * @param user     当前用户
     * @param refAbbr  病毒类型
     * @param virusIds 记录id
     */
    public VicVirusVisualization createVicVirusVisualization(User user, String refAbbr, String[] virusIds) {
        if (virusIds.length <= 0) {
            throw new RuntimeException("Please select at least one record");
        }
        VicVirus defaultVirus = vicVirusRepository.findByRefAbbrAndDefaultRefIsTrue(refAbbr).orElseThrow(() -> new RuntimeException("virus does not exist"));

        // 新增或者更新可视化的记录
        VicVirusVisualization visualization = vicVirusVisualizationRepository.findByUserIdAndRefAbbr(user.getId(), refAbbr).orElseGet(() -> {
            VicVirusVisualization virusVisualization = new VicVirusVisualization();
            virusVisualization.setRefAbbr(defaultVirus.getRefAbbr());
            virusVisualization.setUserId(user.getId());
            return virusVisualization;
        });

        // jBrowse 配置存放文件路径
        //String trackConfPath = "/" + defaultVirus.getRefAbbr() + "_" + user.getEmail() + "_" + user.getId();

        // jBrowse 配置存放文件路径(RefAbbr,Email,System.currentTime)
        String trackConfPath = "";
        File jBrowseDataDir = new File(Constants.tomcatJBrowseParentPath + "/" + Constants.jBrowseDataDir);
        for(File jBrowseData:jBrowseDataDir.listFiles()){
            if(jBrowseData.isDirectory() && jBrowseData.getName().startsWith(defaultVirus.getRefAbbr() + "_" + user.getEmail())){
                trackConfPath = "/" + jBrowseData.getName();
                break;
            }
        }
        if(StringUtils.isEmpty(trackConfPath))
            trackConfPath = "/" + defaultVirus.getRefAbbr() + "_" + user.getEmail() + "_" + DateUtils.dateTimeFormat(new Date());

        visualization.setUpdateTime(new Date());

        List<VicVirus> virusList = vicVirusRepository.findByIdIn(virusIds);

        Map<String, List<String>> map = new HashMap<>();
        for (VicVirus virus : virusList) {
            String taskId = virus.getTaskId();

            List<String> paths = map.getOrDefault(taskId, new ArrayList<>());
            paths.add(virus.getVcfFilePath());
            map.put(taskId, paths);
        }

        String refAcVersion = defaultVirus.getRefAcVersion();
        String refVersion = refAcVersion.substring(0, refAcVersion.lastIndexOf("."));

        // 重新生成 tracks.conf 配置文件
        String tracks = createTracksConf(refVersion, refAcVersion,trackConfPath, map);


        visualization.setTrackConfPath(trackConfPath);

        String url = Constants.jBrowseBaseUrl + "/?data=" + Constants.jBrowseParentPath + "/" + Constants.jBrowseDataDir + trackConfPath+ "&menu=false&tracklist=0&loc="+refAcVersion+":1..30000&tracks=DNA,"+refVersion+"_gff&cat=VCF";
        visualization.setUrl(url);
        vicVirusVisualizationRepository.save(visualization);
        return visualization;
    }

    /**
     * 重新生成 tracks.conf 配置文件, 移动 NC_045512 下的 seq 文件夹至 tracks.conf 存放路径
     *
     * @param refVersion    "ref_ac_version" : "NC_045512"
     * @param trackConfPath jBrowse 配置保存根路径
     * @param map           key为任务编号，value为 vcf配置文件相对路径列表
     */
    private String createTracksConf(String refVersion, String refAcVersion,String trackConfPath, Map<String, List<String>> map) {
        /**
         * 该路径下目录结构为
         * NC_045512
         * |- NC_045512.gff
         * |- seq文件夹
         */
        StringBuffer tracks = new StringBuffer();
        tracks.append("DNA,");
        String jbrowseGffRoot = Constants.jBrowseParentPath + "/" + Constants.jBrowseRefDir + "/" + refVersion;

        // 重新生成 tracks.conf 配置文件
        List<String> conf = new ArrayList<>();
        conf.add("[ tracks . " + refVersion + "_gff ]");
        conf.add("storeClass = JBrowse/Store/SeqFeature/GFF3");
        conf.add("urlTemplate = " + jbrowseGffRoot + "/" + refAcVersion + ".gff");
        conf.add("type = CanvasFeatures");
        conf.add("metadata.description = This is just all the features in the " + refAcVersion + ".gff tracksConf, display ed directly from a web-accessible GFF3 tracksConf");
        conf.add("category = GFF");
        conf.add("key = GFF - " + refAcVersion);
        tracks.append(refVersion + "_gff,");


        map.forEach((taskId, vcfFilePaths) -> {
            for (int i = 0; i < vcfFilePaths.size(); i++) {
                String name = taskId + "_" + (i + 1) + "_" + refVersion + "_VCF";
                String urlTemplate = Constants.jBrowseParentPath + "/" + Constants.jBrowseVcfDir + vcfFilePaths.get(i);

                conf.add("[ tracks." + name + " ]");
                conf.add("storeClass = JBrowse/Store/SeqFeature/VCFTabix");
                conf.add("urlTemplate = " + urlTemplate);
                conf.add("category = VCF");
                conf.add("type = JBrowse/View/Track/CanvasVariants");
                conf.add("key = " + name + "");
                tracks.append(name+",");
            }
        });
        File tracksConf = new File(Constants.tomcatJBrowseParentPath + "/" + Constants.jBrowseDataDir + trackConfPath, "tracks.conf");
        if (!tracksConf.getParentFile().exists()) {
            tracksConf.getParentFile().mkdirs();
        }
        try {
            FileUtils.writeLines(tracksConf, conf, false);
        } catch (IOException e) {
            log.error("生成配置文件失败:", e);
            throw new RuntimeException("Failed to generate JBrowse configuration file");
        }

        String gffRoot = Constants.tomcatJBrowseParentPath + "/" + Constants.jBrowseRefDir + "/" + refVersion;
        File seq = new File(gffRoot, "seq");
        if (!seq.exists()) {
            throw new RuntimeException("seq The file path is incorrect, and the current path is：" + seq.getAbsolutePath());
        }
        try {
            FileUtils.copyDirectory(seq, new File(tracksConf.getParent() + "/seq"));
        } catch (IOException e) {
            log.error("复制 seq 文件夹出错！", e);
            throw new RuntimeException("There was an error copying the seq folder");
        }
        File trackList = new File(gffRoot, "trackList.json");
        if (!trackList.exists()) {
            throw new RuntimeException("trackList.json The file path is incorrect, and the current path is：" + trackList.getAbsolutePath());
        }
        try {
            FileUtils.copyFile(trackList, new File(tracksConf.getParent(), trackList.getName()));
        } catch (IOException e) {
            log.error("复制 trackList.json 文件出错！", e);
            throw new RuntimeException("Copy trackList.json file error");
        }

        return tracks.substring(0,tracks.length()-1);
    }

    /**
     * 删除jBrowse 配置记录
     *
     * @param id id
     */
    @SneakyThrows
    public void deleteVicVirusVisualization(String id) {
        VicVirusVisualization visualization = vicVirusVisualizationRepository.findById(id).orElseThrow(() -> new RuntimeException("The record does not exist"));

        String confPath = visualization.getTrackConfPath();
        File file = new File(Constants.tomcatJBrowseParentPath + "/" + Constants.jBrowseDataDir, confPath);
        FileUtils.deleteDirectory(file);

        vicVirusVisualizationRepository.delete(visualization);
    }

}
