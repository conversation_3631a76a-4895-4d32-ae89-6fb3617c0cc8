package cn.ac.picb.ipac.repository.impl;

import cn.ac.picb.ipac.model.VicVirusVisualization;
import cn.ac.picb.ipac.vo.VicVirusSearch;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import cn.ac.picb.ipac.repository.VicVirusVisualizationRepositoryCustom;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class VicVirusVisualizationRepositoryImpl implements VicVirusVisualizationRepositoryCustom {

    private final MongoTemplate mongoTemplate;

    /**
     * 病毒分析可视化列表
     *
     * @param search   search
     * @param pageable pageable
     * @return page
     */
    @Override
    public Page<VicVirusVisualization> findByPage(VicVirusSearch search, Pageable pageable) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(search.getUserId())) {
            criteria.and("user_id").is(search.getUserId());
        }
        if (search.getStart() != null && search.getEnd() != null) {
            criteria.and("update_time").gte(search.getStart()).lte(search.getEnd());
        } else if (search.getStart() != null) {
            criteria.and("update_time").gte(search.getStart());
        } else if (search.getEnd() != null) {
            criteria.and("update_time").lte(search.getStart());
        }

        query.addCriteria(criteria);
        query.with(Sort.by(Sort.Order.desc("update_time")));

        long count = mongoTemplate.count(query, VicVirusVisualization.class);
        List<VicVirusVisualization> content = mongoTemplate.find(query.with(pageable), VicVirusVisualization.class);
        return new PageImpl<>(content, pageable, count);
    }
}
