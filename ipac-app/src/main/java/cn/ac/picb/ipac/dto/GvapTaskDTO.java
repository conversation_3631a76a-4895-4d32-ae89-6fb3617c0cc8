package cn.ac.picb.ipac.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class GvapTaskDTO {
    @NotNull
    private GvapRow refFile;

    @NotEmpty
    private List<GvapRow> inputFiles;

    @Data
    public static class GvapRow {
        @NotBlank
        private String sampleName;

        @NotNull
        private FtpFileDTO file;
    }
}
