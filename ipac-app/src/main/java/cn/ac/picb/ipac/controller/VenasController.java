package cn.ac.picb.ipac.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequiredArgsConstructor
@RequestMapping("/venas")
public class VenasController {

    @RequestMapping("/main1")
    public String main1(Model model) {
        return "venas/main1";
    }

    @RequestMapping("/main2")
    public String main2(Model model) {
        return "venas/main2";
    }

}
