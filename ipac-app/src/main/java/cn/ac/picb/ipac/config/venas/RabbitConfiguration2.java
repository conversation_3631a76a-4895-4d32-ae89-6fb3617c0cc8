package cn.ac.picb.ipac.config.venas;

import org.springframework.amqp.core.*;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfiguration2 {

    private final MqProperties2 mqProperties;

    public RabbitConfiguration2(MqProperties2 mqProperties) {
        this.mqProperties = mqProperties;
    }

    @Bean
    public DirectExchange venas2DirectExchange() {
        return ExchangeBuilder
                .directExchange(mqProperties.getExchange().getName())
                .ignoreDeclarationExceptions()
                .build();
    }

    /**
     * web创建分析队列
     *
     * @return queue
     */
    @Bean
    public Queue venas2TaskQueue() {
        return new Queue(mqProperties.getTask().getQueueName());
    }


    @Bean
    public Binding venas2BindingTaskQueue(DirectExchange venas2DirectExchange, Queue venas2TaskQueue) {
        return BindingBuilder
                .bind(venas2TaskQueue)
                .to(venas2DirectExchange)
                .with(mqProperties.getTask().getRoutingKey());
    }
}
