package cn.ac.picb.ipac.service;


import cn.ac.picb.ipac.common.core.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

@Service
@Slf4j
@EnableAsync
public class MailService {
/*
    private final JavaMailSender mailSender;

    private final Environment environment;

    public MailService(JavaMailSender mailSender, Environment environment) {
        this.mailSender = mailSender;
        this.environment = environment;
    }*/

    public void sendMail(String to, String subject, String content) {
      /*  SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(environment.getProperty("spring.mail.username"));
        message.setTo(to);
        message.setSubject(subject);
        message.setText(content);
        try {
            mailSender.send(message);
            log.info("The email has been sent");
        } catch (Exception e) {
            log.error("An exception occurred while sending an email！", e);
        }*/
    }

    @Async
    public void sendHtmlMail(String to, String subject, String htmlContent) {
     /*   final MimeMessage mimeMessage = mailSender.createMimeMessage();
        final MimeMessageHelper message = new MimeMessageHelper(mimeMessage, "UTF-8");
        try {
            message.setSubject(subject);
            message.setFrom(environment.getProperty("spring.mail.username"));
            message.setTo(to);
            message.setText(htmlContent, true);
        } catch (MessagingException e) {
            log.error("An exception occurred while sending an email!");
            e.printStackTrace();
            throw new ServiceException("An exception occurred while sending an email!");
        }
        mailSender.send(mimeMessage);*/
    }

}


