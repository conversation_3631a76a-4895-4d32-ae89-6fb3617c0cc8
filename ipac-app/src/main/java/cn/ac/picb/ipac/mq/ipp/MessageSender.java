package cn.ac.picb.ipac.mq.ipp;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import cn.ac.picb.ipac.config.ipp.MqProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MessageSender {

    private final RabbitTemplate rabbitTemplate;
    private final MqProperties mqProperties;

    final RabbitTemplate.ConfirmCallback confirmCallback = (correlationData, ack, cause) -> {
        log.debug("correlationData: {}", correlationData);
        log.debug("ack: {}", ack);
        if (!ack) {
            log.error("未确认。。。。。。异常处理");
        } else {
            log.info("投递成功");
        }
    };

    final RabbitTemplate.ReturnCallback returnCallback = (message, replyCode, replyText, exchange, routingKey) -> log.debug("exchange: {} ---- routingKey: {} ---- replyCode: {} ----replyText: {}", exchange, routingKey, replyCode, replyText);

    /**
     * 任务创建发送 任务id以及用户选择文件列表至队列
     *
     * @param msg task
     */
    public void sendTaskMessage(AnalysisTask msg) {
        rabbitTemplate.setConfirmCallback(confirmCallback);
        rabbitTemplate.setReturnCallback(returnCallback);

        CorrelationData correlationData = new CorrelationData(msg.getTaskId() + System.currentTimeMillis());
        rabbitTemplate.convertAndSend(mqProperties.getExchange().getName(), mqProperties.getTask().getRoutingKey(), msg, correlationData);
    }

}
