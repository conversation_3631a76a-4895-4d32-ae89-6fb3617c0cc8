package cn.ac.picb.ipac.controller;


import cn.ac.picb.file.vo.TreeNode;
import cn.ac.picb.ipac.common.core.Result;
import cn.ac.picb.ipac.common.core.ResultGenerator;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.service.FtpService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequiredArgsConstructor
@RequestMapping("/file")
public class FileController extends BaseController {
    private final FtpService ftpService;

    @RequestMapping("/fileTree")
    public String fileTree(Model model, String parentPath) {
        User user = getUser();
        List<TreeNode> rootNodeList = ftpService.findTreeNode(user, parentPath);
        model.addAttribute("rootNodeList", rootNodeList);
        return "file/fileTree";
    }

    @RequestMapping("/fileTreeAjax")
    @ResponseBody
    public List<TreeNode> fileTreeAjax( String parentPath) {
        User user = getUser();
        return ftpService.findTreeNode(user, parentPath);

    }

    @RequestMapping("/fileTreeNodes")
    @ResponseBody
    public Result fileTreeNodes(String parentPath) {
        //file-service接口有问题 windows返回的全路径，linux返回相对路径，路径分隔符斜杠反斜杠引起的问题，
        // cn.ac.picb.file.service.fileService源代码需要修改为 FileUtil.subPath(basePath, absolutePath)
        parentPath = parentPath.replace("C:\\ftp_test\\wjh","");
        User user = getUser();
        List<TreeNode> rootNodeList = ftpService.findTreeNode(user, parentPath);
        return ResultGenerator.genSuccessResult(rootNodeList);
    }

    /**
     * FTP已上传数据自动配对
     *
     * @return
     */
    @RequestMapping("/matchFileGroup")
    @ResponseBody
    public Result matchFileGroup(String[] folderId) {
        //todo
/*        JSONArray jsonArray = analysisService.matchFileGroup(getUser(), folderId);
        return ResultGenerator.genSuccessResult(jsonArray);*/
        return null;
    }
}
