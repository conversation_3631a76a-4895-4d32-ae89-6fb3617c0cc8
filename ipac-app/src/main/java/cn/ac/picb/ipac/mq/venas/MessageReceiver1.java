package cn.ac.picb.ipac.mq.venas;

import cn.ac.picb.ipac.common.core.ServiceException;
import cn.ac.picb.ipac.common.enums.venas.VenasTaskStatusEnum;
import cn.ac.picb.ipac.config.venas.VenasProperties;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.repository.UserTaskRepository;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

@Component
@Slf4j
@AllArgsConstructor
public class MessageReceiver1 {
    private final UserTaskRepository userTaskRepository;
    private VenasProperties venasProperties;

    @RabbitListener(queues = "${venas1.rabbitmq.task.queueName}")
    @RabbitHandler
    @SneakyThrows
    public void receiveResult1Message(@Payload Venas1Task msg, Channel channel, @Headers Map<String, Object> headers) {
        log.info("venas1消费端Payload: " + msg);
        UserTask userTask = userTaskRepository.findByTaskId(msg.getTaskId()).get();
        if (!ObjectUtil.equal(userTask.getStatus(), VenasTaskStatusEnum.draft.getCode())) {
            return;
        }
        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        upStatus(userTask, VenasTaskStatusEnum.analyzing, null);
        try {
            analysis(userTask);
            upStatus(userTask, VenasTaskStatusEnum.complete, null);
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            e.printStackTrace();
            upStatus(userTask, VenasTaskStatusEnum.error, e.getMessage());
            channel.basicReject(deliveryTag, false);
        }
    }

    private void analysis(UserTask userTask) throws Exception {
        String workingDirectory = venasProperties.getScriptParentPath();
        String pythonScript = "scripts/venas2.py";
        if ("M.tuberculosis".equals(userTask.getAttr1())) {
            pythonScript = "scripts/venas2_mtb.py";
        }
        String[] command = {
                "python3", pythonScript, "-input_name", userTask.getTaskId()
        };
        if (StrUtil.isNotBlank(userTask.getAttr2())) {
            command = new String[]{
                    "python3", pythonScript,
                    "-input_name", userTask.getTaskId(),
                    "-batch_mode", "True",
                    "-initial_size", userTask.getAttr3(),
                    "-batch_size", userTask.getAttr4()
            };
        }
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        log.info("venas1 script: {}", JSONObject.toJSONString(command));
        processBuilder.directory(new File(workingDirectory));
        try {
            Process process = processBuilder.start();
            // 读取标准输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("venas1 taskId:{}, python log: :", userTask.getTaskId(), line);
            }
            int exitCode = process.waitFor();
            log.info("Exited with result code : " + exitCode);
            if (exitCode != 0) {
                throw new ServiceException("分析失败！");
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
            throw e;
        }
    }

    private void upStatus(UserTask userTask, VenasTaskStatusEnum statusEnum, String errMsg) {
        userTask.setStatus(statusEnum.getCode());
        userTask.setErrMsg(errMsg);
        userTaskRepository.save(userTask);
    }
}
