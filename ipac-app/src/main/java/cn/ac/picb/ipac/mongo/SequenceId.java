package cn.ac.picb.ipac.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "index_counter")
@Data
public class SequenceId {

    @Id
    private String id;
    @Field("value")
    private long value;
    @Field("index_name")
    private String indexName;
}
