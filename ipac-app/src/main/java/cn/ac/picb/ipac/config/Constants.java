package cn.ac.picb.ipac.config;

import cn.ac.picb.ipac.common.enums.DirectoryEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;


/**
 * <AUTHOR>
 */
@Component
public class Constants {

    public static final String jBrowseDataDir = "tracks";
    public static final String jBrowseRefDir = "refseq";
    public static final String jBrowseVcfDir = "analysis";
    public static final String BASE_URL = "baseUrl";
    public static final String HTML_TITLE = "htmlTitle";

    public static String appName;
    public static String dataHome;
    public static String virtualDirName;
    public static String baseUrl;
    public static String secretKey;

    public static String jBrowseBaseUrl;

    //------------------ tomat 容器里面jbrowse配置文件的路径，包括data，refseq目录
    public static String tomcatJBrowseParentPath;
    //------------------ jBrowse 容器里面的路径
    public static String jBrowseParentPath;

    public static String registerEmail;
    /**
     * 分析结果存放根路径
     * <p>
     * 分析任务结果绝对路径为 analysisResultHome/{taskId}
     */
    public static String analysisResultHome;

    @Value("${app.tomcatJBrowseParentPath:/data/test/jbrowse}")
    public void setTomcatJBrowseParentPath(String tomcatJBrowseParentPath) { Constants.tomcatJBrowseParentPath = tomcatJBrowseParentPath; }

    @Value("${app.jBrowseParentPath: /data/test/jbrowse}")
    public void setjBrowseParentPath(String jBrowseParentPath) { Constants.jBrowseParentPath = jBrowseParentPath; }

    @Value("${app.jBrowseBaseUrl:http://**************:8080}")
    public void setjBrowseBaseUrl(String jBrowseBaseUrl) { Constants.jBrowseBaseUrl = jBrowseBaseUrl; }

    @Value("${app.analysisResultHome}")
    public void setAnalysisResultHome(String analysisResultHome) {
        Constants.analysisResultHome = analysisResultHome;
    }

    @Value("${baseUrl:http://localhost:8084}")
    public void setBaseUrl(String baseUrl) {
        Constants.baseUrl = baseUrl;
    }

    @Value("${app.appName:ipac}")
    public void setAppName(String appName) {
        Constants.appName = appName;
    }

    @Value("${app.dataHome}")
    public void setDataHome(String dataHome) {
        Constants.dataHome = dataHome;
    }

    @Value("${app.virtualDir:/ipac/userfiles}")
    public void setVirtualDirName(String virtualDirName) {
        Constants.virtualDirName = virtualDirName;
    }

    @Value("${app.secretKey:ncov-ftp-aes-key}")
    public void setSecretKey(String secretKey) {
        Constants.secretKey = secretKey;
    }

    @Value("${app.registerEmail:}")
    public void setRegisterEmail(String registerEmail) {
        Constants.registerEmail = registerEmail;
    }

    public static File getDirHome(DirectoryEnum dir) {
        File home = new File(Constants.dataHome, dir.name());
        if (!home.exists()) {
            home.mkdir();
        }
        return home;
    }

}
