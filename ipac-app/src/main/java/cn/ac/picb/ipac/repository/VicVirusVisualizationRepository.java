package cn.ac.picb.ipac.repository;

import cn.ac.picb.ipac.model.VicVirusVisualization;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface VicVirusVisualizationRepository extends MongoRepository<VicVirusVisualization, String>,
        VicVirusVisualizationRepositoryCustom {

    /**
     * 查找用户该病毒的可视化记录
     *
     * @param userId  用户id
     * @param refAbbr 病毒
     * @return 可视化记录
     */
    Optional<VicVirusVisualization> findByUserIdAndRefAbbr(String userId, String refAbbr);
}
