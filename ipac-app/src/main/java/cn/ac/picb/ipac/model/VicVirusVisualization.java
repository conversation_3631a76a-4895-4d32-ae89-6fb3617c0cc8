package cn.ac.picb.ipac.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Document(collection = "vic_variation_visualization")
@Data
public class VicVirusVisualization {

    @Id
    private String id;

    @Field(name = "user_id")
    private String userId;

    @Field(name = "ref_abbr")
    private String refAbbr;

    @Field(name = "track_conf_path")
    private String trackConfPath;

    private String url;

    @Field(name = "update_time")
    private Date updateTime;

}
