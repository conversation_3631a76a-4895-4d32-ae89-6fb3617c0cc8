<!doctype html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" >
    <th:block th:include="this :: treeNode(${rootNodeList}, '')"></th:block>
    <th:block th:fragment="treeNode(treeNodeList, parentKey)">
        <th:block th:if="${!#lists.isEmpty(treeNodeList)}" th:each="treeNode,stat : ${treeNodeList}">
            <tr th:data-tt-id='${treeNode.key}' th:data-tt-parent-id="${parentKey}" th:file_path="${treeNode.data.path}" th:data-tt-branch="${treeNode.folder}">
                <th:block th:if="${!treeNode.folder}">
                    <td>
                        <input type="checkbox" name="custom1" th:value="${treeNode.title}" th:file_id="${treeNode.data.path}" class="form-check-input position-static m-r-5" >
                        <span class='file'>[[${treeNode.title}]]</span>
                    </td>
                    <td>[[${treeNode.data.size}]]</td>
                    <td th:text="${#dates.format(treeNode.data.time, 'yyyy-MM-dd HH:mm:ss')}"></td>
                </th:block>
                <th:block th:if="${treeNode.folder}">
                    <td><span class='folder'>[[${treeNode.title}]]</span></td>
                    <td></td>
                    <td>--</td>
                </th:block>
            </tr>
            <th:block th:if="${!#lists.isEmpty(treeNode.children)}">
               <!-- 接口不返回children数据，点击展开逐级加载-->
              <!--  <th:block th:include="this :: treeNode(${treeNode.children}, ${treeNode.key})"></th:block>-->
            </th:block>
        </th:block>
    </th:block>
</html>
