<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
    <th:block layout:fragment="custom-css">
        <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    </th:block>
    <div class="wrapper py-0" layout:fragment="content">

        <div class="wrapper-page">

            <div class="card overflow-hidden account-card mx-3">

                <div class="bg-login p-4 text-white text-center position-relative">
                    <h4 class="font-20 m-b-5">Reset Password</h4>
                </div>
                <div class="account-card-content">
                    <div class="alert alert-primary m-t-20" role="alert">
                        Please enter your Email and a reset link will be sent to you!
                    </div>
                    <form class="form-horizontal m-t-10"  id="dataForm" onsubmit="sendEmail();return false;">

                        <div class="form-group">
                            <label for="useremail">Email</label>
                            <input type="email" class="form-control validate[required,custom[email]]" id="useremail" name="useremail" placeholder="Enter email">
                        </div>


                        <div class="form-group row m-t-20">
                            <div class="col-sm-12 text-right">
                                <button class="btn btn-primary w-md waves-effect waves-light" type="submit">Reset password</button>
                            </div>
                        </div>


                    </form>
                </div>
            </div>
        </div>

    </div>
</html>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-EN.js}"></script>
    <script>

        function sendEmail() {
            if (!$("#dataForm").validationEngine('validate')){
                return;
            }
            $.ajax({
                url : "/forgetPwd2",
                data: $("#dataForm").serialize(),
                type: "post",
                dataType: 'json',
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg("Sending successful");
                        setTimeout(function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            location.href = _context_path + "/login";
                        },2000);
                    } else {
                        layer.alert(result.message, {icon: 2});
                    }
                }
            });
        }
    </script>
</th:block>
