<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="_context_path" th:content="${#httpServletRequest.getContextPath()}"/>
        <title>病原微生物综合分析云平台</title>
        <link th:href="@{/css/bootstrap.css}" rel="stylesheet">
        <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet">
        <link th:href="@{/css/style.css}" rel="stylesheet">

        <link rel="shortcut icon" th:href="@{/images/ico.png}" type="image/x-icon"/>
    </head>
    <body data-spy="scroll" data-target="#scrollspy-1">
        <div class="page-wrapper">
            <div class="hidden" id="fomrDiv">
            </div>
        </div>
        <div class="data-export">
            <div class="export-container">
                <i class="fa fa-file-zip-o fa-3x text-primary"></i>
                <div class="export-info">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="m-t-0">数据正在导出中，请稍后。</h5>
                        <span class="m-b-10" id='timer'>00:00:00</span>
                    </div>
                    <div class="progress">
                        <div id="progress-bar" class="progress-bar progress-bar-striped active" role="progressbar"
                             aria-valuenow="2"
                             aria-valuemin="0" aria-valuemax="100" style="min-width: 0.2em; width: 0%;"></div>
                    </div>
                </div>
            </div>

        </div>
        <script th:src="@{/js/jquery-1.11.3.min.js}"></script>
        <script th:src="@{/js/layer/layer.js}"></script>
    </body>
    <script>
        var ele_timer = document.getElementById("timer");
        var n_sec = 0; //秒
        var n_min = 0; //分
        var n_hour = 0; //时

        function timer() {
            return setInterval(function () {

                var str_sec = n_sec;
                var str_min = n_min;
                var str_hour = n_hour;
                if (n_sec < 10) {
                    str_sec = "0" + n_sec;
                }
                if (n_min < 10) {
                    str_min = "0" + n_min;
                }

                if (n_hour < 10) {
                    str_hour = "0" + n_hour;
                }

                var time = str_hour + ":" + str_min + ":" + str_sec;
                ele_timer.innerHTML = time;
                n_sec++;
                if (n_sec > 59) {
                    n_sec = 0;
                    n_min++;
                }
                if (n_min > 59) {
                    n_sec = 0;
                    n_hour++;
                }

            }, 1000);
        }

        timer();
    </script>
    <script>

        /**
         * 第一种方式：根据查询条件下载
         * @param $formClone 查询条件
         * @param action 请求必须返回导出任务id
         * @param option 配置参数 {@link downloader.oSettings}
         *
         */
        function init($formClone, action, option) {
            $.extend(downloader.oSettings, option);
            if (downloader.oSettings.useDefaultFreshProgressBar) {
                defaultFreshProgressBar();
            }
            $("#fomrDiv").append($formClone);
            $("form").attr("action", action);
            var taskId = downloader.getTaskId();
            downloader.start(taskId);
        }

        /**
         * 第二种方式：根据导出任务id下载
         * @param taskId
         */
        function init2(taskId, option) {
            $.extend(downloader.oSettings, option);
            if (downloader.oSettings.useDefaultFreshProgressBar) {
                defaultFreshProgressBar();
            }
            downloader.start(taskId);
        }

        /**
         * 进度条默认效果
         */
        function defaultFreshProgressBar() {
            $.each(downloader.oSettings.defaultFreshProgressBarEffect, function (i, item) {
                setTimeout(function () {
                    downloader.doFreshProgressBar(item.percentage);
                }, item.time);
            })
        }

        var percentageNow = 0.0;
        var downloader = {
            oSettings: {
                /**
                 * 是否使用默认刷新的进度条，
                 */
                useDefaultFreshProgressBar: true,
                /**
                 * ajax轮询查询进度频率，默认3秒查询一次
                 */
                ajaxPollingTime: 3000,

                /**
                 * 默认进度条动画效果
                 */
                defaultFreshProgressBarEffect: [
                    {percentage: 5, time: 1000},
                    {percentage: 15, time: 5000},
                    {percentage: 25, time: 12000},
                    {percentage: 30, time: 20000},
                    {percentage: 35, time: 30000},
                    {percentage: 45, time: 45000},
                    {percentage: 50, time: 70000},
                    {percentage: 60, time: 100000}]
            },
            start: function (taskId) {
                var filePath = null;
                var si = setInterval(function () {
                    filePath = downloader._freshProgressBar(taskId);
                    if (filePath != null) {
                        //延迟下载，等进度条动画100%
                        setTimeout(function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            window.opener.location.href = _context_path + "/download/" + filePath;
                            window.close();
                            clearInterval(si);
                        }, downloader.oSettings.ajaxPollingTime + 1000);
                    }
                }, downloader.oSettings.ajaxPollingTime);
            },
            _freshProgressBar: function (taskId) {
                var asePath = null;
                var _context_path = $("meta[name='_context_path']").attr("content");
                $.ajax({
                    url: _context_path + "/export/task/findById",
                    data: {"id": taskId},
                    type: 'get',
                    dataType: 'json',
                    async: false,
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        console.error(XMLHttpRequest);
                        console.error(textStatus);
                        console.error(errorThrown);
                        alert("下载异常！请联系管理员");
                        window.close();
                    },
                    success: function (result) {
                        if (result.code == 200) {
                            var taskInfo = result.data;
                            if (taskInfo.status == 'success') {
                                asePath = taskInfo.aesPath;
                                downloader.doFreshProgressBar(100.0);
                            } else if (taskInfo.status == 'error') {
                                console.error(taskInfo.errorMsg);
                                layer.alert("下载异常！请联系管理员");
                                window.close();
                            } else {
                                downloader.doFreshProgressBar(100.0 * (taskInfo.percentage > 1.0 ? 1.0 : taskInfo.percentage));
                            }
                        } else {
                            layer.alert("下载异常！请联系管理员");
                            window.close();
                        }
                    }
                })
                return asePath;
            },
            doFreshProgressBar: function (percentageTarget) {
                var option = {
                    //动画刷新频率 毫秒， 默认0.1s
                    frequency: 100
                }
                if (percentageTarget - percentageNow < 0) {
                    return
                }
                var step = (percentageTarget - percentageNow) / 20.0;
                var progressBarTimer = setInterval(function () {
                    if ((percentageNow + step) > percentageTarget) {
                        clearInterval(progressBarTimer);
                        return;
                    }
                    percentageNow += step;
                    if (percentageNow >= 100) {
                        $("#progress-bar").css("width", "100%");
                        clearInterval(progressBarTimer);
                        return;
                    }
                    $("#progress-bar").css("width", percentageNow + "%");
                }, option.frequency);
            },
            getTaskId: function () {
                if ($("form").length == 0) {
                    return null;
                }
                var taskId = null;
                var formData = new FormData($("form")[0]);
                $.ajax({
                    url: $("form").attr("action"),
                    data: formData,
                    type: 'post',
                    dataType: 'json',
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        console.error(XMLHttpRequest);
                        console.error(textStatus);
                        console.error(errorThrown);
                        layer.alert("下载异常！请联系管理员");
                        window.close();
                    },
                    success: function (result) {
                        if (result.code == 200) {
                            taskId = result.data;
                        } else {
                            layer.alert(result.message);
                            window.close();
                        }
                    }
                })
                return taskId;
            }
        };
    </script>
</html>
