<!DOCTYPE html>
<header th:fragment="header" xmlns:th="http://www.thymeleaf.org"
        xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

    <div class="container-fluid">
        <nav class="navbar navbar-expand-lg">
            <a class="navbar-brand" th:href="@{/home}" href="index.html"><img th:src="@{/images/logo-ipac.png}" src="assets/images/logo-ipac.png" alt=""/></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent">
                <span class="fa fa-bars"></span>
            </button>
            <div class="collapse navbar-collapse active" id="navbarSupportedContent">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/home}" href="index.html">HOME</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/ipp/main}" href="index-ipp.html">IPP</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link"  th:href="@{/venas/main1}"  href="javascript:void(0);">VENAS</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/pits/main}" href="javascript:void(0);">PITS</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/gvap/main}" href="javascript:void(0);">GVAP</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="javascript:void(0);">HELP</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="javascript:void(0);">CONTACT US</a>
                    </li>
                    <th:block   th:if="${session?.SPRING_SECURITY_CONTEXT?.authentication?.principal eq null}">
                        <li class="nav-item">
                            <div class="d-flex align-items-center justify-content-center py-1">
                                <a href="register.html" th:href="@{${@webConstant.REGIST_URL}}"  class="btn btn-outline-primary waves-effect waves-light btn-sm mx-2" >SIGN UP</a>
                                <a id="loginA" href="login.html"   th:href="@{${@webConstant.CAS_BASE_URL + '/login?service=' + @constants.baseUrl +'/' + @constants.appName +'/home' }}"  class="btn btn-outline-primary waves-effect waves-light btn-sm ml-2">SIGN IN</a>
                            </div>
                        </li>
                    </th:block>
                    <th:block  th:unless="${session?.SPRING_SECURITY_CONTEXT?.authentication?.principal eq null}">
                        <li class="dropdown notification-list list-inline-item ml-4">
                            <div class="dropdown notification-list pt-2">
                                <a class="dropdown-toggle nav-user text-primary" data-toggle="dropdown" href="javascript:void(0);" aria-haspopup="false" aria-expanded="false">
                                    <span id="username" th:username="${session?.SPRING_SECURITY_CONTEXT?.authentication?.principal?.username}" th:text="${session?.SPRING_SECURITY_CONTEXT?.authentication?.principal?.username}">
                                    </span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right profile-dropdown ">
                                    <a class="dropdown-item" href="index-my-data.html" th:href="@{/usercenter/files}"><i class="mdi mdi-database m-r-5"></i> My data</a>
                                    <a class="dropdown-item" href="index-my-tasks.html" th:href="@{/usercenter/tasks}"><i class="mdi mdi-file-document-box-outline m-r-5"></i> My task</a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item text-danger" th:href="@{/cas/logout}" href="index.html"><i class="mdi mdi-power text-danger"></i> Logout</a>
                                </div>
                            </div>
                        </li>
                    </th:block>
                </ul>
            </div>
        </nav>
    </div>
    <script>
        $(function () {
            var a = $("ul.navbar-nav a.nav-link").filter(function () {
                var href = window.location.href;
                var a=href.split("/");
                return this.href.indexOf("/" + a[3] + "/" + a[4]) !== -1;
            });
            if(a.length > 1){
                $(".nav-item").eq(0).addClass("active")
                return;
            }
            a.parents(".nav-item").addClass("active");
            if(a.is(".btn")){
                a.removeClass("btn-outline-primary");
                a.addClass("btn-primary");
            }
        });
    </script>
</header>
</html>
