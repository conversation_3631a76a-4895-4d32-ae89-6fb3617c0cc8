<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<div class="pt-1" th:unless="${#lists.isEmpty(page.content) }"
     th:object="${page}">

    <ul  class="pagination justify-content-end pagination-sm" data-th-if="${page.totalPages le 7}">
        <!-- Previous -->
        <li class="page-item" data-th-classappend="*{first} ? 'disabled' : ''">
            <a href="javascript:void(0);" class="page-link"
               data-th-classappend="${page.number >0 } ? 'page-link' : ''"
               data-th-attr="data-pageindex=${page.number} - 1"
               aria-label="Previous">
                <span aria-hidden="true">Previous</span>
            </a>
        </li>
        <!-- 迭代生成页码 -->
        <li class="page-item" data-th-each="i : ${#numbers.sequence(1, page.totalPages)}"
            data-th-classappend="${(page.number + 1) eq i} ? 'active' : ''">
            <a class="page-link" data-th-attr="data-pageindex=${i} - 1" href="javascript:void(0);">
                <span data-th-text="${i}"></span>
            </a>
        </li>
        <!-- Next -->
        <li class="page-item" data-th-classappend="*{last} ? 'disabled' : ''">
            <a href="javascript:void(0);" class="page-link"
               data-th-classappend="*{last} ? '' : 'page-link'"
               data-th-attr="data-pageindex=${page.number} + 1"
               aria-label="Next">
                <span aria-hidden="true">Next</span>
            </a>
        </li>
    </ul>
    <!-- 处理页数大于7 的情况 -->
    <ul class="pagination justify-content-end pagination-sm" data-th-if="${page.totalPages gt 7}">
        <!-- Previous -->
        <li class="page-item" data-th-classappend="*{first} ? 'disabled' : ''">
            <a href="javascript:void(0);" class="page-link"
               data-th-classappend="*{first} ? '' : 'page-link'"
               data-th-attr="data-pageindex=${page.number} - 1"
               aria-label="Previous">
                <span aria-hidden="true">Previous</span>
            </a>
        </li>

        <!-- 首页 -->
        <li class="page-item" data-th-classappend="${(page.number + 1) eq 1} ? 'active' : ''">
            <a href="javascript:void(0);" class="page-link" data-th-attr="data-pageindex=0">1</a>
        </li>

        <!-- 当前页面小于等于4 -->
        <li class="page-item" data-th-if="${(page.number + 1) le 4}"
            data-th-each="i : ${#numbers.sequence(2,5)}"
            data-th-classappend="${(page.number + 1) eq i} ? 'active' : ''">
            <a class="page-link" href="javascript:void(0);" data-th-attr="data-pageindex=${i} - 1">
                <span data-th-text="${i}"></span>
            </a>
        </li>

        <li class="page-item disabled" data-th-if="${(page.number + 1) le 4}">
            <a href="javascript:void(0);" class="page-link">
                <span aria-hidden="true">...</span>
            </a>
        </li>

        <!-- 最后一页与当前页面之差，小于等于3 -->
        <li class="page-item disabled" data-th-if="${(page.totalPages-(page.number + 1)) le 3}">
            <a href="javascript:void(0);" class="page-link">
                <span aria-hidden="true">...</span>
            </a>
        </li>
        <li class="page-item" data-th-if="${(page.totalPages-(page.number + 1)) le 3}"
            data-th-each="i : ${#numbers.sequence(page.totalPages-4, page.totalPages-1)}"
            data-th-classappend="${(page.number + 1) eq i} ? 'active' : ''">
            <a class="page-link" href="javascript:void(0);" data-th-attr="data-pageindex=${i} - 1">
                <span data-th-text="${i}"></span>
            </a>
        </li>

        <!-- 最后一页与当前页面之差大于3，且  当前页面大于4-->
        <li class="page-item disabled"
            data-th-if="${((page.number + 1) gt 4) && ((page.totalPages-(page.number + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link">
                <span aria-hidden="true">...</span>
            </a>
        </li>
        <li class="page-item"
            data-th-if="${((page.number + 1) gt 4) && ((page.totalPages-(page.number + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link" data-th-attr="data-pageindex=${page.number} - 1">[[${page.number}]]</a>
        </li>
        <li class="page-item active"
            data-th-if="${((page.number + 1) gt 4) && ((page.totalPages-(page.number + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link" data-th-attr="data-pageindex=${page.number}">[[${page.number
                + 1}]]</a>
        </li>
        <li class="page-item"
            data-th-if="${((page.number + 1) gt 4) && ((page.totalPages-(page.number + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link" data-th-attr="data-pageindex=${page.number} + 1">[[${page.number
                + 2}]]</a>
        </li>

        <li class="page-item disabled"
            data-th-if="${((page.number + 1) gt 4) && ((page.totalPages-(page.number + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link">
                <span aria-hidden="true">...</span>
            </a>
        </li>

        <!-- 最后一页 -->
        <li class="page-item" data-th-classappend="${(page.number + 1) eq page.totalPages} ? 'active' : ''">
            <a href="javascript:void(0);" class="page-link"
               data-th-attr="data-pageindex=${page.totalPages} - 1">[[${page.totalPages}]]</a>
        </li>

        <!-- Next -->
        <li class="page-item" data-th-classappend="*{last} ? 'disabled' : ''">
            <a href="javascript:void(0);" class="page-link"
               data-th-classappend="*{last} ? '' : 'page-link'"
               data-th-attr="data-pageindex=${page.number} + 1"
               aria-label="Next">
                <span aria-hidden="true">Next</span>
            </a>
        </li>
    </ul>
</div>
<script type="text/javascript">
    var size = [[${page.size}]];

    function initPage(size, index) {
        var $form = $("#search-form");
        if (!$form[0]) {
            return;
        }
        var $sizeInput = $form.find("input[name='size']");
        var hiddenSizeDiv = ['<input type="hidden" name="size" value="' + size + '">'];
        if ($sizeInput[0]) {
            $sizeInput.val(size);
        } else {
            $form.append(hiddenSizeDiv.join(''));
        }

        var $pageInput = $form.find("input[name='page']");
        var hiddenPageDiv = ['<input type="hidden" name="page" value="' + index + '">'];
        if ($pageInput[0]) {
            $pageInput.val(index);
        } else {
            $form.append(hiddenPageDiv.join(''));
        }
        return $form;
    }

    $("#change-page-num").on('change', function () {
        var index = parseInt($(this).val());
        if (isNaN(index) || index > [[${page.totalPages}]] || index < 1) {
            $(this).val('');
            return;
        }
        var $form = initPage(size, index-1);
        if ($form) {
            $form.submit();
        }
    });
    $("#change-page-size").on('change', function () {
        var size = $(this).val();
        var $form = initPage(size, 0);
        $form.submit();
    });
    $("a.page-link").on('click', function () {
        var index = $(this).data("pageindex");
        var $form = initPage(size, index);
        if ($form) {
            $form.submit();
        }
    });
</script>
</html>
