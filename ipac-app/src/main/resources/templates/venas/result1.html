<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
    <th:block layout:fragment="custom-css">
        <style>
            .ui-autocomplete {
                max-height: 200px; /* 设置最大高度 */
                overflow-y: auto; /* 启用垂直滚动条 */
                overflow-x: hidden; /* 防止水平滚动条出现（如果需要的话）*/
                z-index: 1000; /* 可能需要调整以确保它显示在其他元素之上 */
            }
        </style>
    </th:block>
    <div class="wrapper py-0" layout:fragment="content">
        <div class="container-fluid">
            <div class="page-title-box py-3">
                <div class="row align-items-center">
                    <div class="col-sm-12">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html" th:href="@{/home}"><i class="mdi mdi-home-outline"></i></a>
                            </li>
                            <li class="breadcrumb-item active">VENAS</li>
                        </ol>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xl-12">
                    <div class="card card-box">
                        <div class="card-body">
                            <input type="hidden" name="id" th:value="${id}">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="my-0 header-title">[[${task.taskId}]] Analysis Result</h4>
                                <a href="javascript:void(0);" class="btn btn-outline-primary btn-sm waves-effect d-none" mainBtn showOrHideLabel="show" onclick="displayLabel(this)"><i class="mdi mdi-eye h6 m-0"></i> Display Label</a>
                                <a href="javascript:void(0);" class="btn btn-outline-primary btn-sm waves-effect" mainBtn showOrHideLabel="hide" onclick="hideLabel(this)"><i class="mdi mdi-eye-off h6 m-0"></i> Hide Label</a>
                            </div>
                            <div class="position-relative" id="myTabContent">
                                <div class="d-flex mb-1 justify-content-end align-items-center">
                                    <form class="d-flex search-graph"  id="graphSearchForm">
                                        <div class="search-graph-content">
                                            <span class="text-muted">Time：</span>
                                            <div class="input-group width-300 mr-2">
                                                <input type="text" class="form-control text-center date_input" name="timeStart">
                                                <div class="input-group-append"><span
                                                        class="input-group-text px-1 border-right-0">-</span></div>
                                                <input type="text" class="form-control text-center date_input" name="timeEnd">
                                            </div>
                                            <span class="text-muted">Locations：</span>
                                            <input type="text" class="form-control text-center width-150 mr-2" name="location">
                                            <span class="text-muted">Haploid ID/Sample ID：</span>
                                            <input type="text" class="form-control text-center width-150 mr-2" name="sampleId">
                                            <span class="text-muted">Cluster ID：</span>
                                            <input type="text" class="form-control text-center width-150 mr-2" name="lineage">
                                        </div>
                                        <button type="button" class="btn btn-primary" title="filter" onclick="filterGraph();"><i
                                                class="fa fa-filter" ></i></button>
                                        <button type="button" class="btn btn-primary ml-2" onclick="reset();filterGraph();"
                                                data-toggle="tooltip" title="reset"><i class="fa fa-trash"></i></button>
                                    </form>
                                </div>

                                <div class="chart" id="clade-graph" style="width: 100%; "></div>
                                <div id="filter4" class="chart-filter d-none">
                                    <div class="num-range">
                                        <input type="range" class="custom-range" min="0" step="1" value="1">
                                    </div>
                                    <div class="row no-gutters flex-column">
                                    </div>
                                </div>
                                <p class="text-center m-t-10">
                                    <a href="javascript:history.back()" class="btn btn-outline-primary btn-sm">Back</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="modal-clade">
            <div class="modal-dialog modal-full" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title font-weight-normal mt-0" id="modelCladeTitle"></h6>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3"  style="display: block; padding-right: 5px; padding-top: 5px;">
                        <h4 class="my-0 header-title"></h4>
                        <a href="javascript:void(0);" class="btn btn-outline-primary btn-sm waves-effect d-none" subBtn showOrHideLabel="show" onclick="displayLabel2(this)"><i class="mdi mdi-eye h6 m-0"></i> Display Label</a>
                        <a href="javascript:void(0);" class="btn btn-outline-primary btn-sm waves-effect" subBtn  showOrHideLabel="hide" onclick="hideLabel2(this)"><i class="mdi mdi-eye-off h6 m-0"></i> Hide Label</a>
                    </div>
                    <div class="modal-body">
                        <div id="clade-modal" style="height: 100%;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</html>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.js}"></script>
    <script th:src="@{/js/shine.js}"></script>
    <script th:src="@{/js/dataTool.js}"></script>
    <script th:src="@{/js/console.save.js}"></script>
    <script th:src="@{/js/3d-force-graph.min.js}"></script>
    <script>
        function setChartHeight() {
            $('.chart').css('height', $(window).height() - 300)
        }
        setChartHeight()
        $('#clade-graph').css('width', $('#myTabContent').width())
        // 颜色区间取色方法
        // rgb to hex
        function rgbToHex(r, g, b) {
            var hex = ((r << 16) | (g << 8) | b).toString(16);
            return "#" + new Array(Math.abs(hex.length - 7)).join("0") + hex;
        }

        // hex to rgb
        function hexToRgb(hex) {
            var rgb = [];
            for (var i = 1; i < 7; i += 2) {
                rgb.push(parseInt("0x" + hex.slice(i, i + 2)));
            }
            return rgb;
        }

        // 计算渐变过渡色
        function gradient(startColor, endColor, step) {
            // 将 hex 转换为rgb
            var sColor = hexToRgb(startColor),
                eColor = hexToRgb(endColor);

            // 计算R\G\B每一步的差值
            var rStep = (eColor[0] - sColor[0]) / step,
                gStep = (eColor[1] - sColor[1]) / step,
                bStep = (eColor[2] - sColor[2]) / step;

            var gradientColorArr = [];
            for (var i = 0; i < step; i++) {
                // 计算每一步的hex值
                gradientColorArr.push(rgbToHex(parseInt(rStep * i + sColor[0]), parseInt(gStep * i + sColor[1]), parseInt(bStep * i + sColor[2])));
            }
            return gradientColorArr;
        }

        //计算两个日期相差天数
        function getDaysBetween(dateString1, dateString2) {
            var startDate = Date.parse(dateString1);
            var endDate = Date.parse(dateString2);
            var days = (endDate - startDate) / (1 * 24 * 60 * 60 * 1000);
            // alert(days);
            return days;
        }

        //毫秒转日期
        Date.prototype.format = function (fmt) { //author: meizz
            var o = {
                "M+": this.getMonth() + 1,                 //月份
                "d+": this.getDate(),                    //日
                "h+": this.getHours(),                   //小时
                "m+": this.getMinutes(),                 //分
                "s+": this.getSeconds(),                 //秒
                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                "S": this.getMilliseconds()             //毫秒
            };
            if (/(y+)/.test(fmt))
                fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt))
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        }

        function getDateByDay(days) {
            var startDate = Date.parse('1900-01-01');
            var endDate = days * (1 * 24 * 60 * 60 * 1000) + startDate
            // alert(days);
            return new Date(endDate).format("yyyy-MM-dd");
        }

        // 颜色区间取色方法
        var colorPalette = [
            '#339ca8', '#0098d9', '#e6b600', '#3fb1e3',
            '#005eaa', '#96dee8', '#a0a7e6', '#c12e34',
            '#c4ebad', '#626c91', '#6be6c1', '#32a487',
            '#cda819', '#2b821d',
            '#516b91', '#59c4e6', '#edafda', '#93b7e3',
            '#a5e7f0', '#cbb0e3', '#d87c7c', '#919e8b',
            '#d7ab82', '#6e7074', '#61a0a8', '#efa18d',
            '#787464', '#cc7e63', '#724e58', '#4b565b'
        ];

        //生成base64
        function base64(data, chartColor) {
            data = data.map(function (item) {
                if (item.country) {
                    item.itemStyle = {color: chartColor[item.country]}
                } else if (item.host) {
                    item.itemStyle = {color: chartColor[item.host]}
                } else {
                    item.itemStyle = {color: chartColor[item.clade]}
                }

                return item
            })
            var base64 = echarts.init(document.getElementById('base64'), 'shine', {renderer: 'svg'})
            base64.setOption({
                series: [
                    {
                        type: 'pie',
                        radius: '100%',
                        center: ['50%', '50%'],
                        data: data,
                        label: {show: false}
                    }
                ],
                animation: false
            })
            const svg = $('#base64').find('svg')[0];
            const s = new XMLSerializer().serializeToString(svg);
            const ImgBase64 = `data:image/svg+xml;base64,${window.btoa(s)}`;
            // console.info(ImgBase64)
            return ImgBase64
        }

        function chartZoom(dom, dom1) {
            var tag = dom1.getElementsByTagName('div')[0]
            var labelShow = false
            tag.onmousewheel = function (e) {
                e = e || window.event;
                var chartZoom = dom.getOption().series[0].zoom

                if (e.wheelDelta) { //第一步：先判断浏览器IE，谷歌滑轮事件
                    // console.info('1')
                    if (e.wheelDelta > 50 && chartZoom > 2 && !labelShow) { //当滑轮向上滚动时
                        // console.info('2')
                        dom.setOption({
                            series: [{
                                label: {
                                    show: true,
                                    position: 'top',
                                    fontSize: 12,
                                    formatter: function (params) {
                                        // console.info(params)
                                        return params.data.name
                                    }
                                }
                            }]
                        })
                        labelShow = true
                    }
                    if (e.wheelDelta < 50 && chartZoom < 2 && labelShow) { //当滑轮向下滚动时
                        // console.info('3')
                        dom.setOption({
                            series: [{
                                label: {
                                    show: false
                                }
                            }]
                        })
                        labelShow = false
                    }
                } else if (e.detail) { //Firefox滑轮事件
                    if (e.detail > 50 && chartZoom > 2 && !labelShow) { //当滑轮向上滚动时
                        dom.setOption({
                            series: [{
                                label: {
                                    show: true,
                                    position: 'top',
                                    fontSize: 12,
                                    formatter: function (params) {
                                        // console.info(params)
                                        return params.data.name
                                    }
                                }
                            }]
                        })
                        labelShow = true
                    }
                    if (e.detail < 50 && chartZoom < 2 && labelShow) { //当滑轮向下滚动时
                        dom.setOption({
                            series: [{
                                label: {
                                    show: false
                                }
                            }]
                        })
                        labelShow = false
                    }
                }
            }
        }

        function reset() {
            $("#graphSearchForm").reset();
        }

        //坐标信息
        coord = {}

        mainGraphData = {};
        var cladeGraphChart = echarts.init(document.getElementById('clade-graph'));
        function drawCladeGraph() {
            coord = {}
            $.ajax({
                url: '/analysis/venas/graphData',
                type: "POST",
                data: {
                    "id": $("[name=id]").val()
                },
                beforeSend: function () {
                    cladeGraphChart.showLoading();
                },
                success: function (result) {
                    mainGraphData = result.data;

                    initLocationAutocomplete(mainGraphData);
                    initSampleIdAutocomplete(mainGraphData);
                    initLineageAutocomplete(mainGraphData);

                    doDrawCladeGraph(mainGraphData);
                }
            });
        }

        function initLocationAutocomplete(mainGraphData) {
            var availableTags = new Set();
            $.each(mainGraphData.nodes, function (i, node) {
                for(var key in node.countries) {
                    availableTags.add(key);
                }
            });
            $( "[name=location]" ).autocomplete({
                source: Array.from(availableTags)
            });
        }

        function initSampleIdAutocomplete(mainGraphData) {
            var availableTags = new Set();
            $.each(mainGraphData.nodes, function (i, node) {
                $.each(node.sampleIds, function (j, item) {
                    availableTags.add(item);
                });
                $.each(node.haploidIds, function (j, item) {
                    availableTags.add(item);
                });
            });
            $( "[name=sampleId]" ).autocomplete({
                source: Array.from(availableTags)
            });
        }

        function initLineageAutocomplete(mainGraphData) {
            var availableTags = new Set();
            $.each(mainGraphData.nodes, function (i, node) {
                $.each(node.lineage, function (j, item) {
                    availableTags.add(item);
                });
            });
            $( "[name=lineage]" ).autocomplete({
                source: Array.from(availableTags)
            });
        }


        function doDrawCladeGraph(graphData) {
            // console.log(graphData)
            cladeGraphChart.hideLoading();
            // 原始数据值范围，需要根据你的实际数据来确定
            var minVal = Math.min.apply(null, graphData.nodes.map(function(item) { return item.value; })); // 假设yourDataArray是你的数据数组
            var maxVal = Math.max.apply(null, graphData.nodes.map(function(item) { return item.value; }));

            var data = graphData.nodes.map(function (item, index) {
                /*                  if (item.value <= 20) {
                                      item.symbolSize = item.value / 5 + 7;
                                  } else if (20 < item.value && item.value <= 40) {
                                      item.symbolSize = item.value / 10 + 11;
                                  } else if (40 < item.value && item.value <= 80) {
                                      item.symbolSize = item.value / 20 + 15;
                                  } else if (80 < item.value && item.value <= 160) {
                                      item.symbolSize = item.value / 40 + 19;
                                  } else if (160 < item.value && item.value <= 320) {
                                      item.symbolSize = item.value / 80 + 23;
                                  } else if (320 < item.value && item.value <= 640) {
                                      item.symbolSize = item.value / 160 + 27;
                                  } else if (640 < item.value && item.value <= 1280) {
                                      item.symbolSize = item.value / 320 + 31;
                                  } else {
                                      //最大值为35
                                      item.symbolSize = 35;
                                  }*/

                // 假设val是节点的数据值
                // 设定最小和最大显示大小
                var minSize = 10; // 当数据值很小时，节点大小至少为10
                var maxSize = 50; // 当数据值很大时，节点大小最多为50


                // 根据数据值的比例计算大小
                // 这里使用了简单的线性插值，但你也可以使用其他更复杂的函数
                var scale = (maxSize - minSize) / (maxVal - minVal);
                var size = Math.min(maxSize, Math.max(minSize, scale * (item.value - minVal) + minSize));
                item.symbolSize = size

                if (item.highOrDark == 'dark') {
                    item.itemStyle = {color: 'rgba(0,0,0,0.55)'}
                    item.itemStyle.opacity = 0.6
                } else {
                    item.itemStyle = {color: colorPalette[index % colorPalette.length]}
                    item.itemStyle.opacity = 1
                }
                item.label = {
                    show: $("[mainBtn][showOrHideLabel=show]").hasClass("d-none"),
                    textStyle: {
                        color: 'black'
                    }
                }
                return item;
            });
            var links = graphData.links;
            cladeGraphChart.setOption({
                backgroundColor: 'white', // 或者 '#FFFFFF'
                color: colorPalette,
                tooltip: {
                    confine : true,
                    formatter: function (program) {
                        var toolTip = '';
                        if (program.dataType == 'edge') {
                            //连接线
                            toolTip += '<div style="padding-left: 5px">' + 'Distance: ' + program.data.value + '</div>'
                            var diffLength = program.data.diffs.length <= 6 ? program.data.diffs.length : 6;
                            toolTip += '<div class="mb-1 border-top mt-1 pt-1" style="padding: 0 5px;">Variations:' + program.data.diffs.length + '</div>'
                            for (var i = 0; i < diffLength; i++) {
                                toolTip += '<span class="tooltip-tag">' + program.data.diffs[i] + '</span>'
                                if (i % 3 == 2) {
                                    toolTip += '<br/>'
                                }
                            }
                            toolTip += '</div>'
                        } else {
                            //主节点

                            //国家分布统计数据
                            var countries = program.data.countries;
                            var countries2 = program.data.countries2;

                            var cladeCountryStatData = [];
                            var cladeCountryStatData2 = [];

                            for (var key in countries) {
                                cladeCountryStatData.push({value: countries[key], name: key})
                            }
                            for (var key in countries2) {
                                cladeCountryStatData2.push({value: countries2[key], name: key})
                            }
                            //virus按collection_date进行分组升序统计
                            var virus = program.data.virus;
                            var virus2 = program.data.virus2;
                            var virusXAxisData = [];
                            var virusSeriesData = [];
                            var virusXAxisData2 = [];
                            var virusSeriesData2 = [];
                            for (var key in virus) {
                                virusXAxisData.push(key);
                                virusSeriesData.push(virus[key]);
                            }
                            for (var key in virus2) {
                                virusXAxisData2.push(key);
                                virusSeriesData2.push(virus2[key]);
                            }
                            var cladeCount = program.data.value;
                            var clusterId = program.data.clusterId;
                            toolTip += '<div class="bg-light p-3" style="width: 1000px">' +
                                '<div class="row">' +
                                '<div class="col-md-6">' +
                                '<div class="d-flex justify-content-center text-dark align-items-center">' +
                                '<b>Cluster information</br>' +
                                'Cluster:' + clusterId + '</br>' +
                                'Cluster size：' + cladeCount +
                                '</br>' +
                                ' ' +
                                '</br>' +
                                ''+ '</b>' +
                                '' +
                                '</div>' +
                                '<hr><div id="tooltipPie" style="height:300px;"></div>' +
                                '<div id="tooltipLine" style="height: 150px;"></div>' +
                                '</div>' +
                                '<div class="col-md-6">' +
                                '<div class="d-flex justify-content-center text-dark align-items-center">' +
                                '<b>' +
                                'Node information</br>' +
                                'Haplotype: ' + program.data.hapid + '</br>' +
                                'Mutations: ' + program.data.mutations + '</br>' +
                                'Samples count: ' + cladeCount +
                                '</b>' +
                                '' +
                                '</div>' +
                                '<hr><div id="tooltipPie2" style="height:300px;"></div>' +
                                '<div id="tooltipLine2" style="height: 150px;"></div>' +
                                '</div>' +
                                '</div>' +
                                '</div>';
                            setTimeout(function () {
                                if(document.getElementById('tooltipPie') == null) {
                                    return;
                                }
                                var tooltipPie = echarts.init(document.getElementById('tooltipPie'))
                                var tooltipLine = echarts.init(document.getElementById('tooltipLine'))
                                tooltipPie.setOption({
                                    title: {
                                        text: 'Location of haplotypes in the cluster',
                                        textStyle:{
                                            fontSize: 14,
                                            fontWeight: 'normal'
                                        },
                                        left: 'center'
                                    },
                                    series: [
                                        {
                                            name: '国家分布',
                                            type: 'pie',
                                            radius: '50%',
                                            center: ['50%', '50%'],
                                            data: cladeCountryStatData,
                                            emphasis: {
                                                itemStyle: {
                                                    shadowBlur: 10,
                                                    shadowOffsetX: 0,
                                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                                }
                                            }
                                        }
                                    ]
                                })
                                tooltipLine.setOption({
                                    title: {
                                        text: 'Time distribution of haplotypes in the cluster',
                                        textStyle:{
                                            fontSize: 14,
                                            fontWeight: 'normal'
                                        },
                                        left: 'center'
                                    },
                                    xAxis: {
                                        type: 'category',
                                        boundaryGap: false,
                                        data: virusXAxisData
                                    },
                                    yAxis: {
                                        type: 'value',
                                        minInterval: 1
                                        // show: false
                                    },
                                    grid: {
                                        top: 30,
                                        left: 35,
                                        right: 35,
                                        bottom: 30,
                                    },
                                    series: [{
                                        data: virusSeriesData,
                                        type: 'line'
                                    }]
                                })
                            },300)
                            setTimeout(function () {
                                if(document.getElementById('tooltipPie2') == null){
                                    return;
                                }
                                var tooltipPie = echarts.init(document.getElementById('tooltipPie2'))
                                var tooltipLine = echarts.init(document.getElementById('tooltipLine2'))
                                tooltipPie.setOption({
                                    title: {
                                        text: 'Location of samples in the haplotype',
                                        textStyle: {
                                            fontSize: 14,
                                            fontWeight: 'normal'
                                        },
                                        left: 'center'
                                    },
                                    series: [
                                        {
                                            name: 'Location of samples in the haplotype',
                                            type: 'pie',
                                            radius: '50%',
                                            center: ['50%', '50%'],
                                            data: cladeCountryStatData2,
                                            emphasis: {
                                                itemStyle: {
                                                    shadowBlur: 10,
                                                    shadowOffsetX: 0,
                                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                                }
                                            }
                                        }
                                    ]
                                })
                                tooltipLine.setOption({
                                    title: {
                                        text: 'Time distribution of samples in the haplotype',
                                        textStyle: {
                                            fontSize: 14,
                                            fontWeight: 'normal'
                                        },
                                        left: 'center'
                                    },
                                    xAxis: {
                                        type: 'category',
                                        boundaryGap: false,
                                        data: virusXAxisData2
                                    },
                                    yAxis: {
                                        type: 'value',
                                        minInterval: 1
                                        // show: false
                                    },
                                    grid: {
                                        top: 30,
                                        left: 35,
                                        right: 35,
                                        bottom: 30,
                                    },
                                    series: [{
                                        data: virusSeriesData2,
                                        type: 'line'
                                    }]
                                })
                            }, 300)
                        }
                        return toolTip
                    }
                },
                series: [{
                    type: 'graph',
                    edgeSymbol: ['circle', 'arrow'],
                    edgeSymbolSize: [4, 10],
                    edgeLabel: {
                        fontSize: 20
                    },
                    layout: 'force',
                    draggable: true,
                    roam: true,
                    data: data,
                    links: links,
                    lineStyle: { // 边的样式
                        color: 'rgba(0, 0, 0, 0.8)', // 更深的颜色值
                        width: 4, // 更宽的线宽
                        type: 'solid' // 实线
                    },
                    emphasis: {
                        label: {show: false}
                    },
                    force: {
                        repulsion: 300,
                        gravity: 0.03,
                        edgeLength: [1, 100]
                    }
                }]
            });
            chartClickClade(cladeGraphChart)
            chartZoom(cladeGraphChart, document.getElementById('clade-graph'))
        }

        cladeGraphChart.on('mouseup', function (params) {
            mouseMove(cladeGraphChart, params)
        });

        function mouseMove(chart, params) {
            var option = chart.getOption();
            option.series[0].data[params.dataIndex].x = params.event.offsetX;
            option.series[0].data[params.dataIndex].y = params.event.offsetY;
            option.series[0].data[params.dataIndex].fixed = true;

            chart.setOption(option);
            coord[params.name] = params.event.offsetX + "," + params.event.offsetY;
        }

        /**
         * 点击主节点
         */
        var cladeModal;
        let subGraphDataMap = new Map();
        function chartClickClade(dom) {
            dom.on('click', function (program) {
                if (program.dataType != 'edge') {
                    $('#modelCladeTitle').text("Cluster: " + program.data.clusterId + ";Cluster size: " + program.data.clusterSize + "; KeyNode: " + program.data.hapid);
                    $('#modal-clade').modal();

                    setTimeout(function () {
                        cladeModal = echarts.init(document.getElementById('clade-modal'))
                        cladeModal.showLoading();
                        //点击主节点 得到所有的相同clade的节点信息
                        var clusterId = program.data.clusterId;
                        if(subGraphDataMap.has(clusterId)) {
                            doChartClickClade(filterSubGraphData(subGraphDataMap.get(clusterId)));
                        } else {
                            var d = getSubData(clusterId);
                            subGraphDataMap.set(clusterId, d);
                            doChartClickClade(filterSubGraphData(d));
                        }
                    }, 300)

                    setTimeout(function () {
                        cladeModal.hideLoading();
                    }, 1200)
                }
            })
        }

        function getSubData(clusterId) {
            var data = null;
            $.ajax({
                async : false,
                url: '/analysis/venas/subGraphData',
                data: {
                    "id": $("[name=id]").val(),
                    "clusterId": clusterId,
                },
                beforeSend: function () {
                },
                success: function (result) {
                    // console.log(result.data)
                    data = result.data;
                }
            })
            return data;
        }

        function doChartClickClade (subGraphData) {
            // 原始数据值范围，需要根据你的实际数据来确定
            var minVal = Math.min.apply(null, subGraphData.nodes.map(function(item) { return item.value; })); // 假设yourDataArray是你的数据数组
            var maxVal = Math.max.apply(null, subGraphData.nodes.map(function(item) { return item.value; }));

            var data = subGraphData.nodes.map(function (item, index) {
                /*                if (item.value <= 20) {
                                    item.symbolSize = item.value / 5 + 7;
                                } else if (20 < item.value && item.value <= 40) {
                                    item.symbolSize = item.value / 10 + 11;
                                } else if (40 < item.value && item.value <= 80) {
                                    item.symbolSize = item.value / 20 + 15;
                                } else if (80 < item.value && item.value <= 160) {
                                    item.symbolSize = item.value / 40 + 19;
                                } else if (160 < item.value && item.value <= 320) {
                                    item.symbolSize = item.value / 80 + 23;
                                } else if (320 < item.value && item.value <= 640) {
                                    item.symbolSize = item.value / 160 + 27;
                                } else if (640 < item.value && item.value <= 1280) {
                                    item.symbolSize = item.value / 320 + 31;
                                } else {
                                    //最大值为35
                                    item.symbolSize = 35;
                                }*/

                // 设定最小和最大显示大小
                var minSize = 20; // 当数据值很小时，节点大小至少为10
                var maxSize = 45; // 当数据值很大时，节点大小最多为50

                // 根据数据值的比例计算大小
                // 这里使用了简单的线性插值，但你也可以使用其他更复杂的函数
                var scale = (maxSize - minSize) / (maxVal - minVal);
                var size = Math.min(maxSize, Math.max(minSize, scale * (item.value - minVal) + minSize));
                item.symbolSize = size

                if (item.highOrDark == 'dark') {
                    item.itemStyle = {color: 'rgba(0,0,0,0.55)'}
                    item.itemStyle.opacity = 0.6
                } else {
                    item.itemStyle = {color: colorPalette[index % colorPalette.length]}
                    item.itemStyle.opacity = 1
                }
                item.label = {
                    show: $("[subBtn][showOrHideLabel=show]").hasClass("d-none"),
                    textStyle: {
                        color: 'black'
                    }
                }
                return item;
            });
            var links = subGraphData.links;
            cladeModal.setOption({
                backgroundColor: 'white', // 或者 '#FFFFFF'
                color: colorPalette,
                tooltip: {
                    confine : true,
                    position: function (point) {
                        return [point[0]+30,0]
                    },
                    formatter: function (program) {
                        var toolTip = '';
                        if (program.dataType == 'edge') {
                            //连接线
                            toolTip += '<div style="padding-left: 5px">' + 'Distance: ' + program.data.value + '</div>'
                            var diffLength = program.data.diffs.length <= 6 ? program.data.diffs.length : 6;
                            toolTip += '<div class="mb-1 border-top mt-1 pt-1" style="padding: 0 5px;">Variations:' + program.data.diffs.length + '</div>'
                            for (var i = 0; i < diffLength; i++) {
                                toolTip += '<span class="tooltip-tag">' + program.data.diffs[i] + '</span>'
                                if (i % 3 == 2) {
                                    toolTip += '<br/>'
                                }
                            }
                            toolTip += '</div>'
                        } else {
                            //节点

                            //国家分布统计数据
                            var countries = program.data.countries;
                            var cladeCountryStatData = [];
                            for (var key in countries) {
                                cladeCountryStatData.push({value: countries[key], name: key})
                            }

                            //virus按collection_date进行分组升序统计
                            var virus = program.data.virus;
                            var virusXAxisData = [];
                            var virusSeriesData = [];
                            for (var key in virus) {
                                virusXAxisData.push(key);
                                virusSeriesData.push(virus[key]);
                            }

                            var count = program.data.value;
                            toolTip += '<div class="bg-light p-3" style="width: 500px">' +
                                '<div class="d-flex justify-content-center text-dark align-items-center">' +
                                '<b>' +
                                'Node information</br>' +
                                'Haplotype: ' + program.data.hapid + '</br>' +
                                'Mutations: ' + program.data.mutations + '</br>' +
                                'Samples count: ' + count + '</br>' +
                                'Location ofsamples in the haplotype' + '</br>' +
                                '</b>' +
                                '' +
                                '</div><hr><div id="mTooltipPie" style="height:300px;"></div><div id="mTooltipLine" style="height: 150px;">' +
                                '</div>' +
                                '</div>';
                            setTimeout(function () {
                                if(document.getElementById('mTooltipPie') == null){
                                    return;
                                }
                                var tooltipPie = echarts.init(document.getElementById('mTooltipPie'))
                                var tooltipLine = echarts.init(document.getElementById('mTooltipLine'))
                                tooltipPie.setOption({
                                    title: {
                                        text: 'Location of samples in the haplotype',
                                        textStyle:{
                                            fontSize: 14,
                                            fontWeight: 'normal'
                                        },
                                        left: 'center'
                                    },
                                    series: [
                                        {
                                            name: 'Location of samples in the haplotype',
                                            type: 'pie',
                                            radius: '40%',
                                            center: ['50%', '50%'],
                                            data: cladeCountryStatData,
                                            emphasis: {
                                                itemStyle: {
                                                    shadowBlur: 10,
                                                    shadowOffsetX: 0,
                                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                                }
                                            }
                                        }
                                    ]
                                })
                                tooltipLine.setOption({
                                    title: {
                                        text: 'Time distribution of samples in the haplotype',
                                        textStyle:{
                                            fontSize: 14,
                                            fontWeight: 'normal'
                                        },
                                        left: 'center'
                                    },
                                    xAxis: {
                                        type: 'category',
                                        boundaryGap: false,
                                        data: virusXAxisData
                                    },
                                    yAxis: {
                                        type: 'value',
                                        minInterval: 1
                                        // show: false
                                    },
                                    grid: {
                                        top: 30,
                                        left: 35,
                                        right: 35,
                                        bottom: 30,
                                    },
                                    series: [{
                                        data: virusSeriesData,
                                        type: 'line'
                                    }]
                                })
                            },300)
                        }
                        return toolTip
                    }
                },
                series: [{
                    type: 'graph',
                    edgeSymbol: ['circle', 'arrow'],
                    edgeSymbolSize: [4, 10],
                    edgeLabel: {
                        fontSize: 20
                    },
                    layout: 'force',
                    draggable: true,
                    roam: true,
                    data: data,
                    links: links,
                    lineStyle: { // 边的样式
                        color: 'rgba(0, 0, 0, 0.8)', // 更深的颜色值
                        width: 4, // 更宽的线宽
                        type: 'solid' // 实线
                    },
                    emphasis: {
                        label: {show: false}
                    },
                    force: {
                        repulsion: 100,
                        gravity: 0.05,
                        edgeLength: [1, 60]
                    }
                }]
            });
        }

        $(document).ready(function () {
            //置空搜索条件
            drawCladeGraph();

            $('.date_input').datepicker({
                format: "yyyy/mm/dd",
        /*        language: 'zh-CN',*/
                toggleActive: true,
                autoclose: true,
                todayHighlight: true
            });
        })

        function isDateStringInRange(dateStr, startDateStr, endDateStr) {
            return dateStr >= startDateStr && dateStr <= endDateStr;
        }

        function getFirstAndLastProperty(obj) {
            const keys = Object.keys(obj);
            const firstProperty = keys[0] ? keys[0] : undefined;
            const lastProperty = keys[keys.length - 1] ? keys[keys.length - 1] : undefined;
            return {
                firstProperty,
                lastProperty
            };
        }

        /**
         * 筛选
         */
        function filterGraph(item) {
            var data = JSON.parse(JSON.stringify(mainGraphData));
            data.nodes = filterNode(data.nodes);
            doDrawCladeGraph(data);
        }


        function filterSubGraphData(subGraphData) {
            var data = JSON.parse(JSON.stringify(subGraphData));
            data.nodes = filterNode(data.nodes);
            return data;
        }

        function filterNode(nodes) {
            var timeStart = $("#graphSearchForm").find("input[name=timeStart]").val().replaceAll("/","-");
            var timeEnd = $("#graphSearchForm").find("input[name=timeEnd]").val().replaceAll("/","-");
            var timeStartCondition = true;
            var timeEndCondition = true;
            if(timeStart == '') {
                timeStartCondition = false;
                timeStart = '1900-01-01';
            }
            if(timeEnd == '') {
                timeEndCondition = false;
                timeEnd = '2099-12-31';
            }
            var location = $("#graphSearchForm").find("input[name=location]").val();
            var sampleId = $("#graphSearchForm").find("input[name=sampleId]").val();
            var lineage = $("#graphSearchForm").find("input[name=lineage]").val();
            const nodesN = nodes.filter(function (node) {
                //time
                var dates = getFirstAndLastProperty(node.virus);
                if((timeStartCondition || timeEndCondition) && !isDateStringInRange(dates.firstProperty, timeStart, timeEnd) &&  !isDateStringInRange(dates.lastProperty, timeStart, timeEnd)) {
                    return false;
                }
                //location
                if($.trim(location) != "" && node.countries && !node.countries[location]) {
                    return false;
                }
                return true;
            });
            $.each(nodes, function (i, node) {
                //sampleId
                if($.trim(sampleId) != "" ) {
                    node.highOrDark = (node.sampleIds.includes(sampleId) || node.haploidIds.includes(sampleId)) ? 'highlight' : 'dark';
                }
                //lineage
                if($.trim(lineage) != "" ) {
                    node.highOrDark = node.lineage.includes(lineage)? 'highlight' : 'dark';
                }
            });
            return nodesN;
        }
    </script>
    <script>
        function displayLabel(_this) {
            $(_this).parent().find("[showOrHideLabel=show]").addClass("d-none");
            $(_this).parent().find("[showOrHideLabel=hide]").removeClass("d-none");
            setLabelShow(cladeGraphChart, true);
        }

        function hideLabel(_this) {
            $(_this).parent().find("[showOrHideLabel=show]").removeClass("d-none");
            $(_this).parent().find("[showOrHideLabel=hide]").addClass("d-none");
            setLabelShow(cladeGraphChart, false);
        }

        function displayLabel2(_this) {
            $(_this).parent().find("[showOrHideLabel=show]").addClass("d-none");
            $(_this).parent().find("[showOrHideLabel=hide]").removeClass("d-none");
            setLabelShow(cladeModal, true);
        }

        function hideLabel2(_this) {
            $(_this).parent().find("[showOrHideLabel=show]").removeClass("d-none");
            $(_this).parent().find("[showOrHideLabel=hide]").addClass("d-none");
            setLabelShow(cladeModal, false);
        }

        function setLabelShow(chart, show) {
            var option = chart.getOption();
            // 切换标签的显示状态
            option.series[0].data.forEach(obj => {
                if(obj.label){
                    obj.label.show = show;
                }
            });
            // 更新图表
            chart.setOption(option);
        }

    </script>
</th:block>
