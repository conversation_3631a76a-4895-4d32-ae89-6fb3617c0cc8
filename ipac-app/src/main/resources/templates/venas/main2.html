<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
    <th:block layout:fragment="custom-css">
        <link rel="stylesheet" th:href="@{/libs/select2/css/select2.min.css}">
    </th:block>
    <div class="wrapper py-0" layout:fragment="content">
        <div class="container-fluid">
            <div class="page-title-box py-3">
                <div class="row align-items-center">
                    <div class="col-sm-12">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.html"><i class="mdi mdi-home-outline"></i></a>
                            </li>
                            <li class="breadcrumb-item active">VENAS</li>
                        </ol>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xl-12">
                    <div class="card card-box">
                        <div class="card-body">
                            <h4 class="mt-0 header-title mb-3">VENAS2</h4>
                            <div class="info-box">
                                <h6>Instruction</h6>
                                <p class="mb-1">VENAS2 is an updated Viral genome Evolution Network Analysis System.Comprehensive analyses of viral genomes can provide a global picture of SARS-CoV-2 transmission and help to predict the oncoming trends of the pandemic. However, the rapid accumulation of SARS-CoV-2 genomes presents an unprecedented data size and complexity that has exceeded the capacity of existing methods in constructing evolution network through virus genotyping.</p>
                            </div>
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs-custom-2" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link"  th:href="@{/venas/main1}">
                                        <span class="d-sm-block">Construct haplotype network </span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link active"  href="javascript:void(0);">
                                        <span class="d-sm-block">Trace new samples</span>
                                    </a>
                                </li>
                            </ul>
                            <form action="" class="ana-form" id="dataForm">
                                <div class="row">
                                    <div class="offset-md-1 col-md-10">
                                        <div class="card border shadow-sm">
                                            <div class="card-body">
                                                <div class="form-group row align-items-center">
                                                    <label for="" class="col-md-3 col-form-label">Select Task</label>
                                                    <div class="col-md-5">
                                                        <select class="form-control select2" name="parentTaskId">

                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center mb-1">
                                                    <label for="" class="col-md-3 col-form-label">Pathogen Species</label>
                                                    <div class="col-md-8">
                                                        <div class="btn-group btn-group-toggle flex-wrap" data-toggle="buttons">
                                                            <label class="btn btn-outline-primary btn-sm px-3 active"><input type="radio" name="pathogenSpecies" value="SARS-Cov-19" checked>SARS-Cov-19</label>
                                                            <label class="btn btn-outline-primary btn-sm px-3"><input type="radio" name="pathogenSpecies" value="M.tuberculosis">M.tuberculosis</label>
                                                            <label class="btn btn-outline-primary btn-sm px-3"><input type="radio" name="pathogenSpecies" value="others">others</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-baseline mb-0">
                                                    <label for="" class="col-md-3 col-form-label">Upload samples files</label>
                                                    <div class="col-md-8">
                                                        <div class="form-group">
                                                            <label><a  th:href="@{/example/covid19_example1_new.txt}" href="javascript:void(0);" download="covid19_example1_new.txt">example file</a></label>
                                                            <input type="file" id="fileInput" name="file" class="filestyle input-group-sm" data-buttonname="btn-secondary" >
                                                            <textarea id="fileContent" name="fileContent" class="form-control" rows="4" disabled>
The format of input samples file:
Samplename   Mutations   Time    Country
EPI_ISL_784970  240(SNP:C->T);1059(SNP:C->T) 2020-01-01 China
EPI_ISL_784972  240(SNP:C->T);1059(SNP:C->T);2448(SNP:G->-) 2020-01-01 China</textarea>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="card-footer bg-white">
                                                <div class="text-center">
                                                    <button type="button" class="btn btn-primary mr-2" onclick="run()">Run</button>
                                                    <button type="reset" class="btn btn-secondary"  onclick="reset()">Reset</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</html>
<th:block layout:fragment="custom-script">
    <script th:src="@{/libs/select2/js/select2.min.js}"></script>
    <script>
        function checkLogin() {
            var username = $("#username").attr("username");
            if(username == undefined || username==''){
                window.location.href = $("#loginA").attr('href');
                return false;
            }
            return true;
        }

        function reset() {
            $("#formData").reset();
        }

       /* document.getElementById('fileInput').addEventListener('change', function(e) {
            var file = e.target.files[0];
            if (!file) {
                return;
            }
            var reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('fileContent').value = e.target.result;
            };
            // 如果文件是文本文件（如.txt），则可以使用readAsText方法
            // 第二个参数是字符编码，这里使用默认的'UTF-8'
            reader.readAsText(file);
        });*/

        function run() {
            if(!checkLogin()){
                return;
            }
            var parentTaskId = $("#dataForm").find("[name='parentTaskId']").val();
            if($.trim(parentTaskId) == '') {
                layer.msg("Please select task！");
                return;
            }
            var  uploadFile = $("#dataForm").find("input[name='file']").val();
            if (uploadFile == '') {
                layer.msg("Please select a file！");
                return;
            }

            var formData = new FormData($("#dataForm")[0]);
            var loadLayerIndex;
            $.ajax({
                url: '/analysis/venas2/addTask',
                method: 'post',
                dataType: 'json',
                contentType: false,
                processData: false,
                data: formData,
                beforeSend: function () {
                    loadLayerIndex = layer.load(1, {
                        shade: [0.1, '#fff'] //0.1透明度的白色背景
                    });
                },
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg("Submitted successfully", {time: 500}, function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            location.href=  _context_path + "/usercenter/venas/tasks";
                        });
                    }else {
                        layer.alert(result.message, {icon: 2});
                    }
                },
                complete: function () {
                    layer.close(loadLayerIndex);
                }
            });

        }


        $(function () {
            initSelect2($("#dataForm").find(".select2"));
        })

        function initSelect2($ele) {
            $ele.select2({
                ajax: {
                    url: '/analysis/task/search',
                    dataType: 'json',
                    quietMillis: 250,
                    delay: 250,
                    type: 'post',
                    data: function (params) {
                        console.log(JSON.stringify(params));
                        return {
                            keyword: params.term,
                            page: params.page || 0
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 0;
                        console.info(data)
                        return {
                            results: data.data,
                            pagination: {
                                more: (params.page * 30) < data.total
                            }
                        };
                    },
                    cache: true
                },
                placeholder: "Please enter",
                escapeMarkup: function (markup) { return markup; }, // 让template的html显示效果，否则输出代码
                minimumInputLength: 0,    //搜索框至少要输入的长度，此处设置后需输入才显示结果
                multiple: false, //是否多选
                templateSelection: function formatSubjectSelection(item) {
                    return item.taskId;
                }
                // 自定义选中选项的样式模板
            });
        }
    </script>
</th:block>
