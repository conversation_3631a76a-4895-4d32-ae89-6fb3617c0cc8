<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
    <div class="wrapper py-0" layout:fragment="content">

        <div class="wrapper-page">

            <div class="card overflow-hidden account-card mx-3">

                <div class="bg-login p-4 text-white text-center position-relative">
                    <h4 class="font-20 m-b-5">SIGN IN</h4>
                </div>
                <div class="account-card-content">

                    <form class="form-horizontal m-t-10" action="" th:action="@{/login}" id="data_form" method="post">
                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                        <th:block th:if="${!#strings.isEmpty(param?.error) }">
                            <div class="text-center text-warning">
                                <p>Incorrect username or password</p>
                            </div>
                        </th:block>
                        <th:block th:if="${!#strings.isEmpty(param?.logout) }">
                            <div class="text-center text-success">
                                <p>logout successfully</p>
                            </div>
                        </th:block>
                        <th:block th:if="${!#strings.isEmpty(param?.other)}">
                            <div class="text-center text-success">
                                <p>User logged in elsewhere</p>
                            </div>
                        </th:block>
                        <div class="form-group">
                            <label for="username">User Name</label>
                            <input type="text" class="form-control" id="username"  name="username"
                                   placeholder="Please enter your username">
                        </div>
                        <div class="form-group">
                            <label for="userpassword">Password</label>
                            <input type="password" class="form-control" id="userpassword" name="password"
                                   placeholder="Please enter your password">
                        </div>

                        <div class="form-group row m-t-20">
                            <div class="col-sm-6">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="customControlInline" name="remember-me" >
                                    <label class="custom-control-label" for="customControlInline">Remember
                                        passwords</label>
                                </div>
                            </div>
                            <div class="col-sm-6 text-right">
                                <button class="btn btn-primary w-md waves-effect waves-light" type="submit">Sign in
                                </button>
                            </div>
                        </div>

                        <div class="form-group m-t-10 mb-0 row">
                            <div class="col-6 m-t-10">
                                <a href="recoverpw.html"  th:href="@{/forgetPwd}" class="text-black-50"><i class="mdi mdi-lock"></i> Forgotten
                                    password?</a>
                            </div>
                            <div class="col-6 m-t-10 text-right">
                                No account？ <a href="register.html"  th:href="@{/register}" class="text-primary">Sign up</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</html>
<th:block layout:fragment="custom-script">
    <script>

    </script>
</th:block>
