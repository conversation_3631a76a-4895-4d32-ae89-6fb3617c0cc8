<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <div th:fragment="div">
        <h4 class="mt-0 header-title mb-3"><i></i> Personal Center</h4>
        <ul id="left-common">
            <li class="py-1">
                <span class="mdi mdi-disc text-secondary m-r-5"></span>
                <a href="index-my-data.html" th:href="@{/usercenter/files}"
                   th:class="${#strings.contains(#httpServletRequest.getServletPath(), 'files') ? 'text-primary' : 'text-secondary'}">My
                    Files</a>
            </li>
            <li class="py-1">
                <span class="mdi mdi-disc text-muted m-r-5"></span>
                <a class="text-secondary">My Analysis Tasks</a>
                <ul class="pt-1 pl-3">
                    <li>
                        <a href="index-my-tasks.html" th:href="@{/usercenter/tasks}"
                           th:class="${#strings.contains(#httpServletRequest.getServletPath(), '/usercenter/tasks') and !#strings.contains(#httpServletRequest.getServletPath(), 'venas') ? 'text-primary' : 'text-secondary'}">
                            <i class="mdi mdi-chevron-right"></i> IPP
                        </a>
                    </li>
                    <li>
                        <a th:href="@{/usercenter/venas/tasks}"
                           th:class="${#strings.contains(#httpServletRequest.getServletPath(), 'venas') ? 'text-primary' : 'text-secondary'}">
                            <i class="mdi mdi-chevron-right"></i> VENAS
                        </a>
                    </li>
                    <li>
                        <a th:href="@{/pits/tasks}"
                           th:class="${#strings.contains(#httpServletRequest.getServletPath(), 'pits') ? 'text-primary' : 'text-secondary'}">
                            <i class="mdi mdi-chevron-right"></i> PITS
                        </a>
                    </li>
                    <li>
                        <a th:href="@{/gvap/tasks}"
                           th:class="${#strings.contains(#httpServletRequest.getServletPath(), 'gvap') ? 'text-primary' : 'text-secondary'}">
                            <i class="mdi mdi-chevron-right"></i> GVAP
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</html>
