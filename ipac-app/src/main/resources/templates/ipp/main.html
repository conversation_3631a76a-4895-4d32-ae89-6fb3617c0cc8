<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
    <th:block layout:fragment="custom-css">
        <link rel="stylesheet" th:href="@{/libs/treetable/css/jquery.treetable.css}">
        <link rel="stylesheet" th:href="@{/libs/treetable/css/jquery.treetable.theme.default.css}">
    </th:block>
    <div class="wrapper py-0" layout:fragment="content">
        <div class="container-fluid">
            <div class="page-title-box py-3">
                <div class="row align-items-center">
                    <div class="col-sm-12">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a th:href="@{/home}"><i class="mdi mdi-home-outline"></i></a>
                            </li>
                            <li class="breadcrumb-item active">IPP</li>
                        </ol>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xl-12">
                    <div class="card card-box">
                        <div class="card-body">
                            <h4 class="mt-0 header-title mb-3">IPP</h4>
                            <div class="info-box">
                                <h6>Instruction</h6>
                                <p class="mb-1"><img th:src="@{/images/pic-flow01.png}" src="assets/images/pic-flow01.png" class="img-info" alt=""></p>
                                <p class="mb-1">To speed up the identification of pathogens, we developed the automated Identification Platform for Pathogens (IPP), which takes the metatranscriptomic data measured by next-generation sequencing as input and first filters out the low-quality sequencing data by quality control of the sequence data. Next, we used FastViromeExplorer to compare the remaining sequences with the known pathogenic reference databases to identify possible pathogen species in the samples; and finally, we assembled the genome of the pathogens in the samples based on the reference genomes of the identified pathogens.</p>
                                <p class="mb-1">
                                    The system is capable of automatically pairing files named in a certain style, or you can pair them manually. Please do not change the file name after successful pairing. Please try to name your files according to the following style before uploading:
                                </p>
                            </div>
                            <div class="alert alert-warning alert-wth-icon alert-dismissible fade show" role="alert">
                                <span class="alert-icon-wrap"><i class="fas fa-exclamation-circle fa-2x"></i></span>
                                The system is capable of automatically pairing files with some naming styles, or you can pair them manually. Please do not change the file name after successful pairing. Please try to name them according to the following style before uploading:<br>
                                <strong>XXX1.fq.gz，XXX2.fq.gz</strong> <br>
                                <strong>XXX1.fastq.gz，XXX2.fastq.gz</strong>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">×</span>
                                </button>
                            </div>
                            <form action="" class="ana-form" id="fileArr_form">

                                <div class="row">
                                    <div class="offset-md-2 col-md-8">
                                        <div class="card border shadow-sm">
                                            <div class="card-header bg-white">
                                                <div class="d-flex align-content-center justify-content-between">
                                                    <h5 class="font-600 font-14 mt-2 mb-0">Select the paired fq.gz files</h5>
                                                    <span><a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="addFileArr(this)">Adding paired groups</a></span>
                                                </div>
                                            </div>
                                            <div class="card-body" id="file_clone_div" >
                                                <div class="row mx-md-n2 fileArr">
                                                    <div class="col-md-2"><label class="py-1">Paired group<span class="fileArr_index">1</span>：</label></div>
                                                    <div class="col-md-4 sel-ele">
                                                        <div class="d-flex align-items-center justify-content-between">
                                                            <a href="javascript:void(0);" data-toggle="modal" data-target=".sel-modal" onclick="initTreeModal(this)" class="btn btn-info btn-sm ">Select</a>
                                                            <div class="sel-result">
                                                                <span class="seled" id="1_em_1" file_id="">Selected：<b class="text-primary"></b></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4 sel-ele">
                                                        <div class="d-flex align-items-center justify-content-between">
                                                            <a href="javascript:void(0);" data-toggle="modal" data-target=".sel-modal" onclick="initTreeModal(this)"  class="btn btn-info btn-sm">Select</a>
                                                            <div class="sel-result">
                                                                <span class="seled" id="2_em_1" file_id="">Selected：<b class="text-primary"></b></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-2 p-0 action-del">
                                                        <a href="javascript:void(0);" onclick="removeFileArr(this)" class="btn btn-danger btn-sm">Delete</a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-footer bg-white">
                                                <div class="text-center">
                                                    <button type="button"  onclick="analysis()" class="btn btn-primary mr-2">Analysis</button>
                                                    <button type="reset" class="btn btn-secondary"  onclick="location.reload()">Reset</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade sel-modal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" id="treeModal">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title font-14 mt-0">Select Files</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    </div>
                    <div class="modal-body" id="file_table_tree">
                        <div class="table-responsive">
                            <input id="file_em_id" value="" type="hidden">
                            <table class="table table-striped table-sm mb-0 treetable">

                                <thead class="thead-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Size</th>
                                        <th>Last commit date</th>
                                    </tr>
                                </thead>
                                <tbody>

                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary waves-effect" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary waves-effect waves-light" onclick="setFileName()">Confirm</button>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </div><!-- /.modal -->

        <div class="modal fade sel-modal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" id="treeModal2">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title font-14 mt-0">Select Files</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    </div>
                    <div class="modal-body" id="file_table_tree2">
                        <div class="table-responsive">
                            <form id="folder_form">
                                <table class="table table-striped table-sm mb-0 treetable">

                                    <thead class="thead-light">
                                        <tr>
                                            <th>Name</th>
                                            <th>Size</th>
                                            <th>Last commit date</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </form>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary waves-effect" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary waves-effect waves-light" onclick="matchFileGroup()">Confirm</button>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </div><!-- /.modal -->

    </div>
</html>
<th:block layout:fragment="custom-script">

    <script th:src="@{/libs/bootstrap-filestyle/js/bootstrap-filestyle.min.js}"></script>
    <script th:src="@{/libs/treetable/js/jquery.treetable.js}"></script>
    <script th:src="@{/js/util/treeLoad.js}"></script>
    <script>
        function checkLogin() {
            var username = $("#username").attr("username");
            if(username == undefined || username==''){
                window.location.href = $("#loginA").attr('href');
                return false;
            }
            return true;
        }

        $(function () {
            var username = $("#username").attr("username");
            if (username != undefined && username !='') {
                loadFileTree()
            }
        });

        function loadFileTree() {
            $.ajax({
                url: "/file/fileTree",
                data : {"parentPath" : "/"},
                method: 'post',
                success: function (result) {
                    $("#treeModal").find("tbody").html(result);
                    $("#treeModal2").find("tbody").html(result);
                    loadTree();
                }
            });
        }

        function loadTree() {
            $(".treetable").each(function () {
                var $table = $(this);
                fileTreeUtil.load($table);
            });
            String.prototype.endWith=function(endStr){
                var d=this.length-endStr.length;
                return (d>=0&&this.lastIndexOf(endStr)==d);
            }
            $('[data-toggle="tooltip"]').tooltip();
        }

        function setFileName() {
            var items = $("#file_table_tree").find("[type=checkbox]:checked");
            if(items.length==0){
                layer.msg("Please select files");
                return;
            }
            var checkbox = items.eq(0);
            var emId = $("#file_em_id").val();

            if(checkbox.val().indexOf(" ")!=-1){
                layer.msg("File name cannot contain spaces");
                return;
            }
            if(!checkbox.val().endWith(".fq.gz") &&  !checkbox.val().endWith(".fastq.gz")){
                layer.msg("The file name suffix must be fq.gz or fastq.gz");
                return;
            }
            _setFileNameAndFileId(emId , checkbox.val(), checkbox.attr("file_id"))
            $("#treeModal").modal("hide");
        }

        function _setFileNameAndFileId(emId , fileName, fileId) {
            $("#"+emId).html('Selected：<b class="text-primary" data-toggle="tooltip" title="'+fileName+'">'+fileName+'</b>');
            $("#"+emId).attr("file_id", fileId);
            $('[data-toggle="tooltip"]').tooltip();
        }

        function initTreeModal(_this) {
            if(!checkLogin()){
                return;
            }
            $("#file_em_id").val($(_this).parent().find("[file_id]").attr("id"));
            $("#treeModal").modal("show");
        }

        function removeFileArr(_this) {
            if(!checkLogin()){
                return;
            }
            if($(_this).parents(".fileArr:first").parents(":first").find(".fileArr").length == 1){
                return
            }
            $(_this).parents(".fileArr:first").remove();

            resetFileArrIndex()
        }

        /**
         * 自增序列
         * @type {number}
         */
        var sequence = 2;
        function addFileArr(_this) {
            if(!checkLogin()){
                return;
            }
            _cloneFileArr();
            resetFileArrIndex()
        }

        function _cloneFileArr() {
            var clone = $("#file_clone_div").children(":first").clone(true);
            var itemIndex = sequence++;
            clone.find("[file_id]").eq(0).attr("id","1_em_"+itemIndex);
            clone.find("[file_id]").eq(1).attr("id","2_em_"+itemIndex);
            $("#file_clone_div").append(clone);
            return clone;
        }

        /**
         * 重设组号
         */
        function resetFileArrIndex() {
            $("#fileArr_form").find(".fileArr_index").each(function (i) {
                $(this).text(i+1);
            })
        }

        function analysis() {
            if(!checkLogin()){
                return;
            }
            var formData = new FormData();
            formData.append("type", "ipp");
            var complete = true;
            $("#fileArr_form").find(".fileArr").each(function (i) {
                var file_id1 =  $(this).find("[file_id]").eq(0).attr("file_id");
                var file_id2 =  $(this).find("[file_id]").eq(1).attr("file_id");
                if($.trim(file_id1)=='' || $.trim(file_id2)==''){
                    complete = false;
                }
                formData.append("userTaskIdArrList["+i+"].ids[0]", file_id1);
                formData.append("userTaskIdArrList["+i+"].ids[1]", file_id2);

            });
            if(!complete){
                layer.alert("All paired sequence groups must select two files", {icon: 2});
                return;
            }
            var loadLayerIndex;
            $.ajax({
                url: '/analysis/addTask',
                method: 'post',
                dataType: 'json',
                contentType: false,
                processData: false,
                data: formData,
                beforeSend: function () {
                    loadLayerIndex = layer.load(1, {
                        shade: [0.1, '#fff'] //0.1透明度的白色背景
                    });
                },
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg("Submitted successfully", {time: 500}, function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            location.href=  _context_path + "/usercenter/tasks";
                        });
                    }else {
                        layer.alert(result.message, {icon: 2});
                    }
                },
                complete: function () {
                    layer.close(loadLayerIndex);
                }
            });
        }


        /*---------------------FTP已上传数据自动配对------------------------------------------*/
        function initTreeModal2(_this) {
            $("#treeModal2").modal("show");
        }

        /**
         * 对文件夹的checkbox点击绑定事件
         */
        $(function () {
            $("#treeModal2").find(".folder").each(function () {
                $(this).parents("td:first").find("input[type=checkbox]").click(function () {
                    var fileId = $(this).attr("file_id");
                    if ($(this).is(':checked')) {
                        childrenFolderChecked(fileId, true)
                    } else {
                        childrenFolderChecked(fileId, false)
                    }
                })
            });

            function childrenFolderChecked(parentFileId, isChecked) {
                $("#treeModal2").find("[data-tt-parent-id="+parentFileId+"]").find(".folder").each(function () {
                    $(this).parents("td:first").find("input[type=checkbox]").each(function () {
                        var fileId = $(this).attr("file_id");
                        $(this).prop("checked", isChecked);
                        childrenFolderChecked(fileId, isChecked)
                    })
                });
            }
        });

        function matchFileGroup() {
            var formData = new FormData($("#folder_form")[0]);
            if($("#treeModal2").find("[type=checkbox]:checked").length == 0){
                layer.msg("Please select a folder");
                return;
            }
            var loadLayerIndex;

            $.ajax({
                url: '/file/matchFileGroup',
                data: formData,
                method: 'post',
                dataType: 'json',
                contentType: false,
                processData: false,
                beforeSend: function () {
                    loadLayerIndex = layer.load(1, {
                        shade: [0.1, '#fff'] //0.1透明度的白色背景
                    });
                },
                success: function (result) {
                    if (result.code == 200) {
                        if(result.data!=null){
                            var data = result.data;
                            if(data.length == 0){
                                layer.alert("There is currently no pairing data available", {icon: 2});
                                return;
                            }
                            $("#fileArr_form").find(".fileArr").each(function (i) {
                                if(i != 0){
                                    $(this).remove();
                                }
                            });
                            $(data).each(function (i) {
                                var ftpFileList = data[i];
                                if(ftpFileList.length == 2){
                                    var ftpFile0 = ftpFileList[0];
                                    var ftpFile1 = ftpFileList[1];
                                    var fileArr;
                                    if(i == 0){
                                        fileArr = $("#fileArr_form").find(".fileArr").eq(0);
                                    }else{
                                        fileArr = _cloneFileArr($("#addFileArr_btn"));
                                    }
                                    var emId0 = fileArr.find("[file_id]").eq(0).attr("id");
                                    _setFileNameAndFileId(emId0 , ftpFile0.fileName, ftpFile0.id);
                                    var emId1 = fileArr.find("[file_id]").eq(1).attr("id");
                                    _setFileNameAndFileId(emId1 , ftpFile1.fileName, ftpFile1.id);
                                }
                            });
                            resetFileArrIndex()
                        }
                    }else {
                        layer.alert(result.message, {icon: 2});
                    }
                },
                complete: function () {
                    layer.close(loadLayerIndex);
                    $("#treeModal2").modal("hide");
                }
            });
        }
    </script>
</th:block>
