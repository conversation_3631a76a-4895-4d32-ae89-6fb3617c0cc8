<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
    <th:block layout:fragment="custom-css">
        <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    </th:block>
    <div class="wrapper py-0" layout:fragment="content">
        <div class="wrapper-page">

            <div class="card overflow-hidden account-card mx-3">

                <div class="bg-login p-4 text-white text-center position-relative">
                    <h4 class="font-20 m-b-5">SIGN UP</h4>
                </div>
                <div class="account-card-content">
                    <form class="form-horizontal strength" id="register-form">

                        <div class="form-group row">
                            <label for="accountname" class="col-sm-3 col-form-label text-nowrap">User name <span class="text-danger">*</span> </label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control validate[required,custom[AccountName]]" id="accountname" name="accountname"
                                       placeholder="Please enter your username">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="useremail" class="col-sm-3 col-form-label">Email <span class="text-danger">*</span> </label>
                            <div class="col-sm-9">
                                <input type="email" class="form-control validate[required,custom[email]]" id="useremail"
                                       name="useremail" placeholder="Please enter your email">
                            </div>
                        </div>
                        <div class="form-group mb-2 row">
                            <label for="password1" class="col-sm-3 col-form-label">Password <span class="text-danger">*</span> </label>
                            <div class="col-sm-9">
                                <div class="input-group">
                                    <input class="strength__input form-control validate[required, minSize[8]]" name="password1"
                                           type="password" id="password1"
                                           aria-describedby="passwordHelp" placeholder="Please enter your password"/>
                                    <div class="input-group-append">
                                        <button class="strength__visibility btn btn-outline-secondary" type="button"><span
                                                class="strength__visibility-icon" data-visible="hidden"><i
                                                class="fas fa-eye-slash"></i></span><span class="strength__visibility-icon js-hidden"
                                                                                          data-visible="visible"><i class="fas fa-eye"></i></span></button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12 text-center">
                                <small class="strength__error text-danger js-hidden">You can't enter spaces in the password!</small>
                                <small class="form-text text-muted mt-2" id="passwordHelp">Please enter more than 8 characters, use upper and lower case letters, numbers and symbols to make your password stronger!</small>
                            </div>
                        </div>
                        <div class="strength__bar-block progress mb-2">
                            <div class="strength__bar progress-bar bg-danger" role="progressbar" aria-valuenow="0"
                                 aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="form-group row">
                            <label for="password2" class="col-sm-3 col-form-label font-12 text-nowrap">Confirm password <span class="text-danger">*</span> </label>
                            <div class="col-sm-9">
                                <input type="password" class="form-control validate[required]" name="password2" id="password2"
                                       placeholder="Please confirm your password">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="name" class="col-sm-3 col-form-label">Name <span class="text-danger">*</span> </label>
                            <div class="col-sm-9">
                                <input type="text" maxlength="20" class="form-control validate[required]" id="name" name="name"
                                       placeholder="Please enter your name">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="organization" class="col-sm-3 col-form-label">Company <span class="text-danger">*</span> </label>
                            <div class="col-sm-9">
                                <input type="text" maxlength="200" class="form-control validate[required]" id="organization"
                                       name="organization" placeholder="Please enter your company">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="tel" class="col-sm-3 col-form-label font-12 text-nowrap">Contact number </label>
                            <div class="col-sm-9">
                                <input type="text" maxlength="20" class="form-control" id="tel" name="tel"
                                       placeholder="Please enter a contact number">
                            </div>
                        </div>
                        <div class="form-group row m-t-20">
                            <div class="col-sm-12 text-right">
                                <button class="strength__submit btn btn-primary w-md waves-effect waves-light" type="button"  onclick="registered(this)" >Sign up</button>
                            </div>
                        </div>

                        <div class="form-group m-t-10 mb-0 row">
                            <div class="col-12 m-t-10">
                                <p class="mb-0 text-center"> <a href="login.html" th:href="@{/login}"  class="text-black-50">Already have an account to log in</a></p>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</html>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-EN.js}"></script>
    <script th:src="@{/js/reg.js}"></script>
    <script>

        function registered(_this) {
            if (!$("#register-form").validationEngine('validate')) {
                return;
            }

            if ($(_this).attr("strength") === 'none' || $(_this).attr("strength") === 'weak') {
                layer.alert("Insufficient password strength！", {icon: 2});
                return;
            }

            $.ajax({
                url: "/registered",
                data: $("#register-form").serialize(),
                type: "post",
                dataType: 'json',
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg("registered successfully");
                        setTimeout(function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            location.href = _context_path + "/login";
                        }, 2000);
                    } else {
                        layer.alert(result.message, {icon: 2});
                    }
                }
            });
        }
    </script>
</th:block>
