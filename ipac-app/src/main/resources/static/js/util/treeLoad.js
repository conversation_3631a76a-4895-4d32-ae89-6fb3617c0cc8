var fileTreeUtil = {
    load : function ($table) {
        $table.on('change', 'input[type="checkbox"]', function(){
            $table.find('input[type="checkbox"]').not(this).prop("checked", false);
        });
        $table.treetable({
            expandable: true,
            onNodeExpand: function () {
                // 分支展开后的回调函数
                var node = this;
                //判断当前节点是否已经拥有子节点
                var childSize = $table.find("[data-tt-parent-id='" + node.id + "']").length;
                if (childSize > 0) {
                    return;
                }
                var path = $table.find("[data-tt-id='" + node.id + "']").attr("file_path");
                $.ajax({
                    loading: false,
                    sync: false,
                    url: "/file/fileTreeNodes",
                    data: {"parentPath": path },
                    success: function (result) {
                        if (200 == result.code) {
                            var data = result.data;
                            if (0 == data.length) {
                                //不存在子节点
                                var $tr = $table.find("[data-tt-id='" + node.id + "']");
                                $tr.attr("data-tt-branch", "false");
                                // data-tt-branch 标记当前节点是否是分支节点，在树被初始化的时候生效
                                $tr.find("span.indenter").html("");// 移除展开图标
                                return;
                            }
                            var rows = createHtml(result.data, node.id);
                            $table.treetable("loadBranch", node, rows);
                            // 插入子节点
                            $table.treetable("expandNode", node.id);
                            // 展开子节点
                        } else {
                            console.error(result.tip);
                        }
                    }
                });
            }
        });

        function createHtml(data, parentKey) {
            var rows = [];
            $.each(data, function (i, file) {
                rows.push("<tr data-tt-id='"+file.key+"'  file_path='"+file.data.path+"' data-tt-parent-id='"+parentKey+"'  data-tt-branch="+file.folder+">");
                if(file.folder){
                    rows.push("<td><span class='folder'>"+file.title+"</span></td>\n" +
                        "<td></td>\n" +
                        "<td>--</td>");
                }else{
                    rows.push('   <td>\n' +
                        '<input type="checkbox" name="custom1" value="'+file.title+'" file_id="'+file.data.path+'" class="form-check-input position-static m-r-5" >\n' +
                        '<span class=\'file\'>'+file.title+'</span>\n' +
                        '</td>\n' +
                        '<td>'+file.data.size+'</td>\n' +
                        '<td>'+file.data.time+'</td>');
                }
                rows.push("</tr>");
            });
            return rows.join("");
        }
    }
}

