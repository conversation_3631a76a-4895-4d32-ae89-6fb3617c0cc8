:root {
    /* --primary: #4450dc; */
    --primary: #4e65e9;
}
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: none;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-button {
    display: none;
}
body {
    font-family: 'Helvetica Neue', Helvetica, Arial, '\5FAE\8F6F\96C5\9ED1', sans-serif;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.5;
    color: #5b626b;
    background-color: #f5f5f5;
}

html {
    overflow-x: hidden;
    position: relative;
    min-height: 100%;
}

ul, li {
    list-style: none;
    padding: 0;
    margin: 0;
}

img, svg {
    max-width: 100%;
}

h1, h2, h3, h4, h5, h6 {
    margin: 10px 0;
    font-family: 'Helvetica Neue', Helvetica, Arial, '\5FAE\8F6F\96C5\9ED1', sans-serif;
    font-weight: 600;
}

p {
    line-height: 1.7;
}

a:hover {
    outline: 0;
    text-decoration: none;
}

a:active {
    outline: 0;
    text-decoration: none;
}

a:focus {
    outline: 0;
    text-decoration: none;
}

b, strong {
    font-weight: 600;
}

/* ::-webkit-scrollbar { width: 6px; } ::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); } ::-webkit-scrollbar-thumb { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); } */
.container-alt {
    margin-left: auto;
    margin-right: auto;
    padding-left: 15px;
    padding-right: 15px;
}


header{
    border: 0;
    -webkit-transition: all .5s ease;
    transition: all .5s ease;
    box-shadow: 0 1px 10px -3px #9aacbb;
}
header .top-header {
    background-color: #104eb1;
    height: 30px;
    line-height: 2;
    box-shadow: inset 0 15px 0 rgba(255, 255, 255, .05);
}
header .top-header .top-container{
    width: 100%;
    max-width: 1200px;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    display:flex;
    justify-content: space-between;
    align-items: center;
}
header .top-header .top-list-inline{
    list-style: none;
    padding:0;
    margin:0;
}
header .top-header .top-list-inline a {
    color: #fff;
}

header .top-header .top-list-inline a:hover {
    color: #d8dbf0;
}
header .top-header .top-list-inline-item{
    display: inline-block;
}
header .top-header .top-list-inline-item:not(:last-child) {
    margin-right: .8rem;
}

header .top-header .logo-bmdc {
    background: url(../images/logo-bmdc.png) no-repeat left center;
    width: 114px;
    height: 25px;
    position: relative;
}
header .top-header .logo-bmdc-other{
    line-height: 1.8;
    position: relative;
}
header .top-header .logo-bmdc a {
    width: 60px;
    height: 24px;
    left: 56px;
    top: 2px;
    position: absolute;
    text-indent: -9999px;
}
header .top-header .logo-bmdc-other a{
    color:#fff;
    font-size:18px;
    font-family: 'Myriad Pro Bold';
    letter-spacing: .025rem;
}
header .top-header .logo-bmdc-other span{
    color:#fff;
    font-size:18px;
    padding:0 .2rem;
}

.top-list-inline-item:not(:last-child) {
    margin-right: 0;
}


.top-list-inline-item .dropdown-menu {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.top-list-inline-item .nav-link {
    padding: 0 15px;
}
.top-list-inline-item .dropdown-menu{
    min-width:8rem;
}
.top-list-inline-item .dropdown-menu .dropdown-item{
    color: #212529;
    padding: .2rem 1rem;
}
.top-list-inline-item .dropdown-menu .dropdown-item:hover {
    background-color: #f0f4f7;
    color: #2b354f;
}

header {
    background: url('../images/bg-main.png') #fff no-repeat;
    background-position: 50% 70%;
    background-size: cover;
    position: relative;
}
header::after{
    content: '';
    background:rgba(255, 255, 255, .2);
    backdrop-filter: blur(3px);
    width:100%;
    height:100%;
    top:0;
    right:0;
    bottom:0;
    left:0;
    position: absolute;
}
header .nav-item {
    margin-left: 20px;
}
header .nav-item:first-child {
    margin-left: 0;
}
header .navbar-expand-lg .navbar-nav .nav-link {
    color: var(--gray);
    position: relative;
    transition: all 0.2s;
    padding: 5px 2px;
    line-height: 26px;
}
header .navbar-expand-lg .navbar-nav .nav-item.active .nav-link,
header .navbar-expand-lg .navbar-nav .nav-link:hover {
    color: var(--primary);
}
header .navbar-expand-lg .navbar-nav .nav-item.active .nav-link {
    font-weight: bold;
}
header .navbar-expand-lg .navbar-nav .nav-link:after {
    display: block;
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background-color: var(--primary);
    left: 50%;
    bottom: -18px;
    transform: translateX(-50%);
    transition: all 0.2s;
}
header .navbar-expand-lg .navbar-nav .nav-item.active .nav-link:after,
header .navbar-expand-lg .navbar-nav .nav-link:hover:after {
    height: 2px;
    width: 100%;
}
header .navbar-brand {
    padding-top: 7px;
    padding-bottom: 7px;
}
header .navbar-brand img{
    max-width: fit-content;
}
header .navbar {
    padding:5px 0 ;
    z-index: 1;
}

#wrapper {
    height: 100%;
    overflow: hidden;
    width: 100%;
}

.slimScrollDiv {
    height: auto !important;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
    background-color: #626ed4;
}

.dropdown-menu {
    padding: 4px 0;
    font-size: 14px;
    -webkit-box-shadow: 0px 0px 13px 0px rgba(236, 236, 241, 0.44);
    box-shadow: 0px 0px 13px 0px rgba(236, 236, 241, 0.44);
    background-color: #ffffff;
    border-color: #e9ecef;
    margin: 0;
}

.dropdown-item {
    background-color: #ffffff;
    padding: .55rem 1.5rem;
}

.dropdown-item:active, .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #2a3142;
}

.dropdown-item.active, .dropdown-item:active {
    background-color: #f8f9fa;
    color: #2a3142;
}

.breadcrumb > li + li:before {
    padding: 0 5px;
    color: #9ca8b3;
    content: "\f105" !important;
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.blockquote {
    padding: 10px 20px;
    margin-bottom: 20px;
    border-left: 4px solid #e9ecef;
}

.blockquote-reverse {
    border-left: 0;
    border-right: 4px solid #e9ecef;
    text-align: right;
}

.bg-primary {
    background-color: #626ed4 !important;
}

.bg-success {
    background-color: #02a499 !important;
}

.bg-info {
    background-color: #38a4f8 !important;
}

.bg-warning {
    background-color: #f8b425 !important;
}

.bg-danger {
    background-color: #ec4561 !important;
}

.bg-muted {
    background-color: #9ca8b3 !important;
}

.bg-white {
    background-color: #ffffff !important;
}

.bg-count {
    background: #f6f6f7;
    border-radius: .25rem;
    -webkit-border-radius: .25rem;
    -moz-border-radius: .25rem;
    -ms-border-radius: .25rem;
    -o-border-radius: .25rem;
}

.bg-login {
    background: url(../images/bg-login.jpg) no-repeat center;
    background-size: cover;
}

.text-white {
    color: #ffffff !important;
}

.text-danger {
    color: #ec4561 !important;
}

.text-muted {
    color: #9ca8b3 !important;
}

.text-primary {
    color: #626ed4 !important;
}

.text-warning {
    color: #f8b425 !important;
}

.text-success {
    color: #02a499 !important;
}

.text-info {
    color: #38a4f8 !important;
}

.text-dark {
    color: #2a3142 !important;
}

.badge {
    font-weight: 500;
}

.badge-primary {
    background-color: #626ed4;
}

.badge-success {
    background-color: #02a499;
}

.badge-info {
    background-color: #38a4f8;
}

.badge-warning {
    background-color: #f8b425;
    color: #ffffff;
}

.badge-danger {
    background-color: #ec4561;
}

.badge-dark {
    background-color: #2a3142;
}

dt {
    font-weight: 500;
}

.spinner-border, .spinner-grow {
    margin-right: 10px;
    margin-top: 10px;
}

.custom-control-input:checked ~ .custom-control-label:before {
    border-color: #626ed4;
    background-color: #626ed4;
}

.p-0 {
    padding: 0;
}

.p-t-10 {
    padding-top: 10px;
}

.p-b-10 {
    padding-bottom: 10px;
}

.m-0 {
    margin: 0;
}

.m-r-5 {
    margin-right: 5px;
}

.m-r-10 {
    margin-right: 10px;
}

.m-r-15 {
    margin-right: 15px;
}

.m-l-10 {
    margin-left: 10px;
}

.m-l-15 {
    margin-left: 15px;
}

.m-t-5 {
    margin-top: 5px;
}

.m-t-10 {
    margin-top: 10px;
}

.m-t-15 {
    margin-top: 15px;
}

.m-t-20 {
    margin-top: 20px;
}

.m-t-30 {
    margin-top: 30px;
}

.m-t-40 {
    margin-top: 40px;
}

.m-b-5 {
    margin-bottom: 5px;
}

.m-b-10 {
    margin-bottom: 10px;
}

.m-b-15 {
    margin-bottom: 15px;
}

.m-b-20 {
    margin-bottom: 20px;
}

.m-b-30 {
    margin-bottom: 30px;
}

.w-30 {
    max-width: 30px;
}

.w-xs {
    min-width: 80px;
}

.w-sm {
    min-width: 95px;
}

.w-md {
    min-width: 110px;
}

.w-lg {
    min-width: 140px;
}

.l-h-23 {
    line-height: 23px;
}

.l-h-34 {
    line-height: 34px;
}

.font-12 {
    font-size: 12px;
}

.font-13 {
    font-size: 13px;
}

.font-14 {
    font-size: 14px;
}

.font-16 {
    font-size: 16px;
}

.font-18 {
    font-size: 18px;
}

.font-20 {
    font-size: 20px;
}

.font-24 {
    font-size: 24px;
}

.font-30 {
    font-size: 30px;
}

.thumb-sm {
    height: 32px;
    width: 32px;
}

.thumb-md {
    height: 48px;
    width: 48px;
}

.thumb-lg {
    height: 88px;
    width: 88px;
}

.font-500 {
    font-weight: 500;
}

.font-600 {
    font-weight: 600;
}

.header-title {
    font-size: 16px;
}

.waves-effect {
    position: relative;
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.waves-effect .waves-ripple {
    position: absolute;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    opacity: 0;
    background: rgba(0, 0, 0, 0.2);
    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
    -webkit-transition: all 0.5s ease-out;
    transition: all 0.5s ease-out;
    -webkit-transition-property: -webkit-transform, opacity;
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
    -webkit-transform: scale(0) translate(0, 0);
    transform: scale(0) translate(0, 0);
    pointer-events: none;
}

.waves-effect.waves-light .waves-ripple {
    background: rgba(255, 255, 255, 0.4);
    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.waves-effect.waves-classic .waves-ripple {
    background: rgba(0, 0, 0, 0.2);
}

.waves-effect.waves-classic.waves-light .waves-ripple {
    background: rgba(255, 255, 255, 0.4);
}

.waves-notransition {
    -webkit-transition: none !important;
    transition: none !important;
}

.waves-button, .waves-circle {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
}

.waves-button, .waves-button:hover, .waves-button:visited, .waves-button-input {
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: none;
    outline: none;
    color: inherit;
    background-color: rgba(0, 0, 0, 0);
    font-size: 1em;
    line-height: 1em;
    text-align: center;
    text-decoration: none;
    z-index: 1;
}

.waves-button {
    padding: 0.85em 1.1em;
    border-radius: 0.2em;
}

.waves-button-input {
    margin: 0;
    padding: 0.85em 1.1em;
}

.waves-input-wrapper {
    border-radius: 0.2em;
    vertical-align: bottom;
}

.waves-input-wrapper.waves-button {
    padding: 0;
}

.waves-input-wrapper .waves-button-input {
    position: relative;
    top: 0;
    left: 0;
    z-index: 1;
}

.waves-circle {
    text-align: center;
    width: 2.5em;
    height: 2.5em;
    line-height: 2.5em;
    border-radius: 50%;
}

.waves-float {
    -webkit-mask-image: none;
    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
    -webkit-transition: all 300ms;
    transition: all 300ms;
}

.waves-float:active {
    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
}

.waves-block {
    display: block;
}

.container-fluid {
    max-width: 1300px;
}

.main-box {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
    max-width: 100%;
}

.main-bg {
    position: relative;
}

.wrapper {
    padding: 15px 0;
    min-height: calc(100vh - 116px);
    background-color: #f8f8fa;
}

.wrapper.main-bg {
    background: none;
}

.main-bg .dark-box {
    background-image: radial-gradient(circle farthest-corner at 10% 20%, rgba(69, 86, 102, .65) 0%, rgba(34, 34, 34, .65) 90%);
}
.main-wrapper{
    background: url('../images/bg-main-elem.png') no-repeat;
    background-size: cover;
    background-position: 50% 25%;
}
.main-content {
    width: 100%;
    max-width: 1140px;
    margin: 0 auto;
    padding: 1.5rem 2.5rem .5rem;
}

.main-content p {
    color: var(--dark);
    /*font-size: .95rem;*/
    line-height: 1.8;
    text-align: justify;
    /*text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);*/
    opacity: .8;
}

.main-content .input-group, .main-content .eg-info {
    max-width: 600px;
    margin: auto;
}

/*.main-content .input-group .form-control {
    height: 3.2rem;
    border-radius: 1.6rem 0 0 1.6rem;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    font-size: 1.125rem;
    border-color: #fff;
}
.main-content .input-group .form-control:focus {
    border-color: #fff;
}

.main-content .input-group {
    background-color: #fff;
    border-radius: 1.6rem;
}
.main-content .input-group .btn {
    padding: .375rem 1.25rem;
    font-size: 1rem;
    border-radius: 1.6rem;
    background-color: #3c5066;
}
.main-content .input-group .btn:hover {
    color: #fff;
    background-color: #304358;
}*/
.home-search {
    background-color: #fff;
    padding: 10px 70px 10px 25px;
    border-radius: 28px;
    position: relative;
    box-shadow: 0 5px 10px rgba(0,0,0,0.05);
}

.home-search .form-control {
    border: 0;
    padding: 0;
    font-size: 16px;
}

.home-search .input-group-append {
    position: absolute;
    top: 3px;
    right: 3px;
    width: 52px;
    height: 52px;
}

.home-search .input-group-append .btn {
    width: 100%;
    height: 100%;
    border-radius: 26px;
    background-color: #4671c5;
    color: #fff;
    font-size: 18px;
}

.main-content .eg-info {
    color: var(--gray);
    margin-top: .2rem;
    text-align: center;
    font-size: 14px;
}

.main-content .eg-info p {
    /*font-size: .725rem;*/
}

.main-content .eg-info a {
    padding-left: .25rem;
    padding-right: .25rem;
    color: var(--primary);
}

.topbar-custom .nav-link {
    display: block;
    color: rgba(255, 255, 255, 0.7);
    transition: all .5s ease;
}

.topbar-custom .nav-link:hover {
    color: #fff;
}

.topbar-custom .dropdown-toggle:after {
    content: initial;
}

.topbar-custom .list-inline-item > a {
    color: rgba(255, 255, 255, 0.7);
    transition: all .3s;
    -webkit-transition: all .3s;
    -moz-transition: all .3s;
    -ms-transition: all .3s;
    -o-transition: all .3s;
}

.topbar-custom .list-inline-item > a:hover {
    color: #fff;
}

.notification-item-list {
    max-height: 230px;
}

.notification-list.list-inline-item:not(:last-child) {
    margin-right: 0;
}

.notification-list .noti-icon {
    font-size: 24px;
    vertical-align: middle;
}

.notification-list .noti-icon-badge {
    display: inline-block;
    position: absolute;
    top: 11px;
    right: 10px;
}

.notification-list .notify-item {
    padding: 10px 20px;
}

.notification-list .notify-item .notify-icon {
    float: left;
    height: 36px;
    width: 36px;
    text-align: center;
    margin-right: 10px;
    border-radius: 50%;
}

.notification-list .notify-item .notify-icon i {
    line-height: 36px;
    color: #ffffff;
}

.notification-list .notify-item .notify-details {
    margin-bottom: 0;
    overflow: hidden;
    margin-left: 45px;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
    font-family: "Sarabun", sans-serif;
}

.notification-list .notify-item .notify-details span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    font-size: 12px;
    font-weight: normal;
}

.notification-list .dropdown-menu {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.notification-list .language-switch a img {
    float: right;
}

.notification-list .profile-dropdown .notify-item {
    padding: 4px 20px;
}

.notification-list .nav-link {
    padding: 0 15px;
}

.profile-dropdown {
    width: 120px;
    min-width: 120px;
}

.profile-dropdown .dropdown-item {
    padding: .25rem .55rem;
}

.profile-dropdown i {
    font-size: 17px;
    vertical-align: middle;
    margin-right: 5px;
    color: #6c757d;
}

.nav-user img {
    height: 30px;
    width: 30px;
}

.arrow-none:after {
    border: none;
    margin: 0;
    display: none;
}

.dropdown-menu-lg {
    width: 300px;
}

.app-search {
    position: relative;
}

.app-search .form-control, .app-search .form-control:focus {
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 13px;
    height: 34px;
    padding-left: 18px;
    padding-right: 40px;
    margin-right: 14px;
    background: rgba(255, 255, 255, 0.1);
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 30px;
    width: 200px;
    color: #ffffff;
}

.app-search button {
    position: absolute;
    top: 8px;
    right: 26px;
    display: block;
    color: rgba(248, 249, 250, 0.5);
    font-size: 11px;
    border: none;
    background-color: transparent;
}

.app-search input::-webkit-input-placeholder {
    color: #dee2e6;
}

.app-search input:-moz-placeholder {
    color: #adb5bd;
}

.app-search input::-moz-placeholder {
    color: #adb5bd;
}

.app-search input:-ms-input-placeholder {
    color: #adb5bd;
}

.page-title-box {
    padding: 5px 0px;
}

.page-title-box .page-title {
    font-size: 18px;
    margin: 0;
    line-height: 30px;
    font-weight: 700;
}

.page-title-box .breadcrumb {
    padding: 4px 0;
    background-color: transparent;
    margin-bottom: 0;
}

.page-title-box .breadcrumb a {
    color: #2a3142;
}

.page-title-box .breadcrumb a:hover {
    color: rgba(42, 49, 66, 0.9);
}

.page-title-box .breadcrumb .active {
    color: rgba(42, 49, 66, 0.7);
}

/* Responsive Menu */
@media (min-width: 992px) {
    header .topbar-main {
        padding: 10px 0px;
    }


    header .navigation-menu > li.last-elements .submenu {
        left: auto;
        right: 0;
    }

    header .navigation-menu > li.last-elements .submenu > li.has-submenu .submenu {
        left: auto;
        right: 100%;
        margin-left: 0;
        margin-right: 10px;
    }

    header .navigation-menu > li:hover a i {
        color: #ffffff;
    }

    header .navigation-menu > li .submenu {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;
        padding: 8px 0;
        list-style: none;
        min-width: 150px;
        text-align: left;
        visibility: hidden;
        opacity: 0;
        margin-top: 20px;
        -webkit-transition: all .2s ease;
        transition: all .2s ease;
        background-color: #ffffff;
        -webkit-box-shadow: 0 1px 12px rgba(0, 0, 0, 0.1);
        box-shadow: 0 1px 12px rgba(0, 0, 0, 0.1);
    }

    header .navigation-menu > li .submenu.megamenu {
        white-space: nowrap;
        width: auto;
    }

    header .navigation-menu > li .submenu.megamenu > li {
        overflow: hidden;
        width: 200px;
        display: inline-block;
        vertical-align: top;
    }

    header .navigation-menu > li .submenu > li.has-submenu > a:after {
        content: "\56";
        font-family: "dripicons-v2";
        position: absolute;
        right: 20px;
        top: 9px;
        font-size: 12px;
    }

    header .navigation-menu > li .submenu > li .submenu {
        left: 100%;
        top: 0;
        margin-top: 10px;
    }

    header .navigation-menu > li .submenu li {
        position: relative;
    }

    header .navigation-menu > li .submenu li ul {
        list-style: none;
        padding-left: 0;
        margin: 0;
    }

    header .navigation-menu > li .submenu li a {
        display: block;
        padding: 5px 25px;
        clear: both;
        white-space: nowrap;
        font-size: 14px;
        color: rgba(42, 49, 66, 0.7);
    }

    header .navigation-menu > li .submenu li a:hover {
        color: #626ed4;
    }

    header .navigation-menu > li .submenu li span {
        display: block;
        padding: 8px 25px;
        clear: both;
        line-height: 1.42857143;
        white-space: nowrap;
        font-size: 10px;
        text-transform: uppercase;
        letter-spacing: 2px;
        font-weight: 500;
        color: rgba(42, 49, 66, 0.7);
    }

    header .navbar-toggle {
        display: none;
    }
}

@media (max-width: 991px) {
    .container-fluid {
        width: auto !important;
    }

    header .navbar-right {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
    }

    header .notification-list .app-search {
        margin-top: 14px;
    }

    header .navigation-menu {
        float: none;
        max-height: 400px;
        text-align: left;
    }

    header .navigation-menu > li {
        display: block;
    }

    header .navigation-menu > li > a i {
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 0;
        vertical-align: inherit;
    }

    header .navigation-menu > li > a:after {
        position: absolute;
        right: 15px;
    }

    header .navigation-menu > li .submenu {
        display: none;
        list-style: none;
        padding-left: 20px;
        margin: 0;
    }

    header .navigation-menu > li .submenu li a {
        display: block;
        position: relative;
        padding: 7px 20px;
        color: rgba(42, 49, 66, 0.7);
    }

    header .navigation-menu > li .submenu li a:hover {
        color: #626ed4;
    }

    header .navigation-menu > li .submenu li.has-submenu > a:after {
        content: "\54";
        font-family: "dripicons-v2";
        position: absolute;
        right: 30px;
    }

    header .navigation-menu > li .submenu.open {
        display: block;
    }

    header .navigation-menu > li .submenu .submenu {
        display: none;
        list-style: none;
    }

    header .navigation-menu > li .submenu .submenu.open {
        display: block;
    }

    header .navigation-menu > li .submenu.megamenu > li > ul {
        list-style: none;
        padding-left: 0;
    }

    header .navigation-menu > li .submenu.megamenu > li > ul > li > span {
        display: block;
        position: relative;
        padding: 15px;
        text-transform: uppercase;
        font-size: 11px;
        letter-spacing: 2px;
        color: rgba(42, 49, 66, 0.7);
    }

    header .navigation-menu > li.has-submenu.open > a {
        color: #626ed4;
    }

    #navigation {
        position: absolute;
        top: 60px;
        left: 0;
        right: 0;
        display: none;
        height: auto;
        padding-bottom: 0;
        overflow: auto;
        border-top: 1px solid #e7e7e7;
        border-bottom: 1px solid #e7e7e7;
        background-color: #ffffff;
    }

    #navigation.open {
        display: block;
        overflow-y: auto;
    }

    .main-content {
        width: 100%;
        padding: 1rem 2rem;
    }

    .main-content p {
        font-size: .8rem;
    }

    .topbar-custom .list-inline-item > a {
        font-size: .625rem;
        line-height: 60px;
    }

    .topbar-custom .nav-link {
        color: #fff;
        font-size: .625rem;
        line-height: 3.5rem;
    }
}

@media (max-width: 620px) {
    .logo-large {
        display: none;
    }

    .logo-small {
        display: inline-block !important;
    }
}

@media (min-width: 768px) {
    header .navigation-menu > li.has-submenu:hover > .submenu {
        visibility: visible;
        opacity: 1;
        margin-top: 0;
    }

    header .navigation-menu > li.has-submenu:hover > .submenu > li.has-submenu:hover > .submenu {
        visibility: visible;
        opacity: 1;
        margin-top: -1px;
        margin-right: 0;
    }

    .navbar-toggle {
        display: block;
    }
}

.footer {
    padding: 10px 20px;
    color: #c4c4c4;
    text-align: center;
    background-color: #424242;
    -webkit-box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.05);
    box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.05);
}

.wrapper.main-bg + .footer {
    color: #c4c4c4;
    background-color: rgba(111, 111, 111, 0.4);
}

.footer .container-fluid p {
    font-size: 14px;
    text-align: center;
    margin-bottom: 0;
}

.btn {
    border-radius: 3px;
    font-size: 14px;
}

button:focus {
    outline: none;
}

.btn-sm {
    font-size: 11.66667px;
}

.btn-lg {
    font-size: 16.8px;
}

.btn-primary, .btn-success, .btn-info, .btn-warning, .btn-danger, .btn-dark, .btn-pink, .btn-purple, .btn-indigo, .btn-teal, .btn-lime, .btn-orange, .btn-brown, .btn-blue-grey {
    color: #ffffff;
}

.btn-primary {
    background-color: #626ed4;
    border: 1px solid #626ed4;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .btn-primary.focus, .btn-primary:active, .btn-primary:focus, .btn-primary:hover, .open > .dropdown-toggle.btn-primary, .btn-outline-primary.active, .btn-outline-primary:active, .show > .btn-outline-primary.dropdown-toggle, .btn-outline-primary:hover, .btn-primary.active, .btn-primary:active, .show > .btn-primary.dropdown-toggle, .btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show > .btn-primary.dropdown-toggle, .btn-outline-primary:not([disabled]):not(.disabled).active, .btn-outline-primary:not([disabled]):not(.disabled):active, .show > .btn-outline-primary.dropdown-toggle {
    background-color: #4e5ccf;
    border: 1px solid #4e5ccf;
}

.btn-primary.focus, .btn-primary:focus, .btn-primary.active, .btn-primary:active {
    box-shadow: inset 0 3px 12px -2px rgba(0, 0, 0, .4);
}

.btn-primary.focus, .btn-primary:focus, .btn-outline-primary.focus, .btn-outline-primary:focus, .btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-primary.dropdown-toggle:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(98, 110, 212, 0.3);
    box-shadow: 0 0 0 2px rgba(98, 110, 212, 0.3);
}

.btn-secondary.focus, .btn-secondary:focus, .btn-outline-secondary.focus, .btn-outline-secondary:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-secondary.dropdown-toggle:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(42, 49, 66, 0.3);
    box-shadow: 0 0 0 2px rgba(42, 49, 66, 0.3);
}

.btn-success {
    background-color: #02a499;
    border: 1px solid #02a499;
}

.btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active, .btn-success.focus, .btn-success:active, .btn-success:focus, .btn-success:hover, .open > .dropdown-toggle.btn-success, .btn-outline-success.active, .btn-outline-success:active, .show > .btn-outline-success.dropdown-toggle, .btn-outline-success:hover, .btn-success.active, .btn-success:active, .show > .btn-success.dropdown-toggle, .btn-success:not(:disabled):not(.disabled).active:focus, .btn-success:not(:disabled):not(.disabled):active:focus, .show > .btn-success.dropdown-toggle:focus, .btn-outline-success:not([disabled]):not(.disabled).active, .btn-outline-success:not([disabled]):not(.disabled):active, .show > .btn-outline-success.dropdown-toggle {
    background-color: #028b81;
    border: 1px solid #028b81;
}

.btn-success.focus, .btn-success:focus, .btn-outline-success.focus, .btn-outline-success:focus, .btn-success:not(:disabled):not(.disabled).active:focus, .btn-success:not(:disabled):not(.disabled):active:focus, .show > .btn-success.dropdown-toggle:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus, .btn-outline-success:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-success.dropdown-toggle:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(2, 164, 153, 0.3);
    box-shadow: 0 0 0 2px rgba(2, 164, 153, 0.3);
}

.btn-info {
    background-color: #38a4f8;
    border: 1px solid #38a4f8;
}

.btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active, .btn-info.focus, .btn-info:active, .btn-info:focus, .btn-info:hover, .open > .dropdown-toggle.btn-info, .btn-outline-info.active, .btn-outline-info:active, .show > .btn-outline-info.dropdown-toggle, .btn-outline-info:hover, .btn-info.active, .btn-info:active, .show > .btn-info.dropdown-toggle, .btn-info:not(:disabled):not(.disabled).active, .btn-info:not(:disabled):not(.disabled):active, .show > .btn-info.dropdown-toggle, .btn-outline-info:not([disabled]):not(.disabled).active, .btn-outline-info:not([disabled]):not(.disabled):active, .show > .btn-outline-info.dropdown-toggle {
    background-color: #1f99f7;
    border: 1px solid #1f99f7;
}

.btn-info.focus, .btn-info:focus, .btn-outline-info.focus, .btn-outline-info:focus, .btn-info:not(:disabled):not(.disabled).active:focus, .btn-info:not(:disabled):not(.disabled):active:focus, .show > .btn-info.dropdown-toggle:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus, .btn-outline-info:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-info.dropdown-toggle:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(56, 164, 248, 0.3);
    box-shadow: 0 0 0 2px rgba(56, 164, 248, 0.3);
}

.btn-warning {
    background-color: #f8b425;
    border: 1px solid #f8b425;
}

.btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .btn-warning.focus, .btn-warning:active, .btn-warning:focus, .btn-warning:hover, .open > .dropdown-toggle.btn-warning, .btn-outline-warning.active, .btn-outline-warning:active, .show > .btn-outline-warning.dropdown-toggle, .btn-outline-warning:hover, .btn-warning.active, .btn-warning:active, .show > .btn-warning.dropdown-toggle, .btn-warning:not(:disabled):not(.disabled).active, .btn-warning:not(:disabled):not(.disabled):active, .show > .btn-warning.dropdown-toggle, .btn-outline-warning:not([disabled]):not(.disabled).active, .btn-outline-warning:not([disabled]):not(.disabled):active, .show > .btn-outline-warning.dropdown-toggle {
    background-color: #f7ac0c;
    border: 1px solid #f7ac0c;
    color: #ffffff;
}

.btn-warning.focus, .btn-warning:focus, .btn-outline-warning.focus, .btn-outline-warning:focus, .btn-warning:not(:disabled):not(.disabled).active:focus, .btn-warning:not(:disabled):not(.disabled):active:focus, .show > .btn-warning.dropdown-toggle:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus, .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-warning.dropdown-toggle:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(248, 180, 37, 0.3);
    box-shadow: 0 0 0 2px rgba(248, 180, 37, 0.3);
}

.btn-danger {
    background-color: #ec4561;
    border: 1px solid #ec4561;
}

.btn-danger:active, .btn-danger:focus, .btn-danger:hover, .btn-danger.active, .btn-danger.focus, .btn-danger:active, .btn-danger:focus, .btn-danger:hover, .open > .dropdown-toggle.btn-danger, .btn-outline-danger.active, .btn-outline-danger:active, .show > .btn-outline-danger.dropdown-toggle, .btn-outline-danger:hover, .btn-danger.active, .btn-danger:active, .show > .btn-danger.dropdown-toggle, .btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active, .show > .btn-danger.dropdown-toggle, .btn-outline-danger:not([disabled]):not(.disabled).active, .btn-outline-danger:not([disabled]):not(.disabled):active, .show > .btn-outline-danger.dropdown-toggle {
    background-color: #ea2e4d;
    border: 1px solid #ea2e4d;
}

.btn-danger.focus, .btn-danger:focus, .btn-outline-danger.focus, .btn-outline-danger:focus, .btn-danger:not(:disabled):not(.disabled).active:focus, .btn-danger:not(:disabled):not(.disabled):active:focus, .show > .btn-danger.dropdown-toggle:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus, .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-danger.dropdown-toggle:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(236, 69, 97, 0.3);
    box-shadow: 0 0 0 2px rgba(236, 69, 97, 0.3);
}

.btn-dark {
    background-color: #2a3142;
    border: 1px solid #2a3142;
    color: #ffffff;
}

.btn-dark:hover, .btn-dark:focus, .btn-dark:active, .btn-dark.active, .btn-dark.focus, .btn-dark:active, .btn-dark:focus, .btn-dark:hover, .open > .dropdown-toggle.btn-dark, .btn-outline-dark.active, .btn-outline-dark:active, .show > .btn-outline-dark.dropdown-toggle, .btn-outline-dark:hover, .btn-dark:not(:disabled):not(.disabled).active, .btn-dark:not(:disabled):not(.disabled):active, .show > .btn-dark.dropdown-toggle, .btn-outline-dark:not([disabled]):not(.disabled).active, .btn-outline-dark:not([disabled]):not(.disabled):active, .show > .btn-outline-dark.dropdown-toggle {
    background-color: #202532;
    border: 1px solid #202532;
    color: #ffffff;
}

.btn-dark.focus, .btn-dark:focus, .btn-outline-dark.focus, .btn-outline-dark:focus, .btn-dark:not(:disabled):not(.disabled).active:focus, .btn-dark:not(:disabled):not(.disabled):active:focus, .show > .btn-dark.dropdown-toggle:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus, .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-dark.dropdown-toggle:focus {
    -webkit-box-shadow: 0 0 0 2px rgba(42, 49, 66, 0.3);
    box-shadow: 0 0 0 2px rgba(42, 49, 66, 0.3);
}

.btn-link {
    color: #2a3142;
}

.btn-link:hover {
    color: #626ed4;
}

/* button Outline */
.btn-outline-primary {
    color: #626ed4;
    border-color: #626ed4;
}

.btn-outline-success {
    color: #02a499;
    border-color: #02a499;
}

.btn-outline-info {
    color: #38a4f8;
    border-color: #38a4f8;
}

.btn-outline-warning {
    color: #f8b425;
    border-color: #f8b425;
}

.btn-outline-danger {
    color: #ec4561;
    border-color: #ec4561;
}

.btn-outline-dark {
    color: #2a3142;
    background-image: none;
    background-color: transparent;
    border-color: #2a3142;
}

.btn-rounded {
    border-radius: 30px;
}

.btn-icon {
    position: relative;
}

.btn-icon .btn-icon-label {
    margin: -.55rem .9rem -.55rem -.9rem;
    padding: .6rem .9rem;
}

.btn-icon::before {
    content: "";
    position: absolute;
    left: 0px;
    top: 0px;
    bottom: 0px;
    width: 38%;
    background-color: rgba(255, 255, 255, 0.15);
    -webkit-clip-path: polygon(0% 0%, 75% 0%, 100% 50%, 75% 100%, 0% 100%);
    clip-path: polygon(0% 0%, 75% 0%, 100% 50%, 75% 100%, 0% 100%);
    -webkit-transition: all 0.4s;
    transition: all 0.4s;
}

.btn-icon:hover::before {
    width: 100%;
    -webkit-clip-path: polygon(0% 0%, 100% 0%, 100% 50%, 100% 100%, 0% 100%);
    clip-path: polygon(0% 0%, 100% 0%, 100% 50%, 100% 100%, 0% 100%);
}

.card {
    border: none;
    -webkit-box-shadow: 0px 0px 13px 0px rgba(236, 236, 241, 0.44);
    box-shadow: 0px 0px 13px 0px rgba(236, 236, 241, 0.44);
    margin-bottom: 15px;
}

.card-box .card-body {
    padding: 1rem;
    font-size: .875rem;
}

.card-box .header-title, .card-box small {
    border-left: 2px solid #626ed4;
    padding-left: .5rem;
}

.card-box .header-title small {
    border: none;
}

@media (min-width: 576px) {
    .card-columns {
        -webkit-column-gap: 30px;
        column-gap: 30px;
    }
}

.card-columns .card {
    margin-bottom: 30px;
}

#chart-area, #chart-area-china, #chart-area-overseas, #chart-trend, #chart-trend-Addconfirm, #chart-map, #chart-count, #chart-count-case {
    background: #f6f6f7;
    border-radius: .25rem;
    -webkit-border-radius: .25rem;
    -moz-border-radius: .25rem;
    -ms-border-radius: .25rem;
    -o-border-radius: .25rem;
    width: 100%;
    height: 500px;
    margin-bottom: .75rem;
}

#chart-area, #chart-area-china, #chart-area-overseas, #chart-trend-Addconfirm {
    height: 300px;
}

#chart-count {
    height: 400px;
}

#chart-trend {
    height: 990px;
}

#chart-count-case {
    height: 630px;
}

.updata-form .col-form-label, .ana-form .col-form-label {
    text-align: right;
}

.ana-form .btn-outline-primary.active + .btn-outline-primary.active {
    border-left: 1px solid #454e98;
}

.ana-form .input-group > .form-control {
    border-radius: .25rem;
}

.ana-form .input-group .input-group-text {
    background-color: transparent;
    border-color: transparent;
}

.ana-form .seled {
    background: #eff1f3;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    font-size: 12px;
    font-style: normal;
    padding: .375rem .65rem;
}

.ana-form .seled b {
    font-weight: 600;
    width: calc(100% - 82px);
    display: inline-block;
    vertical-align: middle;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* CSS Switch */
input[switch] {
    display: none;
}

input[switch] + label {
    font-size: 1em;
    line-height: 1;
    width: 56px;
    height: 24px;
    background-color: #dee2e6;
    background-image: none;
    border-radius: 2rem;
    padding: 0.16667rem;
    cursor: pointer;
    display: inline-block;
    text-align: center;
    position: relative;
    font-weight: 500;
    -webkit-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
}

input[switch] + label:before {
    color: #2b354f;
    content: attr(data-off-label);
    display: block;
    font-family: inherit;
    font-weight: 500;
    font-size: 12px;
    line-height: 21px;
    position: absolute;
    right: 1px;
    margin: 3px;
    top: -2px;
    text-align: center;
    min-width: 1.66667rem;
    overflow: hidden;
    -webkit-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
}

input[switch] + label:after {
    content: '';
    position: absolute;
    left: 3px;
    background-color: #e9ecef;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 2rem;
    height: 20px;
    width: 20px;
    top: 2px;
    -webkit-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
}

input[switch]:checked + label {
    background-color: #4090cb;
}

input[switch]:checked + label:before {
    color: #ffffff;
    content: attr(data-on-label);
    right: auto;
    left: 3px;
}

input[switch]:checked + label:after {
    left: 33px;
    background-color: #e9ecef;
}

input[switch="bool"] + label {
    background-color: #e74c5e;
}

input[switch="bool"] + label:before, input[switch="bool"]:checked + label:before, input[switch="default"]:checked + label:before {
    color: #ffffff;
}

input[switch="bool"]:checked + label {
    background-color: #47bd9a;
}

input[switch="default"]:checked + label {
    background-color: #a2a2a2;
}

input[switch="primary"]:checked + label {
    background-color: #4090cb;
}

input[switch="success"]:checked + label {
    background-color: #47bd9a;
}

input[switch="info"]:checked + label {
    background-color: #06c2de;
}

input[switch="warning"]:checked + label {
    background-color: #f9d570;
}

input[switch="danger"]:checked + label {
    background-color: #e74c5e;
}

input[switch="dark"]:checked + label {
    background-color: #2b354f;
}

label {
    font-weight: 500;
}

.custom-select,
.form-control {
    font-size: 14px;
}

.form-control:focus {
    border-color: #626ed4;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.form-control-sm {
    font-size: .875rem;
}

.custom-select-sm {
    font-size: 75%;
}

.custom-control-input:checked ~ .custom-control-indicator {
    background-color: #626ed4;
}

.custom-control-input:focus ~ .custom-control-indicator {
    -webkit-box-shadow: 0 0 0 1px #ffffff, 0 0 0 3px #626ed4;
    box-shadow: 0 0 0 1px #ffffff, 0 0 0 3px #626ed4;
}

.has-success .form-control {
    border-color: #02a499;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.has-warning .form-control {
    border-color: #f8b425;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.has-error .form-control {
    border-color: #ec4561;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.input-group-text {
    font-size: 14px;
}

.other-item {
    display: flex;
    margin-bottom: .5rem;
}

.other-item .form-control {
    display: flex;
    flex: 1;
    margin-right: 5px;
}

.nav-tabs .nav-link, .nav-pills .nav-link {
    color: #2a3142;
    font-weight: 500;
}

.nav-tabs-custom {
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    padding-bottom: 12px;
    display: flex;
}

.nav-tabs-custom li {
    margin-right: 10px;
}

.nav-tabs-custom li > a {
    color: #50586d;
    font-size: 15px;
    padding: 7px 15px;
    background-color: #e5e5e5;
    border-radius: 4px;
    display: block;
    position: relative;
    transition: all 0.3s;
}

.nav-tabs-custom li > a:hover,
.nav-tabs-custom li.active > a {
    background-color: #626ed4;
    color: #fff;
}

.nav-tabs-custom li.active > a:before,
.nav-tabs-custom li.active > a:after {
    display: block;
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    bottom: -13px;
    left: 50%;
    margin-left: -7px;
    border-width: 7px;
    border-style: solid;
    border-color: transparent transparent #ddd transparent;
}

.nav-tabs-custom li.active > a:after {
    bottom: -15px;
    border-bottom-color: #fff;
}

.nav-tabs-custom-1 {
    justify-content: center;
}

.nav-tabs-custom-1 .nav-item + .nav-item {
    margin-left: -1px;
}

.nav-tabs-custom-1 .nav-link {
    border: 1px solid #ddd;
    color: var(--gray);
    transition: all 0.3s;
}

.nav-tabs-custom-1 .nav-item:first-child .nav-link {
    border-radius: 21px 0 0 21px;
}

.nav-tabs-custom-1 .nav-item:last-child .nav-link {
    border-radius: 0 21px 21px 0;
}

.nav-tabs-custom-1 .nav-link:hover,
.nav-tabs-custom-1 .nav-link.active {
    background-color: #626ed4;
    border-color: #626ed4;
    color: #fff;
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
    background: #626ed4;
}

#accordion .card {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    border: 1px solid #e9ecef;
}

#accordion .card .card-header {
    border-bottom: 1px solid #e9ecef;
}

#case-table td, #case-table th {
    padding: .5rem;
}

#case-table-1 td, #case-table-1 th {
    padding: .5rem;
}

.alert-danger {
    color: #c33d49;
}

.table-hover tbody tr:hover, .table-striped tbody tr:nth-of-type(odd), .thead-default th {
    background-color: #f8f9fa;
}

.table td, .table th {
    vertical-align: middle;
}

.table-vertical td {
    vertical-align: middle;
}

.table-sm th, .table-sm td {
    padding: .3rem !important;
}

.table-nowrap th, .table-nowrap td {
    white-space: nowrap;
}

.table.table-task th, .table-case-summary th, .table.table-task td, .table-case-summary td {
    padding: .35rem
}

.table.table-task th {
    padding-top: .6rem;
    padding-bottom: .6rem;
}

.table.table-task .action a {
    margin: 0 4px;
}

.table-case-summary th {
    background: #f6f6fd;
}

.symptom-info {
    white-space: normal;
    word-break: break-all;
    width: 300px;
}

.detail-list dt {
    font-weight: 600;
    text-align: right;
}

.detail-list dd a {
    color: #38a4f8
}

.info-box {
    background: #fbfcff;
    padding: .25rem 1rem;
    border: 1px solid #f3f3f3;
    border-radius: .35rem;
    margin-bottom: 1rem;
}
.info-box .img-info{
    aspect-ratio: 12 / 2;
    object-fit: contain;
}
table.treetable {
    border-color: #dee2e6;
}

#case-table tbody td:nth-child(2), #case-count tbody td:nth-child(2) {
    max-width: 40px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#case-table-1 tbody td:nth-child(1), #case-count tbody td:nth-child(2) {
    max-width: 40px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.badge-soft-dark {
    background-color: rgba(43, 53, 79, 0.2);
    color: #2b354f;
}

.list-files {
    width: 280px;
    overflow: hidden;
    display: block;
    position: relative;
}

a.list-files {
    color: #212529
}

a.list-files[data-toggle=collapse].collapsed:before {
    content: '\F419';
}

a.list-files[data-toggle=collapse]:before {
    content: '\F377';
    display: block;
    font-family: 'Material Design Icons';
    font-size: 16px;
    font-weight: 600;
    color: #6d869c;
    position: absolute;
    right: 0;
    top: 50%;
    transition: all .3s;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}

.list-files-item {
    cursor: pointer;
}

.list-files-item p {
    line-height: 1.4;
    position: relative;
    display: flex;
}

.list-files-item span, .list-files-item em {
    padding: .15rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list-files-item span {
    background: #fff;
    color: #626ed4;
    font-weight: 600;
    width: 80px;
    border-radius: .2rem 0 0 .2rem;
    border: 1px solid #dee2e6;
    border-right: none;
}

.list-files-item em {
    background: #dee2e6;
    width: 45px;
    font-style: normal;
    font-size: smaller;
    border-radius: 0 .2rem .2rem 0;
    padding-top: .25rem;
}

.list-files-item + .list-files-item {
    margin-top: .25rem;
}

.list-files-content {
    padding-top: .25rem;
    position: relative;
    width: 280px;
    overflow: hidden;
}

.tooltip-inner {
    font-size: 12px;
}

.wrapper-page {
    margin: 0 auto;
    padding: 6% 0 0;
    max-width: 492px;
    position: relative;
}

.account-card .account-card-content {
    padding: 30px;
}

.form-task {
    margin-bottom: .5rem;
    position: relative;
}

.form-task .input-daterange .form-control.form-control-sm {
    font-size: 12px;
    height: calc(1.5em + .5rem + 2px);
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .5rem;
    width: 100px;
}

.form-task .input-group-append {
    margin-left: -1px;
    margin-right: -1px;
}

.form-task .input-daterange .input-group-text {
    padding: .1rem .3rem;
}

.form-task .btn-primary {
    margin-left: 1rem;
}

.datepicker {
    z-index: 99;
}

.datepicker > div {
    font-size: .725rem;
}

.fa-rotate-45 {
    display: inline-block;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}

.fa-rotate-135 {
    display: inline-block;
    -webkit-transform: rotate(135deg);
    -moz-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    -o-transform: rotate(135deg);
    transform: rotate(135deg);
}

#d3_layout_phylotree_context_menu {
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    list-style: none;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    background-clip: padding-box;
}

#d3_layout_phylotree_context_menu > li > a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.428571429;
    color: #333;
    white-space: nowrap;
}

#d3_layout_phylotree_context_menu > li > a:hover {
    color: #fff;
    background-color: #357ebd;
    background-image: -webkit-gradient(linear, left 0, left 100%, from(#428bca), to(#357ebd));
    background-image: -webkit-linear-gradient(top, #428bca, 0%, #357ebd, 100%);
    background-image: -moz-linear-gradient(top, #428bca 0, #357ebd 100%);
    background-image: linear-gradient(to bottom, #428bca 0, #357ebd 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff428bca', endColorstr='#ff357ebd', GradientType=0);
}

#d3_layout_phylotree_context_menu .dropdown-header {
    display: block;
    padding: 3px 20px;
    font-size: 12px;
    line-height: 1.428571429;
    color: #999;
}

.selection_set a {
    padding: .55rem 1.5rem;
}

.js-hidden {
    display: none;
}

.alert.alert-wth-icon {
    padding-left: 65px;
    position: relative;
}

.alert.alert-wth-icon .alert-icon-wrap {
    display: block;
    text-align: center;
    padding-top: 15px;
    height: 100%;
    width: 65px;
    left: 0;
    top: 0;
    position: absolute;
}

@media (max-width: 768px) {
    body {
        padding-bottom: 120px;
    }

    #chart-map {
        height: 380px
    }

    .updata-form .col-form-label, .ana-form .col-form-label {
        text-align: left;
    }

    .updata-form .row .col-sm-6 + .col-sm-6 {
        margin-top: .75rem;
    }

    .updata-form .offset-4 {
        text-align: right;
    }

    .detail-header .btn-icon {
        float: right;
    }

    .detail-list dt {
        text-align: left;
    }

    .sel-ele + .sel-ele {
        margin-top: .625rem;
    }

    .wrapper-page {
        margin-top: 100px;
    }

    .action-del .btn {
        position: absolute;
        right: 16px;
        top: -65px;
        line-height: 58px;
        width: 60px;
        height: 66px;
    }

    .form-task label {
        font-size: .825rem;
        margin: .5rem 0 !important;
    }

    .form-task .btn-primary {
        margin-left: auto;
        margin-top: .5rem;
        width: 100%;
    }
}

.research-row {
    display: flex;
    margin: 0 -15px
}

.research-left {
    padding-left: 25px;
    flex: 0 0 270px
}

.research-content {
    padding: 0 20px 35px;
    flex-grow: 1;
    position: relative;
}

.research-content .data-tip {
    position: absolute;
    left: 20px;
    bottom: 0;
    font-size: 12px;
}

.filter-box {
    padding: 10px 20px 10px 0;
    border-right: 1px solid #d3d3d3
}

.filter-box .filter-heading {
    opacity: .6;
    font-weight: bold;
    font-size: 15px
}

.filter-list li a {
    padding-left: 20px;
    background-image: url("../images/icon-arrow-right.png");
    background-repeat: no-repeat;
    background-position: 3px center;
    display: flex;
    line-height: 30px;
    transition: all .2s;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--gray);
}

.filter-list li.active a {
    color: #626ed4;
    font-weight: bold
}

.filter-list li a:hover {
    background-position-x: 0
}

.filter-list li a span {
    background-color: #eeeff0;
    color: #5b626b;
    padding: 0 8px;
    line-height: 20px;
    border-radius: 10px
}

.filter-list li.active a span {
    background-color: rgba(98, 110, 212, 0.2);
    color: #626ed4
}

.filter-list li.active a {
    background-image: url("../images/icon-arrow-right-blue.png")
}

.attr-list {
    display: flex;
    margin: 0 -10px
}

.attr-list li {
    padding: 0 10px;
    color: #6d7988
}

.attr-list li:nth-child(1) {
    flex: 0 0 240px
}

.attr-list li:nth-child(2) {
    flex: 0 0 170px
}

.attr-list li:nth-child(3) {
    flex: 0 0 180px
}

.attr-list li:nth-child(4) {
    flex: 0 0 150px
}

.attr-list li:nth-child(5) {
    flex-grow: 1
}

.research-list-heading {
    text-align: center;
    padding: 5px 0;
    border-bottom: 1px solid #d3d3d3;
    background-color: #fff
}

.filter-box.position-fixed, .research-list-heading.position-fixed {
    top: 118px;
    z-index: 40
}

.research-list-heading.position-fixed + .research-item {
    margin-top: 32px
}

.research-list-heading li {
    opacity: .8
}

.research-item {
    padding: 12px 0;
    border-bottom: 1px solid #eeefff
}

.research-item-heading {
    display: flex;
}

.research-item-heading .s-num {
    padding: 0 5px;
    line-height: 20px;
    border-radius: 4px;
    background-color: #717dd9;
    color: #fff;
    text-align: center;
    display: inline-block;
    min-width: 24px;
    height: 21px
}

.research-item-heading .date {
    font-size: 13px;
    background-color: #e7e8e9;
    padding: 0 6px;
    line-height: 24px;
    text-align: center;
    height: 24px;
    border-radius: 3px;
    color: #7e838a
}

.research-item-heading .title {
    padding-right: 10px;
    display: inline-block;
    max-width: calc(100% - 90px);
    flex: 0 0 calc(100% - 90px);
}

.research-item-heading .title a {
    line-height: 24px;
    font-size: 15px;
    font-weight: bold;
    color: #626ed4
}

.research-item-heading a span {
    font-size: 12px;
    padding: 2px 4px 1px;
    background-color: #626ed4;
    color: #fff;
    border-radius: 2px
}

.research-item-content .author {
    opacity: 1;
    position: relative;
}

.research-item-content .author .author-org {
    position: absolute;
    display: block;
    background-color: #fff;
    width: 100%;
    padding: 5px 10px;
    border-radius: 6px;
    border: 1px solid #e5e5e5;
    box-shadow: -5px 5px 10px rgba(0, 0, 0, 0.1);
    margin-top: 15px;
    opacity: 0;
    visibility: hidden;
    transition: all .3s;
    z-index: 20;
    left: 0;
}

.research-item-content .author:hover .author-org {
    opacity: 1;
    margin-top: 5px;
    visibility: visible
}

.research-item-content .author .author-org:before, .research-item-content .author .author-org:after {
    position: absolute;
    content: '';
    display: block;
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent #e5e5e5 transparent;
    top: -12px;
    left: 10px
}

.research-item-content .author .author-org:after {
    top: -11px;
    border-bottom-color: #fff
}

.research-item-content .attr-list {
    align-items: center
}

.research-item-tag {
    background-color: #eee;
    border-radius: 14px;
}

.list-search .form-control {
    border-radius: 17px 0 0 17px;
    border-right: 0;
}

.list-search .input-group-text {
    background-color: #fff;
    border-left: 0;
    border-radius: 0 17px 17px 0;
    transition: all 0.3s;
}

.list-search .form-control:focus + .input-group-append .input-group-text {
    border-color: #626ed4;
}

.research-sort {
    position: relative;
}

.research-sort ul {
    position: absolute;
    background-color: #fff;
    z-index: 10;
    padding: 5px;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
    text-align: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    margin-top: 10px;
}

.research-sort:hover ul {
    opacity: 1;
    margin-top: 0;
    visibility: visible;
}

.research-sort:hover ul li {
    cursor: pointer;
    padding: 3px 0;
}

.research-item-attr {
    position: relative;
    padding-left: 75px;
    line-height: 22px;
    min-height: 22px;
    font-size: 13px;
}

.research-item-attr span {
    position: absolute;
    left: 0;
}

.research-item-heading .title .pdf {
    font-size: 17px;
    position: absolute;
    margin-left: 3px;
    margin-top: 2px;
}

.card-title {
    font-size: 16px;
    font-weight: bold;
    position: relative;
    color: #626ed4;
    padding-left: 10px;
    margin-bottom: 0;
    border-radius: 3px;
}

.card-title:before {
    position: absolute;
    content: '';
    display: block;
    left: 0px;
    top: 50%;
    width: 2px;
    height: 18px;
    margin-top: -10px;
    border-radius: 1px;
    background-color: #626ed4;
}

.detail-group {
    display: flex;
    margin-bottom: 6px;
    line-height: 20px;
}

.detail-group .detail-group-title {
    color: #90979e;
    flex: 0 0 120px;
    font-size: 15px;
}

.detail-group .detail-group-content {
    flex-grow: 1;
}

.list-group-item {
    font-size: 1rem;
    border-color: rgba(0, 0, 0, .05);
}

.home-news-list {

}

.home-news-list li {
    display: flex;
    margin: 0 -8px;
    padding: 6px 0;
}

.home-news-list li > [class^=news-] {
    padding: 0 8px;
}

.home-news-list li .news-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
}

.home-news-list li .news-title a {
    font-size: 1rem;
    color: var(--dark);
}

.home-news-list li .news-title a:hover {
    color: var(--primary);
}

.home-news-list li .news-links,
.home-news-list li .news-date {
    white-space: nowrap;
}

.home-news-list li .news-links a {
    background-color: var(--gray);
    color: #fff;
    border-radius: 3px;
    font-size: 13px;
    padding: 4px 6px;
}

.home-news-list li .news-links a.info {
    background-color: var(--info);
}

.home-news-list li .news-links a.success {
    background-color: var(--success);
}

.home-news-list li .news-date {
    color: var(--gray);
}

.time-line {
    position: relative;
}

.time-line:before {

}

.time-line li {
    display: flex;
    padding: 3px 0;
    position: relative;
}

.time-line:before,
.time-line li:before,
.time-line li:after {
    display: block;
    content: '';
    position: absolute;
}

.time-line:before {
    top: 0;
    left: 100px;
    width: 1px;
    height: 100%;
    background-color: #e5e5e5;
}

.time-line li:first-child:before {
    width: 1px;
    height: 9px;
    background-color: #fff;
    left: 100px;
    top: 0px;
}

.time-line li:last-child:before {
    width: 1px;
    height: calc(100% - 9px);
    background-color: #fff;
    left: 100px;
    bottom: 0px;
}

.time-line li:after {
    width: 9px;
    height: 9px;
    left: 96px;
    top: 9px;
    border-radius: 50%;
    background-color: #bbb;
}

.time-line li .date {
    flex: 0 0 120px;
    max-width: 120px;
}

.time-line li .note {
    flex-grow: 1;
}

.tools-box {
    display: block;
}

.tools-box .img {
    width: 100px;
    height: 100px;
    margin: 0 auto;
    text-align: center;
    line-height: 100px;
}

.tools-box .img img {
    max-width: 100%;
    max-height: 100%;
    opacity: 0.5;
}

.w-35 {
    flex: 0 0 35%;
    width: 35%;
}

.w-33 {
    flex: 0 0 33%;
    width: 33%;
}

.main-bg .h3 {
    color: #86ccc7
}

.main-bg .border-right {
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.main-bg .card {
    box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, .1);
}

/*.main-bg .card.statistics {
    background-color: rgba(255,255,255,.05);
}*/
.other-num,
.main-bg .text-warning,
.main-bg .text-light {
    opacity: .8;
}

.other-num {
}

.width-70 {
    flex: 0 0 70px;
    width: 70px;
}

.width-90 {
    flex: 0 0 90px;
    width: 90px;
}

.width-100 {
    flex: 0 0 100px;
    width: 100px;
}

.width-120 {
    flex: 0 0 120px;
    width: 120px;
}

.width-150 {
    flex: 0 0 150px;
    width: 150px;
}

.width-200 {
    flex: 0 0 200px;
    width: 200px;
}

.width-250 {
    flex: 0 0 250px;
    width: 250px;
}

.width-300 {
    flex: 0 0 300px;
    width: 300px;
}

.btn {
    cursor: pointer;
}

.btn-secondary.active {
    background-color: #007bff !important;
    border-color: #007bff !important;
}

.dropdown.download .dropdown-menu {
    min-width: inherit;
    margin-top: 2px;
}

.dropdown.download .dropdown-menu .dropdown-item {
    padding: 4px 10px;
}

.dropdown.download .dropdown-menu .dropdown-item i.fa {
    font-size: 12px;
    color: var(--gray);
    transition: all 0.2s;
    opacity: 0.8;
}

.dropdown.download .dropdown-menu .dropdown-item:hover i.fa {
    transform: translateX(-3px);
}

.virus-type {
    top: 100%;
    right: 0;
    position: absolute;
    display: block;
    padding: 8px 20px;
    background-color: #d3e5ff;
    color: #4a80ce;
    border-radius: 0 0 20px 20px;
    text-align: center;
    box-shadow: 3px 3px 3px rgba(0, 0, 0, .05);
    z-index: 10;
}

.virus-type small {
    border: 0;
    padding: 0;
    font-size: 12px;
    opacity: 0.7;
    line-height: 1.2;
}

table.dataTable {
    min-width: 100%;
}

.date-range .input-group-text {
    color: var(--gray);
    background-color: #fff;
    border-right: 0;
    border-left: 0;
}

.table-thead-list {
    padding: 10px 15px;
    width: 280px;
    margin-top: 5px;
}

.custom-control-label {
    cursor: pointer;
    line-height: 24px;
}

.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody > table > tbody > tr:not(:last-child) > td {
    border-bottom: 1px solid #e5e5e5;
}

.etree-box {
    border: 6px solid #ddd;
}

.etree-box .etree-tools {
    background-color: #fff;
    padding: 8px 15px;
    display: flex;
    border-bottom: 1px solid #ddd;
}

.etree-box .etree-tools .etree-btn {
    background: none;
    width: 36px;
    height: 36px;
    border: 2px solid var(--gray);
    text-align: center;
    line-height: 32px;
    color: var(--dark);
    border-radius: 50%;
    margin-right: 10px;
}

.etree-box .etree-tools .etree-btn:hover,
.etree-box .etree-tools .etree-btn.active {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.etree-box .etree-content {
    overflow: hidden;
    position: relative;
}

.etree-box .etree-content .etree {
    padding: 10px 15px;
    transition: all 0.3s;
}

.bg-gray {
    background-color: #eee;
}

.etree-box .etree-content .etree-table-left,
.etree-box .etree-content .etree-table-full {
    position: absolute;
    transition: all 0.3s;
    overflow-y: auto;
}

.etree-box .etree-content .etree-table-left {
    width: 450px;
    height: 100%;
    top: 0;
    left: -460px;
    z-index: 10;
    background-color: #fff;
    box-shadow: 3px 0 6px rgba(0, 0, 0, .05);
}

.etree-box .etree-content .etree-table-left.active {
    left: 0;
}

.etree-box .etree-content .etree-table-left.active + .etree {
    transform: translateX(450px);
}

.etree-box .etree-content .etree-table-full {
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    z-index: 11;
    background-color: #fff;
}

.etree-box .etree-content .etree-table-full.active {
    height: 100%;
}

.gene-box {
    display: block;
    text-align: left;
    line-height: 20px;
    word-break: break-all;
}

.tag-num {
    padding: 2px 5px;
    border-radius: 3px;
    background-color: #007bff;
    color: #fff;
    font-size: 12px;
}

.abb-box {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 160px;
}

.other-box {
    max-height: 80px;
    line-height: 20px;
    overflow-y: auto;
}

.select2-container--default .select2-selection--multiple {
    border-color: #ced4da;
}

.select2-dropdown,
.select2-container--default.select2-container--focus .select2-selection--multiple {
    border-color: #626ed4;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    font-size: 14px;
    white-space: normal;
    max-width: 100%;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    float: right;
    margin-right: 0;
    margin-left: 2px;
    color: #38adfa;
}

.select2-container--default .select2-results > .select2-results__options {
    font-size: 14px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #eaf6ff;
    border: 1px solid #bfe3ff;
    color: #007bff;
}

.ui-widget {
    font-size: 14px;
}

.ui-widget.ui-widget-content {
    border-radius: 4px;
    padding: 5px 0;
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
    background-color: #eaf6ff;
    border: 1px solid #bfe3ff;
    color: #007bff;
    font-weight: normal;
}

.custom-switch-lg {
    padding-left: 3rem;
}

.custom-switch-lg .custom-control-label:before {
    width: 2.5rem;
    height: 1.25rem;
    border-radius: 0.625rem;
    left: -3rem;
    top: 0.125rem;
}

.custom-switch-lg .custom-control-label:after {
    top: calc(.125rem + 3px);
    left: calc(-3rem + 3px);
    width: calc(1.25rem - 6px);
    height: calc(1.25rem - 6px);
}

.custom-switch-lg .custom-control-input:checked ~ .custom-control-label:after {
    -webkit-transform: translateX(1.25rem);
    transform: translateX(1.25rem);
}

.page-link {
    color: #626ed4;
}

.page-item.active .page-link {
    background-color: #626ed4;
    border-color: #626ed4;
    color: #fff;
}

.selected-data {
    color: #f33;
    background-color: #fffcfc;
}

.modal-header {
    padding: 0.6rem 1rem;
    background-color: #F8F8F8;
}

.modal-content {
    border-radius: 6px;
    border-width: 6px;
}

.modal-backdrop.show {
    opacity: .2;
}

.abb-boxword-break-all {
    word-break: break-all !important;
    text-align: left !important;
}

.etree-table-left .table td a,
.etree-table-left .table td {
    color: var(--gray);
}

.etree-table-left .table .selected-data td a,
.etree-table-left .table .selected-data td {
    color: #f33;
}

.modal-full {
    width: calc(100vw - 17px);
    height: 100%;
    max-width: calc(100vw - 17px);
    margin: 0;
}

.modal-full .modal-content {
    height: 100%;
}

.time-graph-legend {
    position: absolute;
    top: 0;
    left: 0;
}

.time-graph-legend .before,
.time-graph-legend .color-range {
    position: relative;
    color: var(--gray);
    line-height: 20px;
    padding: 0px 10px 0px 35px;
    margin-bottom: 10px;
    font-size: 14px;
}

.time-graph-legend .color-range:before,
.time-graph-legend .before:before {
    display: block;
    content: '';
    position: absolute;
    width: 25px;
    height: 100%;
    border-radius: 3px;
    top: 0;
    left: 0;
    background-color: #4C50C2;
}

.time-graph-legend .color-range {
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.time-graph-legend .color-range:before {
    background: linear-gradient(to bottom, #ffeded 0%, #ff2828 100%);
}

.select2-container .select2-selection--single {
    height: 35px;
    border-color: #ced4da;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 34px;
    color: var(--dark);
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 34px;
}

.select2-container--open .select2-selection--single {
    border-color: #626ed4;
}

.chart-filter {
    /*width: 400px;*/
    /*flex: 0 0 400px;*/
    display: flex;
    height: 240px;
    position: absolute;
    top: 0;
    right: 10px;
}

.chart-filter .num-range {
    padding: 0;
    position: relative;
    width: 20px;
    height: 100%;
}

.chart-filter .num-range input[type=range] {
    transform: rotate(90deg);
    position: absolute;
    top: 110px;
    width: 194px;
    left: -97px
}

.modal-tag,
.tooltip-tag {
    display: inline-block;
    padding: 2px 5px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    margin-bottom: 5px;
    font-size: 12px;
    margin-right: 5px;
}

.modal-tag {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--dark);
}

.collapse-title {
    display: block;
    position: relative;
    margin-bottom: 5px;
    /*font-weight: bold;*/
    color: var(--primary);
    font-size: 15px;
}

.collapse-title:before,
.collapse-title:after {
    position: absolute;
    display: block;
    top: 50%;
}

.collapse-title:before {
    content: '';
    width: 100%;
    height: 1px;
    background-color: #e5e5e5;
}

.collapse-title:after {
    content: "";
    width: 0;
    height: 0;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent #999;
    left: -10px;
    margin-top: -5px;
    opacity: 0;
    transition: transform 0.1s;
}

.collapse-title:hover:after {
    opacity: 1;
}

.collapse-title.collapsed:after {
    opacity: 1;
    transform: rotate(90deg);
    margin-top: -3px;
    left: -13px;
}

.collapse-title span {
    background-color: #fff;
    padding-right: 10px;
    position: relative;
}

.filter-tabs li {
    position: relative;
}

.filter-tabs li + li:before {
    display: block;
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    height: 18px;
    width: 1px;
    margin-top: -9px;
    background-color: #ddd;
}

.filter-tabs li a {
    display: block;
    padding: 4px 15px;
    color: var(--gray);
    font-size: 1rem;
    transition: all 0.2s;
}

.filter-tabs li a:hover,
.filter-tabs li.active a {
    color: var(--primary);
    font-weight: bold;
}

.search-graph {
    position: relative;
}

.search-graph .search-graph-content {
    /*width: 0px;*/
    transition: all 0.3s;
    white-space: nowrap;
    display: flex;
    overflow: hidden;
    align-items: center;
}

/*.search-graph:hover .search-graph-content {
    width: 380px;
}*/
.menu-box {
    display: flex;
    border-radius: 4px;
    background-color: #41599c;
    margin-right: 30px;
}

.menu-box span,
.menu-box a {
    display: block;
    line-height: 24px;
    padding: 7px 18px;
    color: rgba(255, 255, 255, 0.8);
}

.menu-box span {
    background-color: rgb(116, 153, 255);
    border-radius: 4px;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
}

.menu-box a {
    font-size: 14px;
    position: relative;
}

.menu-box a.active {
    color: #fff;
    font-weight: bold;
}

.menu-box a + a:before {
    display: block;
    content: '';
    position: absolute;
    width: 1px;
    height: 20px;
    left: 0;
    top: 50%;
    margin-top: -10px;
    background-color: rgba(255, 255, 255, 0.2);
}

/*2020-04-20*/
.region-tabs li {
    display: block;
    cursor: pointer;
    padding: 2px 6px;
    margin-bottom: 5px;
    margin-right: 5px;
    background-color: #f6f6f7;
    border-radius: 4px;
    transition: all 0.2s;
}

.region-tabs li.active {
    background-color: #626ed4;
    color: #fff;
    font-weight: bold;
}

.region-tabs li:hover {
    background-color: #626ed4;
    color: #fff;
}

.region-more span {
    display: block;
    cursor: pointer;
    padding: 2px 6px;
    white-space: nowrap;
}

.region-tabs li.region-hide {
    display: none;
}

.region-tabs li.region-hide.show {
    display: block;
}

label.btn {
    cursor: pointer;
}

.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active, .show > .btn-secondary.dropdown-toggle {
    background-color: #0069d9;
    border-color: #0069d9;
}

.btn-group > .btn-group:not(:first-child), .btn-group > .btn:not(:first-child) {
    margin-left: 1px;
}

label.btn {
    cursor: pointer;
}

.btn-group > .btn-group:not(:first-child), .btn-group > .btn:not(:first-child) {
    margin-left: -1px;
}

.language-switch .btn {
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: 14px;
    background-color: #eee;
    color: var(--gray);
    transition: all 0.2s;
    display: flex;
    align-items: center;
}

.language-switch .btn svg {
    margin-right: 3px;
    margin-top: -1px;
    margin-left: -1px;
    fill: var(--gray)
}

.language-switch .btn:hover,
.language-switch .btn.active {
    background-color: var(--primary);
    color: #fff;
}

.language-switch .btn:hover svg,
.language-switch .btn.active svg {
    fill: #fff;
}

header .topbar-main .logo-phone {
    display: none;
}

.virus-box-1 {
    background-color: #fff;
    display: block;
    position: absolute;
    top: 100%;
    left: 15px;
    width: calc(100% - 30px);
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 66;
    margin-top: -25px;
    font-size: 14px;
    opacity: 0;
    visibility: hidden;
    transition: 0.2s;
}

.open .virus-box-1 {
    margin-top: -45px;
    opacity: 1;
    visibility: visible;
}

.virus-num {
    position: relative;
}

.virus-num:before {
    position: absolute;
    display: block;
    content: '';
    width: 0;
    height: 0;
    left: 50%;
    transform: translateX(-50%);
    border-width: 8px;
    border-style: solid;
    border-color: transparent transparent #fff transparent;
    bottom: -27px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
}

.virus-num.active:before {
    bottom: -7px;
    opacity: 1;
    visibility: visible;
}

.virus-box-1 i.fa {
    cursor: pointer;
}

.legend-list {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    padding-bottom: 10px;
}

.legend-list li {
    position: relative;
    display: block;
    flex-grow: 1;
    cursor: pointer;
}

.legend-list li div {
    display: flex;
    align-items: center;
    justify-content: center;
}

.legend-list li div span {
    display: block;
    width: 18px;
    height: 18px;
    border: 2px solid #333;
    background-color: #fff;
    margin-right: 5px;
}

.legend-list .l-orf1ab span {
    background-color: #c5b033;
}

.legend-list .l-s span {
    background-color: #bf2d20;
}

.legend-list .l-orf3a span {
    background-color: #253066;
}

.legend-list .l-e span {
    background-color: #c01a5f;
}

.legend-list .l-m span {
    background-color: #7e191e;
}

.legend-list .l-orf6 span {
    background-color: #add488;
}

.legend-list .l-orf7a span {
    background-color: #95a8c0;
}

.legend-list .l-orf7b span {
    background-color: #77879a;
}

.legend-list .l-orf8 span {
    background-color: #379938;
}

.legend-list .l-n span {
    background-color: #32578c;
}

.legend-list .l-orf10 span {
    background-color: #f4f29b;
}

.legend-list li.active {
    color: var(--primary);
    font-weight: bold;
}

.legend-list li.active span {
    border-color: var(--primary);
}

.legend-list li.has-children.active:before,
.legend-list li.has-children.active:after {
    display: block;
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-width: 8px;
    border-style: solid;
    border-color: transparent transparent #e5e5e5 transparent;
    left: 50%;
    transform: translateX(-50%);
    bottom: -10px;
}

.legend-list li.has-children.active:after {
    border-bottom-color: #f5f5f5;
    bottom: -13px;
}

.legend-dropmenu {
    background-color: #f5f5f5;
    border: 2px solid #e5e5e5;
    border-radius: 6px;
    font-size: 12px;
    padding: 5px 8px;
    margin-bottom: 10px;
}

.legend-dropmenu a {
    color: var(--dark);
    line-height: 20px;
    display: block;
    position: relative;
    transition: all 0.2s;
}

.legend-dropmenu a:hover {
    color: var(--primary);
}

.legend-dropmenu a.active {
    font-weight: bold;
    color: var(--primary);
}

.legend-dropmenu a.active:before {
    display: block;
    content: "\f00c";
    position: absolute;
    right: 0;
    font: normal normal normal 12px/1 FontAwesome;
    top: 50%;
    transform: translateY(-50%);
}

.row-sm {
    margin-left: -8px;
    margin-right: -8px;
}

.row-sm [class^=col-] {
    padding-left: 8px;
    padding-right: 8px;
}

.coll-title {
    position: relative;
    display: block;
    font-size: 15px;
    font-weight: bold;
    color: var(--dark);
}

.coll-title i.fa {
    color: var(--primary);
}

.coll-title.collapsed i.fa-minus-square:before {
    content: "\f0fe";
}

.news-box {
    display: flex;
    position: relative;
    padding: 25px 30px;
    line-height: 1.8;
    background-color: #e7e8f5;
    border-radius: 6px;
    overflow: hidden;
    height: 250px;
    width: 100%;
    align-items: center;
}

.news-box:before,
.news-box:after {
    position: absolute;
    content: '';
    display: block;
    background-color: #fff;
}

.news-box:before {
    top: 12px;
    right: 12px;
    bottom: 12px;
    left: 12px
}

.news-box:after {
    width: 136%;
    height: 100%;
    transform: rotate(-28deg);
    top: 0%;
    left: -18%;
}

.news-box .position-relative {
    z-index: 2;
}

.radar-box {
    width: 219px;
    height: 219px;
    position: relative;
}

.swiper-box,
.news-timeline {
    max-width: calc(100% - 219px);
    flex: 0 0 calc(100% - 219px);
    padding-left: 10px;
    height: 250px;
    overflow-y: auto;
    position: relative;
}

.radar-box .radar-scanning,
.radar-box .radar-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.8;
}

.radar-box .radar-scanning img,
.radar-box .radar-bg img {
    width: 100%;
    height: 100%;
}

.radar-box .radar-scanning {
    z-index: 2;
    animation: rotate 4s linear infinite;
}

.radar-box .radar-pointer {
    position: relative;
}

.radar-box .radar-p {
    display: block;
    position: absolute;
    width: 8px;
    height: 8px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    background-color: #fff;
    top: 150px;
    left: 150px;
    animation: breathe 1.5s ease-out infinite;
}

.radar-box .radar-p:before {
    display: block;
    content: '';
    position: absolute;
    top: -11px;
    left: -11px;
    width: 30px;
    height: 30px;
    border: 7px solid #fff;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    animation: warn 1.5s ease-out infinite;
}

.radar-box .radar-p span {
    position: absolute;
    top: 100%;
    display: block;
    font-size: 12px;
    width: 60px;
    text-align: center;
    transform: translateX(-50%);
    left: 50%;
    color: #fff;
}

.nav-tabs-custom-2 {
    border-bottom: 2px solid #e5e5e5;
    margin-bottom: 10px;
}

.nav-tabs-custom-2 .nav-link {
    font-size: 16px;
    color: var(--dark);
    position: relative;
    overflow: hidden;
    margin-bottom: -2px;
}

.nav-tabs-custom-2 .nav-link:before,
.nav-tabs-custom-2 .nav-link:after {
    display: block;
    content: '';
    position: absolute;
    left: 50%;
    transition: all 0.2s;
}

.nav-tabs-custom-2 .nav-link:before {
    bottom: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary);
}

.nav-tabs-custom-2 .nav-link:after {
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent var(--primary) transparent;
    bottom: -6px;
    transform: translateX(-50%);
}

.nav-tabs-custom-2 .nav-link:hover:before,
.nav-tabs-custom-2 .nav-link.active:before {
    width: calc(100% - .5rem);
    transform: translateX(-50%);
}

.nav-tabs-custom-2 .nav-link:hover:after,
.nav-tabs-custom-2 .nav-link.active:after {
    bottom: 1px;
}

.nav-tabs-custom-2 .nav-link:hover,
.nav-tabs-custom-2 .nav-link.active {
    color: var(--primary);
}

.nav-tabs-custom-2 .nav-link.active {
    font-weight: bold;
}

@keyframes rotate {
    0% {
        transform: rotate(0);
    }
    50% {
        transform: rotate(180deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes warn {
    0% {
        transform: scale(0);
        opacity: 0.0;
    }
    25% {
        transform: scale(0);
        opacity: 0.1;
    }
    50% {
        transform: scale(0.2);
        opacity: 0.3;
    }
    75% {
        transform: scale(0.6);
        opacity: 0.5;
    }
    100% {
        transform: scale(1);
        opacity: 0.0;
    }
}

@keyframes breathe {
    0% {
        opacity: 1;
    }
    25% {
        opacity: 0.9;
    }
    50% {
        opacity: 0.7;
    }
    75% {
        opacity: 0.9;
    }
    100% {
        opacity: 1;
    }
}

.news-timeline ul {
    position: relative;
    min-height: 100%;
}

.swiper-box {
    position: relative;
}

.swiper-box:before,
.news-timeline ul:before {
    display: block;
    content: '';
    position: absolute;
    width: 3px;
    height: 100%;
    background-color: #e7e8f5;
    border-radius: 2px;
    top: 0;
    left: 10px
}

.swiper-box:before {
    left: 20px;
}

.swiper-box .swiper-slide,
.news-timeline li {
    padding-left: 30px;
    position: relative;
    padding-bottom: 5px;
}

.swiper-box .swiper-slide:before,
.news-timeline li:before {
    display: block;
    content: '';
    position: absolute;
    width: 15px;
    height: 15px;
    border: 3px solid #fff;
    border-radius: 50%;
    background-color: var(--primary);
    top: 11px;
    left: 4px;
}

.nt-box {
    border-radius: 6px;
    display: flex;
    padding: 0px 10px;
    background-color: #e7e8f5;
    position: relative;
    color: #1a4d85;
    height: 80px;
    flex-direction: column;
    justify-content: center;
}

.nt-box:before {
    display: block;
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-width: 7px 10px;
    border-style: solid;
    border-color: transparent #e7e8f5 transparent transparent;
    top: 12px;
    left: -20px;
}

.nt-box .nt-content {
    font-size: 12px;
    opacity: 0.8;
}

.mutations-news-title {
    color: #1a4d85;
    font-size: 15px;
    padding: 6px 12px 5px;
    border-left: 3px solid #1a4d85;
    background-color: #e8edf3;
    margin-bottom: 10px;
    display: inline-block;
}

.mutations-news-content {
    background-color: #dde5ed;
    position: relative;
    padding: 2px;
}

.mutations-news-content:before,
.mutations-news-content:after {
    display: block;
    content: '';
    position: absolute;
    background-color: #f3f6f9;
}

.mutations-news-content:before {
    top: 30px;
    bottom: 30px;
    left: 0px;
    right: 0px
}

.mutations-news-content:after {
    top: 0px;
    bottom: 0px;
    left: 30px;
    right: 30px
}

.mutations-chart {
    background-color: #f3f6f9;
    position: relative;
    z-index: 2;
}

.my_table {
    border: 1px solid #e5e5e5;
}

.swiper-box {
    height: 250px;
    overflow: hidden;
}

.swiper-box .swiper-slide {
    display: block;
    height: 80px;
}

.swiper-box .swiper-wrapper {
    flex-direction: column;
}

.swiper-box.swiper-container-vertical > .swiper-scrollbar {
    opacity: 0;
}

.news-box1 {
    border: 1px solid #ddd;
    background-color: #fdfeff;
}

.news-box1 .news-title {
    font-size: 24px;
    /*font-weight: bold;*/
    padding-top: 15px;
    color: #1a4d85;
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.news-box1 .news-content p {
    text-indent: 2em;
    text-align: justify;
    line-height: 1.5;
    font-family: 'FangSong', 'FangSong_GB2312', 'simsun';
    font-size: 16pt;
    margin-bottom: 10px;
}
.gene-filter {
    background-color: #f5f5f5;
    padding: 10px 0;
    border-radius: 6px;
    margin-bottom: 10px;
    border: 1px solid #e5e5e5;
}
.gene-filter .st0{fill:#808285;}
.gene-filter .st1{fill:#B33C29;}
.gene-filter .st2{fill:#221E1F;}
.gene-filter .st3{font-family:'MicrosoftYaHei';}
.gene-filter .st4{font-size:13px;}
.gene-filter .st5{font-size:10px;}
.gene-filter .st6{fill:#595757;}
.gene-filter .st7{font-size:10px;}
.gene-filter .st8{fill:#727171;}
.gene-filter .st9{fill:none;stroke:#000000;stroke-linecap:round;stroke-miterlimit:10;}
.gene-filter rect.st1,
.gene-filter text.stt {
    cursor: pointer;
    transition: all 0.2s;
}
.gene-filter rect.st1.active,
.gene-filter text.stt.active,
.gene-filter text.stt.active tspan,
.gene-filter rect.st1:hover,
.gene-filter text.stt:hover,
.gene-filter text.stt:hover tspan {
    fill: var(--orange);
}
.legend-box {
    max-height: 88px;
    overflow-y: auto;
    border-radius: 3px;
    background-color: #f5f5f5;
    padding: 0 3px;
}
.legend-item {
    font-size: 12px;
    display: flex;
    align-items: center;
    padding: 2px 0;
}
.legend-item i {
    width: 8px;
    height: 8px;
    background-color: #999;
    border-radius: 50%;
    display: block;
    margin-right: 3px;
}
@media (min-width: 576px) {
    header .navbar .collapse {
        display: block;
        width: 100%;
    }

    .navbar-coll {
        display: none;
    }

    header .navigation-menu a.active:before {
        display: none;
    }

    .virus-box {
        padding: 5px 10px;
        margin-bottom: 5px;
        background-color: #f3f3f3;
        border-radius: 6px;
    }

    .virus-box > ul li {
        padding: 1px 8px;
        color: var(--dark);
    }

    .virus-box > ul li span {
        opacity: 0.7;
        font-size: 13px;
    }

    .virus-box.virus-head > ul li {
        color: var(--gray);
    }

    .virus-box > ul li + li {
        border-left: 1px solid #e7e7e7;
    }

    .virus-box .attr {
        padding-top: 5px;
        padding-left: 0;
        font-size: 13px;
        color: var(--gray);
        margin-bottom: 0;
    }

    .virus-box .attr li {
        border-top: 1px dashed #e7e7e7;
        position: relative;
        line-height: 20px;
        min-height: 20px;
        padding-top: 5px;
        padding-bottom: 5px;
        padding-right: 8px;
    }

    .virus-box > ul li.v-id {
        font-weight: bold;
    }

    .virus-box .attr li div span {
        position: absolute;
        left: 8px;
        opacity: 0.8;
    }

    .virus-box .attr li div {
        position: relative;
        padding-left: 90px;
    }
}

@media (max-width: 575px) {
    .container-fluid {
        width: 100% !important;
    }

    header .topbar-main .logo {
        display: none;
    }

    header .topbar-main .logo-phone {
        display: block;
        flex-grow: 1;
    }

    header .topbar-main .logo-phone a {
        display: block;
        color: #fff;
        font-size: 24px;
        line-height: 34px;
        padding: 20px 0;
        text-align: center;
    }

    .main-content p {
        font-size: 1rem;
        line-height: 1.6;
    }

    .navbar-coll {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 25px;
    }

    .navbar-coll > span {
        font-size: 16px;
        opacity: 0.6;
        color: #fff;
    }

    .navbar-coll .navbar-toggler {
        color: rgba(255, 255, 255, .5);
        border-color: rgba(255, 255, 255, .1);
    }

    .navbar-coll i {
        font-size: 24px;
    }

    header .navigation-menu {
        display: block;
        max-height: none;
    }

    header .navbar {
        padding: 0;
    }

    header .navbar .container-fluid {
        padding-left: 0;
        padding-right: 0;
        display: block;
    }

    .menu-box {
        display: block;
        margin: 0;
        background: none;
        padding-left: 45px;
    }

    .menu-box a {
        font-size: 16px;
        padding-left: 0px;
        border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
    }

    .menu-box a:last-child {
        border-bottom: 0;
    }

    .menu-box a + a:before {
        display: none;
    }

    .menu-box span,
    header .navigation-menu > li > a {
        padding: 15px 25px;
    }

    .menu-box span {
        background: none;
        box-shadow: none;
        padding-left: 0;
        margin-left: -20px;
        opacity: 0.8;
        border-bottom: 1px dashed rgba(255, 255, 255, 0.2);
    }

    header .navigation-menu > li {
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    header .navigation-menu > li .dropdown-menu {
        opacity: 1;
        visibility: visible;
        position: relative;
        float: none;
        width: 100%;
        margin-top: 0;
        border-radius: 0;
        background: none;
        border: 0;
        box-shadow: none;
        padding: 0 0 0 20px;
        margin-left: 25px;
        border-top: 1px dashed rgba(255, 255, 255, 0.15);
    }

    header .navigation-menu > li .dropdown-menu:before {
        display: none;
    }

    header .navigation-menu > li .dropdown-menu li {
        border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
    }

    header .navigation-menu > li .dropdown-menu li:last-child {
        border-bottom: 0;
    }

    header .navigation-menu > li .dropdown-menu li a {
        color: #fff;
        line-height: 30px;
        font-size: 16px;
        display: block;
        text-align: left;
        opacity: 0.8;
        padding-left: 0;
    }

    header .navigation-menu a {
        position: relative;
    }

    header .navigation-menu a.active {
        opacity: 1;
        font-weight: bold;
    }

    header .navigation-menu a.active:before {
        display: block;
        font: normal normal normal 16px/1 FontAwesome;
        content: "\f00c";
        position: absolute;
        top: 50%;
        right: 25px;
        margin-top: -8px;
    }

    body {
        padding-bottom: 0;
    }

    .main-bg .card {
        position: relative;
    }

    .main-bg .card a.h3 {
        position: absolute;
        right: 10px;
        margin-bottom: 0;
        top: 50%;
        margin-top: -14px !important;
        font-size: 1.5rem;
    }

    .main-bg .card .text-center {
        text-align: left !important;
    }

    .main-bg .card h5 {
        margin: 0 !important;
    }

    .main-bg .card .other-num {
        display: block !important;
    }

    .main-bg .card .other-num .pl-4 {
        padding-left: 0 !important;
    }

    .main-box h1.text-center {
        text-align: left !important;
    }

    .main-bg .card .img {
        position: absolute;
        right: 20px;
        top: 50%;
        margin-bottom: 0 !important;
        height: 40px;
        line-height: 40px;
        margin-top: -20px;
        width: 40px;
    }

    .main-bg .card .card-body {
        padding: 1rem !important;
    }

    .main-bg .col-lg.mb-3 {
        margin-bottom: 0 !important;
    }

    .footer {
        padding: 10px 0;
    }

    .nav-tabs-custom {
        display: block!important;
    }

    .nav-tabs-custom li {
        margin-right: 0;
        margin-bottom: 10px;
        flex-wrap: wrap;
    }

    .nav-tabs-custom li > a {
        text-align: center;
    }

    .nav-tabs-custom li .mr-2 {
        margin-bottom: 5px;
        margin-right: 0 !important;
        flex: 0 0 50%;
        padding: 0 2px;
    }

    .nav-tabs-custom li .mr-2:nth-child(4) {
        flex: 0 0 100%;
    }

    .nav-tabs-custom li .mr-2:nth-child(4) .input-group {
        width: 100%;
    }

    .nav-tabs-custom li .mr-2:nth-child(6) {
        flex: auto;
        margin-bottom: 0;
    }

    .nav-tabs-custom li.active > a:before, .nav-tabs-custom li.active > a:after {
        display: none;
    }

    .nav-tabs-custom {
        border-bottom: 0;
        padding-bottom: 0;
        margin-bottom: 0;
    }
    .nav-tabs-custom .d-flex {
    }

    .nav-tabs-custom-1 a.nav-link {
        padding: .5rem;
    }

    .card .card-body .dataTables_wrapper + .d-flex {
        display: block !important;
    }
    .news-box {
        height: auto;
        margin-bottom: 15px;
    }
    .news-box:after {
        width: 150%;
        transform: rotate(-40deg);
        left: -23%;
    }
    .radar-box {
        display: none;
    }
    .swiper-box {
        max-width: 100%;
        flex: 0 0 100%;
        padding-left: 0;
    }
    .swiper-box:before {
        left: 10px;
    }
    table.dataTable {
        white-space: nowrap;
    }
}

.mb-1 .h3{
    color: var(--primary);
}
.card-tools .card .card-img-top{
    aspect-ratio: 16 / 9;
    object-fit: cover;
    position: relative;
}
.card-tools .card .card-title{
    border-radius:0;
    margin:0 0 5px;
    padding:0;
    text-align: center;
}
.card-tools .card .card-title::before{
    display:none;
}
.sel-ele .sel-result{
    margin-left:10px;
    width: calc(100% - 60px);
}