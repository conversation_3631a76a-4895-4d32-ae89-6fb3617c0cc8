spring:
  thymeleaf:
    cache: false
    mode: HTML
    encoding: utf-8
  datasource: #todo
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************
    username: ipac
    password: ipac
  jpa:
    database: mysql
    show-sql: false
  data:
    mongodb:
      database: ipac #todo
      host: ipac-mongo
      port: 27017
      username: ipac
      password: ipac
  rabbitmq:
    host: analysis-rabbitmq.analysis #todo
    port: 5672
    username: ipac
    password: ipac
    virtual-host: /ipac
    publisher-returns: true
    publisher-confirm-type: correlated
    template:
      mandatory: true
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 5
        max-concurrency: 10
logging:
  config: classpath:config/logback-prod.xml
app:
  appName: ipac
  dataHome: /usr/local/webdata
  analysisResultHome: /usr/local/webdata/analysis/result
baseUrl: https://www.biosino.org
remotes:
  file-url: http://file-service.analysis:8080
biosino:
  cas-base-url: https://www.biosino.org/node-cas
  regist_url: https://www.biosino.org/bmdcRegist/register
  forget_pwd_url: https://www.biosino.org/bmdcRegist/forgetPwd
  change_pwd_url: https://www.biosino.org/bmdcRegist/changePwdPage
  ftp-path: sftp://fms.biosino.org:44399
venas:
  script-parent-path: ${app.dataHome}/venas
security:
  cas:
    server:
      base-url: ${biosino.cas-base-url}
      protocol-version: 2
    service:
      resolution-mode: dynamic
    authorization:
      mode: NONE

